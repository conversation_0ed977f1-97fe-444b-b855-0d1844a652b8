name: Test and Deploy
on:
  push:
    branches: ["**"]    # All branch pushes
    paths:
      - "app/**"    # Trigger on any changes in app directory
  # pull_request:
  #   branches: ["**"]    # All branch PRs
  #   paths:
  #     - "app/**"    # Trigger on any changes in app directory

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        cd app && pip install -r requirements.txt

    - name: Install Flyctl and Dependencies
      run: |
        curl -L https://fly.io/install.sh | sh
        sudo apt-get update && sudo apt-get install -y expect netcat-openbsd redis-tools
        echo "$HOME/.fly/bin" >> $GITHUB_PATH
      env:
        FLYCTL_INSTALL: /home/<USER>/.fly

    - name: Run unit tests
      run: |
        chmod +x app/run_unit_tests.sh
        cd app && ./run_unit_tests.sh

    - name: Run integration tests
      run: |
        chmod +x app/run_integration_tests.sh
        cd app && ./run_integration_tests.sh
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        FLY_UPSTASH_REDIS_PASSWORD: ${{ secrets.FLY_UPSTASH_REDIS_PASSWORD }}

    - name: Run primitive validation tests
      run: |
        chmod +x app/run_primitive_validation.sh
        cd app && ./run_primitive_validation.sh
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        FLY_UPSTASH_REDIS_PASSWORD: ${{ secrets.FLY_UPSTASH_REDIS_PASSWORD }}

  deploy:
    name: Deploy to Fly.io
    needs: test  # Only run after tests pass
    if: github.ref == 'refs/heads/main'  # Only deploy on main branch
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - name: Delete existing machines
        run: |
          echo "Fetching existing machines..."
          machines=$(flyctl machines list -j -a invest-strategy-service)
          
          # Extract machine IDs and delete each one
          echo "$machines" | jq -r '.[].id' | while read -r machine_id; do
            if [ ! -z "$machine_id" ]; then
              echo "Deleting machine $machine_id..."
              flyctl machines destroy --force -a invest-strategy-service "$machine_id"
            fi
          done
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
          
      - name: Deploy app
        working-directory: ./app
        run: flyctl auth docker && flyctl deploy --remote-only --wait-timeout 300
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
