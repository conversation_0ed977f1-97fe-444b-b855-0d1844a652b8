name: Fly Auto Scaling
on:
  workflow_dispatch:
    inputs:
      instance_count:
        description: 'Number of instances to scale to (1-4)'
        required: true
        default: '1'
        type: string

jobs:
  scale:
    name: Auto Scale App
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: superfly/flyctl-actions/setup-flyctl@master
      
      - name: Get current hour in UTC+8
        id: time
        run: |
          hour=$(TZ='Asia/Shanghai' date +%H)
          echo "hour=$hour" >> $GITHUB_OUTPUT

      - name: Validate instance count
        if: github.event_name == 'workflow_dispatch'
        run: |
          count=${{ github.event.inputs.instance_count }}
          if ! [[ "$count" =~ ^[1-4]$ ]]; then
            echo "Error: Instance count must be between 1 and 4"
            exit 1
          fi

      - name: Scale based on manual input
        if: github.event_name == 'workflow_dispatch'
        working-directory: ./app
        run: flyctl scale count ${{ github.event.inputs.instance_count }} --yes
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Scale up (8:00-9:00 UTC+8)
        if: github.event_name == 'schedule' && steps.time.outputs.hour == '08'
        working-directory: ./app
        run: flyctl scale count 4 --yes
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Scale down (after 9:00 UTC+8)
        if: github.event_name == 'schedule' && steps.time.outputs.hour == '09'
        working-directory: ./app
        run: flyctl scale count 1 --yes
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
