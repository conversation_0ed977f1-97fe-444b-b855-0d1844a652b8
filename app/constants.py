import os
from dotenv import load_dotenv

load_dotenv()

ALPHAVANTAGE_API_TOKEN_PRO = os.getenv("ALPHAVANTAGE_API_TOKEN_PRO")
TUSHARE_API_TOKEN = os.getenv("TUSHARE_API_TOKEN")
S3_ENDPOINT_URL = os.getenv("S3_ENDPOINT_URL")
S3_PUBLIC_BUCKET_NAME = os.getenv("S3_PUBLIC_BUCKET_NAME")
S3_PUBLIC_PORTFOLIO_BASE_PATH = 'myinvestpilot/portfolios/'
S3_PRIVATE_BUCKET_NAME = os.getenv("S3_PRIVATE_BUCKET_NAME")
S3_PRIVATE_PORTFOLIO_BASE_PATH = 'portfolios/'
S3_ACCESS_KEY = os.getenv("S3_ACCESS_KEY")
S3_SECRET_ACCESS_KEY = os.getenv("S3_SECRET_ACCESS_KEY")
SLACK_BOT_TOKEN = os.getenv("SLACK_BOT_TOKEN")
SLACK_ADMIN_CHANNEL = os.getenv("SLACK_ADMIN_CHANNEL") or '#myinvestpilot-admin'
PORT = os.getenv("PORT") or 5000

FLY_UPSTASH_REDIS_HOST = os.getenv("FLY_UPSTASH_REDIS_HOST") or 'fly-i365dev-redis.upstash.io'
FLY_UPSTASH_REDIS_PORT = os.getenv("FLY_UPSTASH_REDIS_PORT") or 6379
FLY_UPSTASH_REDIS_PASSWORD = os.getenv("FLY_UPSTASH_REDIS_PASSWORD")

UPSTASH_REDIS_HOST = os.getenv("UPSTASH_REDIS_HOST")
UPSTASH_REDIS_PORT = int(os.getenv("UPSTASH_REDIS_PORT", "6379"))
UPSTASH_REDIS_PASSWORD = os.getenv("UPSTASH_REDIS_PASSWORD")

KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS") or 'caring-bat-12652-us1-kafka.upstash.io:9092'
KAFKA_TOPIC = os.getenv("KAFKA_TOPIC") or 'invest-strategy-service-ingestion-queue'
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID") or 'portfolio-update-production'
KAFKA_USERNAME = os.getenv("KAFKA_USERNAME")
KAFKA_PASSWORD = os.getenv("KAFKA_PASSWORD")

INVEST_OHLC_PROXY_HOST = os.getenv("INVEST_OHLC_PROXY_HOST")

TRADE_DATE_FORMAT_STR = "%Y-%m-%d"
PORTFOLIO_DB_BASE_DIR = 'data'