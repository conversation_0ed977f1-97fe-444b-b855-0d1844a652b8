
from datetime import datetime
import pytz

class TimeUtils:
    CHINA_TIMEZONE = pytz.timezone('Asia/Shanghai')
    
    @staticmethod
    def is_working_hours():
        """判断当前是否在工作时间（UTC+8 6:00-23:59）"""
        current_time = datetime.now(TimeUtils.CHINA_TIMEZONE)
        current_hour = current_time.hour
        return 6 <= current_hour <= 23

    @staticmethod
    def get_polling_interval():
        """根据时间返回轮询间隔（秒）"""
        if TimeUtils.is_working_hours():
            return 5  # 工作时间：5秒
        return 600  # 非工作时间：10分钟