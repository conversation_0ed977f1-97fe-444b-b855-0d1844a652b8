import logging
import sqlite3
import os

import pandas as pd

module_logger = logging.getLogger(__name__)

def ensure_dir_exists(db_name):
    base_dir = os.path.dirname(db_name)
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
        module_logger.info(f"Created directory: {base_dir}")

def persist_dataframe_to_sqlite(df, db_name, table_name, columns=None):
    conn = sqlite3.connect(db_name)
    try:
        if df is None or df.empty:
            if columns:
                create_empty_table(conn, table_name, columns)
            else:
                module_logger.warning(f"Cannot create empty table for {table_name} without column definitions")
            return

        # 确保 'date' 列存在
        if df.index.name == 'date':
            df = df.reset_index()
        elif 'date' not in df.columns:
            module_logger.warning(f"'date' column not found in {table_name}, using index as date")
            df['date'] = df.index

        # 将 'date' 转换为 datetime 类型，然后格式化为 YYYY-MM-DD
        df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')

        # 如果指定了列，确保包含 'date' 列
        if columns:
            if 'date' not in columns:
                columns = ['date'] + columns
            # 确保所有必要的列都存在
            for col in columns:
                if col not in df.columns:
                    df[col] = None
            df = df[columns]

        # 使用 'date' 列作为索引，但保留它作为一个列
        df.set_index('date', inplace=True)
        df.to_sql(table_name, conn, if_exists='replace', index=True)
        
        conn.commit()
        module_logger.info(f"Successfully persisted {table_name} to SQLite database: {db_name}")
    except Exception as e:
        module_logger.error(f"Error persisting {table_name} to SQLite: {str(e)}")
        raise
    finally:
        conn.close()

def execute_query(db_name, query, params=()):
    ensure_dir_exists(db_name)
    with sqlite3.connect(db_name) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(query, params)
        result = cursor.fetchall()
    return result

def execute_query_to_dict(db_name, query, params=()):
    ensure_dir_exists(db_name)
    with sqlite3.connect(db_name) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        cursor.execute(query, params)
        rows = cursor.fetchall()
        rows_as_dict = [dict(row) for row in rows]
    return rows_as_dict

def create_empty_table(conn, table_name, columns):
    cursor = conn.cursor()
    columns_def = ', '.join([f'"{col}" TEXT' for col in columns])
    cursor.execute(f'CREATE TABLE IF NOT EXISTS "{table_name}" ({columns_def})')
