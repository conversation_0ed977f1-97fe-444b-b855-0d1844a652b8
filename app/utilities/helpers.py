from datetime import date, datetime, timedelta
from typing import Any
from pandas import Timestamp
import backtrader as bt
import pystache
import logging

from constants import TRADE_DATE_FORMAT_STR

module_logger = logging.getLogger(__name__)

def get_today():
    return datetime.today()

def get_today_str():
    return get_today().strftime(TRADE_DATE_FORMAT_STR)

def get_yesterday_str():
    return (get_today() - timedelta(days=1)).strftime(TRADE_DATE_FORMAT_STR)

def get_n_days_ago(days):
    return (get_today() - timedelta(days=days))

def get_n_days_ago_str(days):
    return get_n_days_ago(days).strftime(TRADE_DATE_FORMAT_STR)

def get_days_between(start_date, end_date):
    start_date = datetime.strptime(start_date, TRADE_DATE_FORMAT_STR)
    end_date = datetime.strptime(end_date, TRADE_DATE_FORMAT_STR)
    return (end_date - start_date).days

def get_date_from_str(date_str):
    return datetime.strptime(date_str, TRADE_DATE_FORMAT_STR).date()

def is_china_stock(symbol):
    if len(symbol) != 6:
        return False
    if symbol[0] not in ['0', '3', '6', '1', '5']:
        return False
    return True

def get_n_days_ago_date(days):
    return get_n_days_ago(days).date()

def calculate_atr(stock_data, period=66):
    """
    Calculates the Average True Range (ATR) using the True Range (TR) and a rolling average.

    Parameters:
    stock_data (pd.DataFrame): A pandas DataFrame containing the high, low, and close prices of a security.
    period (int): The number of periods to use in the rolling average.

    Returns:
    pd.DataFrame: A pandas DataFrame containing the original data and the ATR values.
    """
    # Create a copy of the input DataFrame
    df = stock_data.copy()

    # Calculate the True Range (TR)
    df.loc[:, 'tr1'] = abs(df['High'] - df['Low'])
    df.loc[:, 'tr2'] = abs(df['High'] - df['Close'].shift())
    df.loc[:, 'tr3'] = abs(df['Low'] - df['Close'].shift())
    df.loc[:, 'tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

    # Calculate the rolling average of the True Range (TR)
    atr = df['tr'].rolling(period).mean()

    # Add the ATR as a new column to the DataFrame
    df.loc[:, 'atr'] = atr

    # Drop intermediate columns
    df = df.drop(['tr1', 'tr2', 'tr3', 'tr'], axis=1)

    return df

def calculate_chandelier_stop(stock_data, period=66, multiplier=4.4):
    """
    Calculates the Chandelier Stop using the highest high over a given period and a multiple of the ATR.

    Parameters:
    stock_data (pd.DataFrame): A pandas DataFrame containing the high, low, close, and ATR columns of a security.
    period (int): The number of periods to use in calculating the highest high.
    multiplier (int): The multiple of the ATR to subtract from the highest high.

    Returns:
    pd.DataFrame: A pandas DataFrame containing the original data and the Chandelier Stop values.
    """
    df = stock_data.copy()

    df.loc[:, 'highest_high'] = df['High'].rolling(period).max()

    df = calculate_atr(df, period)

    chandelier_stop = df['highest_high'] - (multiplier * df['atr'])
    df.loc[:, 'chandelier_stop'] = chandelier_stop.apply(lambda x: round(x, 3))

    return df

def calculate_ma(stock_data, period=250):
    """
    Calculates the moving average (MA) over a given period.

    Parameters:
    stock_data (pd.DataFrame): A pandas DataFrame containing the close prices of a security.
    period (int): The number of periods to use in calculating the moving average.

    Returns:
    pd.DataFrame: A pandas DataFrame containing the original data and the moving average values.
    """
    df = stock_data.copy()

    ma = df['Close'].rolling(period).mean()
    df.loc[:, 'ma'] = ma

    return df

def convert_to_dict(input_ordered_dict):
        """
        递归转换OrderedDict和AutoOrderedDict为标准字典。
        """
        if isinstance(input_ordered_dict, (dict, bt.AutoOrderedDict)):
            return {key: convert_to_dict(value) for key, value in input_ordered_dict.items()}
        elif isinstance(input_ordered_dict, list):
            return [convert_to_dict(item) for item in input_ordered_dict]
        else:
            return input_ordered_dict

def is_cryptocurrency(symbol):
    # 更新后的主流加密货币列表
    mainstream_cryptocurrencies = [
        'BTC', 'ETH', 'XRP', 'LTC', 'BCH', 'ADA', 'DOT', 
        'BNB', 'LINK', 'XLM', 'SOL', 'DOGE', 'TRX', 
        'AVAX', 'UNI', 'TIA', 'WLD'
    ]
    
    return symbol in mainstream_cryptocurrencies

def json_serial(obj):
    if isinstance(obj, (datetime, Timestamp)):
        return obj.isoformat()
    elif isinstance(obj, date):
        return obj.isoformat()
    raise TypeError ("Type %s not serializable" % type(obj))

def render_html(signal_data, template_path, language="en"):
        template_file = f"{template_path}/{language}/template.mustache"
        with open(template_file, 'r', encoding='utf-8') as f:
            template_content = f.read()

        renderer = pystache.Renderer()
        email_html = renderer.render(template_content, signal_data)
        return email_html

def float_precision_handler(obj, precision=3):
    """
    Recursively process an object and convert all float values to a fixed precision format.
    """
    if isinstance(obj, float):
        return round(obj, precision)
    elif isinstance(obj, dict):
        for key, value in obj.items():
            obj[key] = float_precision_handler(value)
    elif isinstance(obj, list):
        obj = [float_precision_handler(item) for item in obj]
    return obj

def convert_keys_to_strings(obj):
    """
    Recursively convert all keys in the object to strings.
    """
    if isinstance(obj, dict):
        return {str(key): convert_keys_to_strings(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_keys_to_strings(element) for element in obj]
    elif isinstance(obj, (datetime, Timestamp)):
        return obj.isoformat()
    return obj

def safe_format(value, format_str):
    if value is None:
        return "N/A"
    try:
        return format_str.format(value)
    except (ValueError, TypeError):
        return "Error"

def xnpv(rate, cashflows):
    """
    Calculate NPV (Net Present Value) for a series of cashflows at irregular intervals.
    
    Args:
        rate: The discount rate to use for the calculation
        cashflows: List of tuples, each containing (date, amount)
    """
    total = 0.0
    start_date = cashflows[0][0]
    
    for date, cf in cashflows:
        # 确保所有日期都被转换为date类型
        if isinstance(date, datetime):
            date = date.date()
        if isinstance(start_date, datetime):
            start_date = start_date.date()
            
        if date == start_date:
            total += cf
        else:
            days = (date - start_date).days
            total += cf / pow(1.0 + rate, days/365.25)
    return total

def calculate_xirr(dates, amounts, guess=0.1):
    """Calculate IRR for irregular cashflows
    
    Args:
        dates: List of dates
        amounts: List of corresponding amounts
        guess: Initial guess for the IRR calculation
    """
    if not dates or not amounts or len(dates) != len(amounts):
        return None
    
    module_logger.info("Starting XIRR calculation...")
    module_logger.info(f"Number of cashflows: {len(dates)}")
    
    try:
        # 将日期和金额组合成元组列表
        cashflows = list(zip(dates, amounts))
        cashflows.sort(key=lambda x: x[0])  # 按日期排序
        
        rate = guess
        for iteration in range(100):
            value = xnpv(rate, cashflows)
            
            if abs(value) < 0.00001:
                # Convert complex to real if needed
                if isinstance(rate, complex):
                    module_logger.warning(f"Complex result detected: {rate}, using real part only")
                    return float(rate.real)
                return float(rate)
                
            delta = rate * 0.0001
            derivative = (xnpv(rate + delta, cashflows) - value) / delta
            
            if abs(derivative) < 0.00001:
                module_logger.warning("Derivative too small")
                return None
                
            new_rate = rate - value / derivative
            # Convert complex to real if needed
            if isinstance(new_rate, complex):
                module_logger.warning(f"Complex new_rate detected: {new_rate}, using real part only")
                new_rate = new_rate.real
                
            if abs(new_rate - rate) < 0.00001:
                return float(new_rate)
                
            rate = new_rate
            
        module_logger.warning("Did not converge")
        # Convert complex to real if needed
        if isinstance(rate, complex):
            module_logger.warning(f"Complex result detected: {rate}, using real part only")
            return float(rate.real)
        return float(rate)
    except Exception as e:
        module_logger.error(f"Error in calculate_xirr: {str(e)}")
        return None

def round_floats(obj: Any, decimal_places: int = 4) -> Any:
    """
    Recursively rounds all floating point numbers in a data structure to the specified decimal places.
    
    Works with:
    - Individual float values
    - Lists and tuples containing floats
    - Dictionaries with float values
    - Nested combinations of the above
    
    Args:
        obj: The object to process
        decimal_places: Number of decimal places to round to (default 4)
    
    Returns:
        The same object with all floats rounded to the specified decimal places
    """
    if isinstance(obj, float):
        return round(obj, decimal_places)
    elif isinstance(obj, dict):
        return {k: round_floats(v, decimal_places) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return type(obj)(round_floats(x, decimal_places) for x in obj)
    return obj


def format_financial_value(value: float, decimal_places: int = 2) -> str:
    """
    Format a financial value with commas as thousands separators and fixed decimal places.
    
    Args:
        value: The financial value to format
        decimal_places: Number of decimal places to show (default 2)
    
    Returns:
        Formatted string representation of the value
    """
    return f"{value:,.{decimal_places}f}"
