import time
import random

class ExponentialBackoff:
    def __init__(self, base_delay=1.0, max_delay=60.0, factor=2.0, jitter=False):
        """
        初始化退避策略参数。
        :param base_delay: 基础延迟时间，单位为秒。
        :param max_delay: 最大延迟时间，单位为秒。
        :param factor: 延迟时间增长因子。
        :param jitter: 是否应用抖动，以添加随机性到延迟时间。
        """
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.factor = factor
        self.jitter = jitter
        self.attempts = 0

    def wait(self):
        """
        计算当前应等待的时间，并暂停执行。
        """
        delay = min(self.max_delay, self.base_delay * (self.factor ** self.attempts))
        if self.jitter:
            delay = random.uniform(self.base_delay, delay)
        time.sleep(delay)
        self.attempts += 1

    def reset(self):
        """
        重置重试次数。
        """
        self.attempts = 0
