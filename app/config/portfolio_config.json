{"portfolios": [{"name": "美股1号", "code": "myinvestpilot_us_1", "description": "基于特定止损与均线策略的美股杠杆ETF组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "FNGU", "name": "FANG+ Index 3x Bull"}, {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"}, {"symbol": "TECL", "name": "S&P Technology 3x Bull"}, {"symbol": "SOXL", "name": "Semiconductor 3x Bull"}, {"symbol": "TNA", "name": "Russell 2000 3x Bull"}, {"symbol": "CONL", "name": "COIN 2x Bull"}, {"symbol": "LABU", "name": "S&P Biotech 3x Bull"}, {"symbol": "FAS", "name": "Financial Sector 3x Bull"}, {"symbol": "EDC", "name": "Emerging Markets 3x Bull"}, {"symbol": "TMF", "name": "Long-term Treasury 3x Bull"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股1号A", "code": "myinvestpilot_us_1_a", "description": "基于特定止损与均线策略的美股杠杆ETF组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 30}}, "symbols": [{"symbol": "FNGU", "name": "FANG+ Index 3x Bull"}, {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"}, {"symbol": "SOXL", "name": "Semiconductor 3x Bull"}, {"symbol": "CONL", "name": "COIN 2x Bull"}], "start_date": "2015-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股2号", "code": "myinvestpilot_us_2", "description": "基于特定止损与均线策略的美股精选ETF组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 45, "atr_multiplier": 3, "n_ma": 120}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 15}}, "symbols": [{"symbol": "QQQ", "name": "Nasdaq ETF"}, {"symbol": "XLK", "name": "Technology Sector ETF"}, {"symbol": "XLY", "name": "Consumer Discretionary Sector Index ETF"}, {"symbol": "VIG", "name": "US Dividend Appreciation ETF"}, {"symbol": "ARKK", "name": "Innovation ETF"}, {"symbol": "ICLN", "name": "Global Clean Energy"}, {"symbol": "FTEC", "name": "Information Technology Index"}, {"symbol": "ESGU", "name": "US ESG Optimized Index"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股3号", "code": "myinvestpilot_us_3", "description": "基于特定止损与均线策略的美股纳指三倍做多ETF组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 99}}, "symbols": [{"symbol": "TQQQ", "name": "Nasdaq 3x Bull"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股3号A", "code": "myinvestpilot_us_3_a", "description": "基于特定止损与均线策略的美股纳指三倍做多ETF组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 68, "atr_multiplier": 4, "n_ma": 28}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}, "symbols": [{"symbol": "TQQQ", "name": "Nasdaq 3x Bull"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股定投1号", "code": "myinvestpilot_us_dip_1", "description": "基于买入持有定投策略的美股行业ETF组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 100000, "investment_amount": 10000, "investment_frequency": "y", "percents": 10, "fund_val_start": 100.0}}, "symbols": [{"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "XBI", "name": "S&P Biotech ETF"}, {"symbol": "IHI", "name": "US Medical Devices ETF"}, {"symbol": "SCHD", "name": "US Dividend Equity ETF"}, {"symbol": "IJR", "name": "S&P Small-Cap ETF"}, {"symbol": "XLP", "name": "Consumer Staples ETF"}, {"symbol": "XLY", "name": "Consumer Discretionary Sector Index ETF"}, {"symbol": "XLE", "name": "Energy Sector ETF"}, {"symbol": "XLI", "name": "Industrial Sector ETF"}, {"symbol": "XLF", "name": "Financial Sector ETF"}], "start_date": "2015-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股定投2号", "code": "myinvestpilot_us_dip_2", "description": "基于买入持有月度定投策略的 QQQ ETF组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 100000, "investment_amount": 1000, "investment_frequency": "m", "percents": 95, "fund_val_start": 100.0}}, "symbols": [{"symbol": "QQQ", "name": "Nasdaq ETF"}], "start_date": "2015-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股全球", "code": "myinvestpilot_us_global", "description": "基于双均线策略的美股全球ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "QQQ", "name": "Nasdaq ETF"}, {"symbol": "CHAU", "name": "CSI 300 2x Bull"}, {"symbol": "INDL", "name": "India 2x Bull"}, {"symbol": "EWJ", "name": "Japan Index"}, {"symbol": "EWG", "name": "Germany Index"}, {"symbol": "GBTC", "name": "Grayscale Bitcoin Trust"}, {"symbol": "GLD", "name": "Gold ETF"}, {"symbol": "GUNR", "name": "Global Upstream Natural Resources"}, {"symbol": "PDBC", "name": "Optimum Yield Diversified Commodity"}, {"symbol": "ICLN", "name": "Global Clean Energy"}, {"symbol": "XLE", "name": "Energy Sector ETF"}, {"symbol": "LTL", "name": "Telecom 2x Bull"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "A股1号", "code": "myinvestpilot_cn_1", "description": "基于双均线策略的A股精选ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "159928", "name": "消费ETF"}, {"symbol": "159929", "name": "医药ETF"}, {"symbol": "510500", "name": "中证500ETF"}, {"symbol": "159915", "name": "创业板ETF"}, {"symbol": "159939", "name": "信息技术ETF"}, {"symbol": "512100", "name": "中证1000ETF"}, {"symbol": "512660", "name": "军工ETF"}, {"symbol": "159941", "name": "纳指ETF"}, {"symbol": "515180", "name": "红利ETF易方达"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股2号", "code": "myinvestpilot_cn_2", "description": "基于双均线策略的A股创业板ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 20, "long_window": 26}}, "capital_strategy": {"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}, "symbols": [{"symbol": "159915", "name": "创业板ETF"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股3号", "code": "myinvestpilot_cn_3", "description": "基于双均线策略的A股ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 10}}, "symbols": [{"symbol": "159805", "name": "传媒ETF"}, {"symbol": "159869", "name": "游戏ETF"}, {"symbol": "159901", "name": "深圳100ETF"}, {"symbol": "159902", "name": "中小100ETF"}, {"symbol": "159915", "name": "创业板ETF"}, {"symbol": "159920", "name": "恒生ETF"}, {"symbol": "159928", "name": "消费ETF"}, {"symbol": "159929", "name": "医药ETF"}, {"symbol": "159930", "name": "能源ETF"}, {"symbol": "159931", "name": "金融ETF"}, {"symbol": "159936", "name": "可选消费ETF"}, {"symbol": "159938", "name": "医药卫生ETF"}, {"symbol": "159939", "name": "信息技术ETF"}, {"symbol": "159941", "name": "纳指ETF"}, {"symbol": "159949", "name": "创业板50ETF"}, {"symbol": "159995", "name": "芯片ETF"}, {"symbol": "160416", "name": "华安标普全球石油指数"}, {"symbol": "161039", "name": "1000增强LOF"}, {"symbol": "161907", "name": "万家中证红利LOF"}, {"symbol": "162411", "name": "华宝油气LOF"}, {"symbol": "164906", "name": "中概互联网LOF"}, {"symbol": "501018", "name": "南方原油LOF"}, {"symbol": "501029", "name": "红利基金LOF"}, {"symbol": "501050", "name": "50AH LOF"}, {"symbol": "502010", "name": "证券LOF"}, {"symbol": "510170", "name": "大宗商品ETF"}, {"symbol": "510180", "name": "上证180ETF"}, {"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "510500", "name": "中证500ETF"}, {"symbol": "510660", "name": "医药卫生ETF"}, {"symbol": "510880", "name": "红利ETF"}, {"symbol": "510900", "name": "H股ETF"}, {"symbol": "511220", "name": "城投债ETF"}, {"symbol": "512010", "name": "医药ETF"}, {"symbol": "512070", "name": "证券保险ETF"}, {"symbol": "512100", "name": "中证1000ETF"}, {"symbol": "512200", "name": "房地产ETF"}, {"symbol": "512580", "name": "环保ETF"}, {"symbol": "512660", "name": "军工ETF"}, {"symbol": "512800", "name": "银行ETF"}, {"symbol": "512880", "name": "证券ETF"}, {"symbol": "513030", "name": "德国ETF"}, {"symbol": "513050", "name": "中概互联网ETF"}, {"symbol": "513500", "name": "标普500ETF"}, {"symbol": "513520", "name": "日经ETF"}, {"symbol": "515070", "name": "人工智能ETF"}, {"symbol": "515180", "name": "红利ETF易方达"}, {"symbol": "515450", "name": "红利低波50ETF"}, {"symbol": "515880", "name": "通信ETF"}, {"symbol": "516510", "name": "云计算ETF"}, {"symbol": "516520", "name": "智能驾驶ETF"}, {"symbol": "517090", "name": "央企共赢ETF"}, {"symbol": "517180", "name": "中国国企ETF"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股全球", "code": "myinvestpilot_cn_global", "description": "基于双均线策略的A股全球ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 12, "long_window": 24}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "501018", "name": "南方原油LOF"}, {"symbol": "510170", "name": "大宗商品ETF"}, {"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "159915", "name": "创业板ETF"}, {"symbol": "513500", "name": "标普500ETF"}, {"symbol": "513030", "name": "德国ETF"}, {"symbol": "159920", "name": "恒生ETF"}, {"symbol": "159941", "name": "纳指ETF"}, {"symbol": "513520", "name": "日经ETF"}, {"symbol": "513080", "name": "法国CAC40ETF"}, {"symbol": "513380", "name": "恒生科技ETF龙头"}, {"symbol": "513730", "name": "东南亚科技ETF"}, {"symbol": "164824", "name": "印度基金LOF"}, {"symbol": "159329", "name": "沙特ETF"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股全球A", "code": "myinvestpilot_cn_global_old", "description": "基于双均线策略的A股全球ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 12, "long_window": 24}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "501018", "name": "南方原油LOF"}, {"symbol": "510170", "name": "大宗商品ETF"}, {"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "159915", "name": "创业板ETF"}, {"symbol": "513500", "name": "标普500ETF"}, {"symbol": "513030", "name": "德国ETF"}, {"symbol": "159920", "name": "恒生ETF"}, {"symbol": "159941", "name": "纳指ETF"}, {"symbol": "513520", "name": "日经ETF"}, {"symbol": "513080", "name": "法国CAC40ETF"}, {"symbol": "159509", "name": "纳指科技ETF"}, {"symbol": "513380", "name": "恒生科技ETF龙头"}, {"symbol": "513730", "name": "东南亚科技ETF"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股QQQ号", "code": "myinvestpilot_cn_qqq", "description": "基于双均线策略的A股纳指ETF组合", "strategy": {"name": "DualMovingAverageStrategy", "params": {"short_window": 10, "long_window": 25}}, "capital_strategy": {"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}, "symbols": [{"symbol": "159941", "name": "纳指ETF"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "A股定投1号", "code": "myinvestpilot_cn_dip_1", "description": "基于买入持有年度定投策略的沪深300ETF组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 100000, "investment_amount": 12000, "investment_frequency": "y", "percents": 95, "fund_val_start": 100.0}}, "symbols": [{"symbol": "510300", "name": "沪深300ETF"}], "start_date": "2015-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "15:00"}, {"name": "A股定投2号", "code": "myinvestpilot_cn_dip_2", "description": "基于买入持有月度定投策略的沪深300ETF组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 100000, "investment_amount": 1000, "investment_frequency": "m", "percents": 95, "fund_val_start": 100.0}}, "symbols": [{"symbol": "510300", "name": "沪深300ETF"}], "start_date": "2015-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "15:00"}, {"name": "加密币1号", "code": "myinvestpilot_cc_1", "description": "基于特定止损与均线策略的加密币组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}, "capital_strategy": {"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}, "symbols": [{"symbol": "BTC", "name": "Bitcoin"}, {"symbol": "ETH", "name": "Ethereum"}, {"symbol": "BNB", "name": "Binance Coin"}, {"symbol": "SOL", "name": "Solana"}, {"symbol": "XRP", "name": "<PERSON><PERSON><PERSON>"}, {"symbol": "DOGE", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"symbol": "ADA", "name": "Cardano"}, {"symbol": "TRX", "name": "TRON"}, {"symbol": "AVAX", "name": "Avalanche"}, {"symbol": "LINK", "name": "Chainlink"}], "start_date": "2018-01-01", "currency": "USDT", "market": "Crypto", "commission": 0.001, "update_time": "04:00"}, {"name": "加密币2号", "code": "myinvestpilot_cc_2", "description": "基于特定止损与均线策略的BTC组合", "strategy": {"name": "ChandelierExitMAStrategy", "params": {"n_atr": 30, "atr_multiplier": 2, "n_ma": 40}}, "capital_strategy": {"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 99}}, "symbols": [{"symbol": "BTC", "name": "Bitcoin"}], "start_date": "2018-01-01", "currency": "USDT", "market": "Crypto", "commission": 0.001, "update_time": "04:00"}, {"name": "加密币定投1号", "code": "myinvestpilot_cc_dip_1", "description": "基于买入持有年度定投策略的比特币组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 10000000, "investment_amount": 2400000, "investment_frequency": "y", "percents": 95, "fund_val_start": 100.0}}, "symbols": [{"symbol": "BTC", "name": "Bitcoin"}], "start_date": "2018-01-01", "currency": "USD", "market": "Crypto", "commission": 0.001, "update_time": "01:00"}, {"name": "加密币定投2号", "code": "myinvestpilot_cc_dip_2", "description": "基于买入持有月度定投策略的比特币组合", "strategy": {"name": "BuyHoldStrategy"}, "capital_strategy": {"name": "FixedInvestmentStrategy", "params": {"initial_capital": 10000000, "investment_amount": 200000, "investment_frequency": "m", "percents": 95, "fund_val_start": 100.0}}, "symbols": [{"symbol": "BTC", "name": "Bitcoin"}], "start_date": "2018-01-01", "currency": "USD", "market": "Crypto", "commission": 0.001, "update_time": "01:00"}]}