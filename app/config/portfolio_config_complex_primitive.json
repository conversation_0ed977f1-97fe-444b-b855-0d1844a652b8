{"portfolios": [{"name": "市场指标过滤策略组合", "code": "myinvestpilot_market_filtered", "description": "使用VIX市场波动指标过滤高波动性环境下的交易，仅在市场相对稳定时进行交易的组合策略", "strategy_definition": {"market_indicators": {"indicators": [{"code": "VIX"}], "transformers": [{"name": "vix_raw", "type": "IdentityTransformer", "params": {"indicator": "VIX", "field": "Close"}}, {"name": "vix_percentile", "type": "PercentileRankTransformer", "params": {"indicator": "VIX", "lookback": 252, "field": "Close"}}, {"name": "vix_ma", "type": "MovingAverageTransformer", "params": {"indicator": "VIX", "window": 20, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "ma_indicator", "type": "SMA", "params": {"period": 250, "column": "Close"}}, {"id": "atr_indicator", "type": "ATR", "params": {"period": 60}}, {"id": "chandelier_exit_indicator", "type": "ChandelierExit", "params": {"period": 60, "multiplier": 4.0}}, {"id": "constant_75", "type": "Constant", "params": {"value": 75}}], "signals": [{"id": "price_gt_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_gt_ce", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "market_volatility_low", "type": "<PERSON><PERSON><PERSON>", "epsilon": 0.5, "inputs": [{"market": "VIX", "transformer": "vix_percentile"}, {"ref": "constant_75"}]}, {"id": "market_volatility_declining", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"market": "VIX", "transformer": "vix_raw"}, {"market": "VIX", "transformer": "vix_ma"}]}, {"id": "price_lt_ma", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "ma_indicator"}, {"column": "Close"}]}, {"id": "price_lt_ce", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "chandelier_exit_indicator"}, {"column": "Close"}]}, {"id": "market_condition_good", "type": "Or", "inputs": [{"ref": "market_volatility_low"}, {"ref": "market_volatility_declining"}]}, {"id": "price_conditions", "type": "And", "inputs": [{"ref": "price_gt_ma"}, {"ref": "price_gt_ce"}]}, {"id": "technical_buy_conditions", "type": "And", "inputs": [{"ref": "price_conditions"}, {"ref": "price_gt_ma"}]}, {"id": "buy_signal_condition", "type": "And", "inputs": [{"ref": "technical_buy_conditions"}, {"ref": "market_condition_good"}]}, {"id": "price_conditions_sell", "type": "And", "inputs": [{"ref": "price_lt_ma"}, {"ref": "price_lt_ce"}]}, {"id": "sell_signal_condition", "type": "And", "inputs": [{"ref": "price_conditions_sell"}, {"type": "Not", "inputs": [{"ref": "market_condition_good"}]}]}], "outputs": {"buy_signal": "buy_signal_condition", "sell_signal": "sell_signal_condition", "indicators": [{"id": "ma_indicator", "output_name": "ma"}, {"id": "atr_indicator", "output_name": "atr"}, {"id": "chandelier_exit_indicator", "output_name": "chandelier_stop"}, {"id": "market_volatility_low", "output_name": "vix_percentile_low"}, {"id": "market_volatility_declining", "output_name": "vix_declining"}, {"id": "market_condition_good", "output_name": "market_ok"}, {"id": "price_conditions", "output_name": "price_conditions"}, {"id": "technical_buy_conditions", "output_name": "tech_buy"}, {"id": "buy_signal_condition", "output_name": "buy_condition"}, {"id": "sell_signal_condition", "output_name": "sell_condition"}], "market_indicators": [{"market": "VIX", "transformer": "vix_raw", "output_name": "vix_raw"}, {"market": "VIX", "transformer": "vix_percentile", "output_name": "vix_percentile"}, {"market": "VIX", "transformer": "vix_ma", "output_name": "vix_ma"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 35}}}, "symbols": [{"symbol": "QQQ", "name": "Nasdaq 100 ETF"}, {"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "IWM", "name": "Russell 2000 ETF"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "市场趋势跟踪策略组合", "code": "myinvestpilot_market_trend", "description": "基于市场指数相对强度和市场趋势的双重过滤策略，结合了离散对比强度速率和市场平均线趋势", "strategy_definition": {"market_indicators": {"indicators": [{"code": "SPX"}], "transformers": [{"name": "spx_raw", "type": "IdentityTransformer", "params": {"indicator": "SPX", "field": "Close"}}, {"name": "spx_ma50", "type": "MovingAverageTransformer", "params": {"indicator": "SPX", "window": 50, "method": "simple", "field": "Close"}}, {"name": "spx_ma200", "type": "MovingAverageTransformer", "params": {"indicator": "SPX", "window": 200, "method": "simple", "field": "Close"}}, {"name": "spx_rs", "type": "RelativeStrengthTransformer", "params": {"indicator": "SPX", "reference": "ma", "window": 90, "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "ma_short", "type": "SMA", "params": {"period": 20, "column": "Close"}}, {"id": "ma_long", "type": "SMA", "params": {"period": 50, "column": "Close"}}, {"id": "rsi_indicator", "type": "RSI", "params": {"period": 14}}], "signals": [{"id": "price_gt_ma_short", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_short"}]}, {"id": "ma_short_gt_ma_long", "type": "GreaterThan", "inputs": [{"ref": "ma_short"}, {"ref": "ma_long"}]}, {"id": "rsi_not_overbought", "type": "<PERSON><PERSON><PERSON>", "epsilon": 0.5, "inputs": [{"type": "Constant", "value": 70}, {"ref": "rsi_indicator"}]}, {"id": "rsi_not_overbought_direct", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"type": "Constant", "value": 65}, {"ref": "rsi_indicator"}]}, {"id": "rsi_not_oversold", "type": "GreaterThan", "inputs": [{"ref": "rsi_indicator"}, {"type": "Constant", "value": 30}]}, {"id": "market_uptrend", "type": "GreaterThan", "inputs": [{"market": "SPX", "transformer": "spx_ma50"}, {"market": "SPX", "transformer": "spx_ma200"}]}, {"id": "market_strength_rising", "type": "GreaterThan", "inputs": [{"market": "SPX", "transformer": "spx_rs"}, {"type": "Constant", "value": 1.0}]}, {"id": "market_environment_bullish", "type": "And", "inputs": [{"ref": "market_uptrend"}, {"ref": "market_strength_rising"}]}, {"id": "market_price_reference", "type": "GreaterThan", "inputs": [{"market": "SPX", "transformer": "spx_raw"}, {"type": "Constant", "value": 0}]}, {"id": "price_and_ma_bullish", "type": "And", "inputs": [{"ref": "price_gt_ma_short"}, {"ref": "ma_short_gt_ma_long"}]}, {"id": "stock_technical_bullish", "type": "And", "inputs": [{"ref": "price_and_ma_bullish"}, {"ref": "rsi_not_overbought"}]}, {"id": "buy_signal_condition", "type": "And", "inputs": [{"ref": "stock_technical_bullish"}, {"ref": "market_environment_bullish"}]}, {"id": "sell_signal_condition", "type": "Or", "inputs": [{"type": "Not", "inputs": [{"ref": "market_uptrend"}]}, {"type": "Not", "inputs": [{"ref": "price_gt_ma_short"}]}]}], "outputs": {"buy_signal": "buy_signal_condition", "sell_signal": "sell_signal_condition", "indicators": [{"id": "ma_short", "output_name": "ma_short"}, {"id": "ma_long", "output_name": "ma_long"}, {"id": "rsi_indicator", "output_name": "rsi"}, {"id": "price_gt_ma_short", "output_name": "price_gt_ma"}, {"id": "ma_short_gt_ma_long", "output_name": "ma_crossover"}, {"id": "rsi_not_overbought", "output_name": "rsi_ok"}, {"id": "rsi_not_overbought_direct", "output_name": "rsi_direct"}, {"id": "rsi_not_oversold", "output_name": "rsi_gt_30"}, {"id": "price_ma_bull", "output_name": "price_ma_bull"}, {"id": "market_uptrend", "output_name": "mkt_uptrend"}, {"id": "market_strength_rising", "output_name": "mkt_strength"}, {"id": "market_environment_bullish", "output_name": "mkt_bullish"}, {"id": "market_price_reference", "output_name": "spx_ref"}, {"id": "stock_technical_bullish", "output_name": "stock_bullish"}, {"id": "buy_signal_condition", "output_name": "buy_condition"}, {"id": "sell_signal_condition", "output_name": "sell_condition"}], "market_indicators": [{"market": "SPX", "transformer": "spx_raw", "output_name": "spx"}, {"market": "SPX", "transformer": "spx_ma50", "output_name": "spx_ma50"}, {"market": "SPX", "transformer": "spx_ma200", "output_name": "spx_ma200"}, {"market": "SPX", "transformer": "spx_rs", "output_name": "spx_rs"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}}, "symbols": [{"symbol": "AAPL", "name": "Apple Inc."}, {"symbol": "MSFT", "name": "Microsoft Corporation"}, {"symbol": "GOOGL", "name": "Alphabet Inc."}, {"symbol": "AMZN", "name": "Amazon.com Inc."}, {"symbol": "NVDA", "name": "NVIDIA Corporation"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "美股1号(原语版)", "code": "myinvestpilot_us_1_primitive", "description": "基于原语化吊灯止损+均线策略的美股杠杆ETF组合", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "ma_indicator", "type": "SMA", "params": {"period": 250, "column": "Close"}}, {"id": "atr_indicator", "type": "ATR", "params": {"period": 60}}, {"id": "chandelier_exit_indicator", "type": "ChandelierExit", "params": {"period": 60, "multiplier": 4.0}}], "signals": [{"id": "price_gt_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_gt_ce", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "price_lt_ma", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "price_lt_ce", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "chandelier_exit_indicator"}]}, {"id": "buy_signal_condition", "type": "And", "inputs": [{"ref": "price_gt_ma"}, {"ref": "price_gt_ce"}]}, {"id": "sell_signal_condition", "type": "And", "inputs": [{"ref": "price_lt_ma"}, {"ref": "price_lt_ce"}]}], "outputs": {"buy_signal": "buy_signal_condition", "sell_signal": "sell_signal_condition", "indicators": [{"id": "ma_indicator", "output_name": "ma"}, {"id": "atr_indicator", "output_name": "atr"}, {"id": "chandelier_exit_indicator", "output_name": "chandelier_stop"}, {"id": "price_gt_ma", "output_name": "price_gt_ma"}, {"id": "price_gt_ce", "output_name": "price_gt_ce"}, {"id": "price_lt_ma", "output_name": "price_lt_ma"}, {"id": "price_lt_ce", "output_name": "price_lt_ce"}, {"id": "buy_signal_condition", "output_name": "buy_condition"}, {"id": "sell_signal_condition", "output_name": "sell_condition"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}}, "symbols": [{"symbol": "FNGU", "name": "FANG+ Index 3x Bull"}, {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"}, {"symbol": "TECL", "name": "S&P Technology 3x Bull"}, {"symbol": "SOXL", "name": "Semiconductor 3x Bull"}, {"symbol": "TNA", "name": "Russell 2000 3x Bull"}, {"symbol": "CONL", "name": "COIN 2x Bull"}, {"symbol": "LABU", "name": "S&P Biotech 3x Bull"}, {"symbol": "FAS", "name": "Financial Sector 3x Bull"}, {"symbol": "EDC", "name": "Emerging Markets 3x Bull"}, {"symbol": "TMF", "name": "Long-term Treasury 3x Bull"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "A股1号(原语版)", "code": "myinvestpilot_cn_1_primitive", "description": "基于原语化双均线策略的A股精选ETF组合", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "shortMA", "type": "SMA", "params": {"period": 11, "column": "Close"}}, {"id": "longMA", "type": "SMA", "params": {"period": 22, "column": "Close"}}], "signals": [{"id": "buy_signal", "type": "Crossover", "params": {"mode": "simple"}, "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]}, {"id": "sell_signal", "type": "<PERSON><PERSON><PERSON>", "params": {"mode": "simple"}, "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal"}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}}, "symbols": [{"symbol": "159928", "name": "消费ETF"}, {"symbol": "159929", "name": "医药ETF"}, {"symbol": "510500", "name": "中证500ETF"}, {"symbol": "159915", "name": "创业板ETF"}, {"symbol": "159939", "name": "信息技术ETF"}, {"symbol": "512100", "name": "中证1000ETF"}, {"symbol": "512660", "name": "军工ETF"}, {"symbol": "159941", "name": "纳指ETF"}, {"symbol": "515180", "name": "红利ETF易方达"}], "start_date": "2018-09-20", "currency": "CNY", "market": "China", "commission": 0.0001, "update_time": "01:00"}, {"name": "RSI超买超卖策略", "code": "myinvestpilot_rsi_primitive", "description": "基于RSI指标的简单超买超卖交易策略", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "rsi_indicator", "type": "RSI", "params": {"period": 11, "column": "Close"}}, {"id": "upper_threshold", "type": "Constant", "params": {"value": 63}}, {"id": "lower_threshold", "type": "Constant", "params": {"value": 37}}], "signals": [{"id": "is_below_lower", "type": "Comparison", "params": {"comparison": "less_equal"}, "inputs": [{"ref": "rsi_indicator"}, {"ref": "lower_threshold"}]}, {"id": "is_above_upper", "type": "Comparison", "params": {"comparison": "greater_equal"}, "inputs": [{"ref": "rsi_indicator"}, {"ref": "upper_threshold"}]}, {"id": "buy_signal_cross", "type": "CrossBelow", "inputs": [{"ref": "rsi_indicator"}, {"ref": "lower_threshold"}]}, {"id": "sell_signal_cross", "type": "CrossAbove", "inputs": [{"ref": "rsi_indicator"}, {"ref": "upper_threshold"}]}], "outputs": {"buy_signal": "buy_signal_cross", "sell_signal": "sell_signal_cross", "indicators": [{"id": "rsi_indicator", "output_name": "rsi"}, {"id": "upper_threshold", "output_name": "upper_bound"}, {"id": "lower_threshold", "output_name": "lower_bound"}, {"id": "is_below_lower", "output_name": "is_oversold"}, {"id": "is_above_upper", "output_name": "is_overbought"}, {"id": "buy_signal_cross", "output_name": "buy_signal"}, {"id": "sell_signal_cross", "output_name": "sell_signal"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 50}}}, "symbols": [{"symbol": "SPY", "name": "SPDR S&P 500 ETF Trust"}, {"symbol": "QQQ", "name": "Invesco QQQ Trust"}], "start_date": "2020-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}]}