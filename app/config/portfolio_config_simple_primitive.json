{"portfolios": [{"name": "简化RSI测试组合", "code": "test_simple_rsi", "description": "简化的RSI信号测试组合，用于验证信号处理", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "rsi", "type": "RSI", "params": {"period": 14, "column": "Close"}}, {"id": "buy_threshold", "type": "Constant", "params": {"value": 30}}, {"id": "sell_threshold", "type": "Constant", "params": {"value": 70}}], "signals": [{"id": "rsi_oversold", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "rsi"}, {"ref": "buy_threshold"}]}, {"id": "rsi_overbought", "type": "GreaterThan", "inputs": [{"ref": "rsi"}, {"ref": "sell_threshold"}]}], "outputs": {"buy_signal": "rsi_oversold", "sell_signal": "rsi_overbought"}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 25}}}, "symbols": [{"symbol": "QQQ", "name": "Nasdaq ETF"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "RSI-MA示例组合", "code": "example_rsi_ma", "description": "RSI和移动平均组合的原语化策略示例，在RSI超买超卖的基础上结合均线确认", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "rsi", "type": "RSI", "params": {"period": 14, "column": "Close"}}, {"id": "sma20", "type": "SMA", "params": {"period": 20, "column": "Close"}}, {"id": "sma50", "type": "SMA", "params": {"period": 50, "column": "Close"}}, {"id": "threshold_30", "type": "Constant", "params": {"value": 30}}, {"id": "threshold_70", "type": "Constant", "params": {"value": 70}}], "signals": [{"id": "rsi_oversold", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "rsi"}, {"ref": "threshold_30"}]}, {"id": "rsi_overbought", "type": "GreaterThan", "inputs": [{"ref": "rsi"}, {"ref": "threshold_70"}]}, {"id": "price_above_sma20", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "sma20"}]}, {"id": "sma20_above_sma50", "type": "GreaterThan", "inputs": [{"ref": "sma20"}, {"ref": "sma50"}]}, {"id": "buy_condition", "type": "Or", "inputs": [{"ref": "rsi_oversold"}, {"ref": "sma20_above_sma50"}]}, {"id": "price_not_above_sma20", "type": "Not", "inputs": [{"ref": "price_above_sma20"}]}, {"id": "sell_condition", "type": "Or", "inputs": [{"ref": "rsi_overbought"}, {"ref": "price_not_above_sma20"}]}], "outputs": {"buy_signal": "buy_condition", "sell_signal": "sell_condition"}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 25}}}, "symbols": [{"symbol": "QQQ", "name": "Nasdaq ETF"}, {"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "XLK", "name": "Technology Sector ETF"}, {"symbol": "XLF", "name": "Financial Sector ETF"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "布林带策略示例", "code": "example_bollinger_bands", "description": "基于布林带突破和回归的原语化策略示例，反转策略理念", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "bb", "type": "BollingerBands", "params": {"period": 20, "std_dev": 2.0, "column": "Close", "method": "sma"}}, {"id": "atr", "type": "ATR", "params": {"period": 14, "method": "sma"}}], "signals": [{"id": "price_below_lower", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"column": "Close"}, {"ref": "bb.lower"}]}, {"id": "price_above_upper", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "bb.upper"}]}, {"id": "price_cross_lower", "type": "Crossover", "params": {"mode": "simple"}, "inputs": [{"column": "Close"}, {"ref": "bb.lower"}]}, {"id": "price_cross_upper", "type": "<PERSON><PERSON><PERSON>", "params": {"mode": "simple"}, "inputs": [{"column": "Close"}, {"ref": "bb.upper"}]}, {"id": "high_volatility", "type": "Percent<PERSON><PERSON>e", "params": {"periods": 10, "comparison": "greater", "threshold": 0.2, "is_absolute": true}, "inputs": [{"column": "Close"}]}, {"id": "buy_signal", "type": "And", "inputs": [{"ref": "price_cross_lower"}, {"ref": "high_volatility"}]}, {"id": "sell_signal", "type": "Or", "inputs": [{"ref": "price_cross_upper"}, {"ref": "price_above_upper"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal"}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}}, "symbols": [{"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "QQQ", "name": "Nasdaq ETF"}, {"symbol": "IWM", "name": "Russell 2000 ETF"}, {"symbol": "EEM", "name": "Emerging Markets ETF"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}, {"name": "MACD+成交量策略示例", "code": "example_macd_volume", "description": "结合MACD和成交量变化的原语化策略示例，寻找高成交量的趋势确认", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "macd_indicator", "type": "MACD", "params": {"fast_period": 12, "slow_period": 26, "signal_period": 9, "column": "Close"}}, {"id": "volume_ma", "type": "SMA", "params": {"period": 20, "column": "Volume"}}, {"id": "highest_high", "type": "HighestValue", "params": {"period": 20, "column": "High"}}, {"id": "lowest_low", "type": "LowestValue", "params": {"period": 20, "column": "Low"}}, {"id": "zero_line", "type": "Constant", "params": {"value": 0}}, {"id": "volatility_threshold", "type": "Constant", "params": {"value": 0.03}}, {"id": "price_range_threshold", "type": "Constant", "params": {"value": 1.5}}], "signals": [{"id": "macd_cross_signal", "type": "Crossover", "params": {"mode": "simple"}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "macd_indicator.signal"}]}, {"id": "macd_below_zero", "type": "<PERSON><PERSON><PERSON>", "params": {"threshold": 0}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "zero_line"}]}, {"id": "not_macd_below_zero", "type": "Not", "inputs": [{"ref": "macd_below_zero"}]}, {"id": "volume_surge", "type": "GreaterThan", "params": {"threshold": 1.5}, "inputs": [{"column": "Volume"}, {"ref": "volume_ma"}]}, {"id": "price_range", "type": "GreaterThan", "params": {"threshold": 10}, "inputs": [{"ref": "highest_high"}, {"ref": "lowest_low"}]}, {"id": "high_volatility", "type": "GreaterThan", "params": {"threshold": 0}, "inputs": [{"ref": "price_range"}, {"ref": "zero_line"}]}, {"id": "buy_condition_part1", "type": "And", "inputs": [{"ref": "macd_cross_signal"}, {"ref": "volume_surge"}]}, {"id": "buy_condition_part2", "type": "And", "inputs": [{"ref": "high_volatility"}, {"ref": "not_macd_below_zero"}]}, {"id": "buy_condition", "type": "And", "inputs": [{"ref": "buy_condition_part1"}, {"ref": "buy_condition_part2"}]}, {"id": "macd_cross_below_signal", "type": "<PERSON><PERSON><PERSON>", "params": {"mode": "simple"}, "inputs": [{"ref": "macd_indicator.macd"}, {"ref": "macd_indicator.signal"}]}, {"id": "sell_condition", "type": "Or", "inputs": [{"ref": "macd_cross_below_signal"}, {"ref": "macd_below_zero"}]}], "outputs": {"buy_signal": "buy_condition", "sell_signal": "sell_condition"}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}}, "symbols": [{"symbol": "SPY", "name": "S&P 500 ETF"}, {"symbol": "QQQ", "name": "Nasdaq ETF"}, {"symbol": "ARKK", "name": "Innovation ETF"}, {"symbol": "XLK", "name": "Technology Sector ETF"}], "start_date": "2018-01-01", "currency": "USD", "market": "US", "commission": 0.0001, "update_time": "08:00"}]}