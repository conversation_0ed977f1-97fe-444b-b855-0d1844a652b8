{"portfolios": [{"name": "沪深300股债轮动策略(原版)", "code": "stock_bond_switch_primitive", "description": "基于沪深300指数200日均线的股债轮动策略，使用原语系统实现，支持全局数据处理", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma200", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 200, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "constant_one", "type": "Constant", "params": {"value": 1}}], "signals": [{"id": "market_trend_up", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma200"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": true}, "inputs": [{"ref": "market_trend_up"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "market_trend_up", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma200", "output_name": "hs300_ma200"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}, "metadata": {"strategy_type": "Asset Rotation - Baseline", "risk_level": "Medium", "optimization_stage": "Initial baseline strategy", "key_features": ["简单的200日均线趋势判断", "全仓股债二元切换", "基于沪深300指数的市场环境判断"]}}, {"name": "沪深300股债轮动策略(优化版v1)", "code": "stock_bond_switch_optimized_v1", "description": "优化版股债轮动：使用120日均线+3日确认机制，减少滞后性和频繁切换", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma120", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 120, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "constant_one", "type": "Constant", "params": {"value": 1}}], "signals": [{"id": "market_trend_up_raw", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma120"}]}, {"id": "market_trend_up_confirmed", "type": "Streak", "params": {"condition": "true", "min_length": 3}, "inputs": [{"ref": "market_trend_up_raw"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": true}, "inputs": [{"ref": "market_trend_up_confirmed"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "market_trend_up_raw", "output_name": "trend_signal_raw"}, {"id": "market_trend_up_confirmed", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma120", "output_name": "hs300_ma120"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}, "metadata": {"strategy_type": "Asset Rotation - Speed Optimized", "risk_level": "Medium", "optimization_stage": "First iteration - reduced lag", "changes_from_baseline": ["缩短均线周期：200日 → 120日", "增加3日确认机制，减少虚假信号", "保持原有资产配置逻辑"], "key_improvements": ["更快的趋势响应速度", "减少震荡市中的频繁切换", "提升年化收益率和夏普比率"]}}, {"name": "沪深300股债轮动策略(优化版v2)", "code": "stock_bond_switch_optimized_v2", "description": "多指标确认版股债轮动：150日均线+RSI+动量确认，提高信号质量", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma150", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 150, "method": "simple", "field": "Close"}}, {"name": "hs300_rsi14", "type": "RSITransformer", "params": {"indicator": "000300.SH", "window": 14, "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "rsi_bullish_threshold", "type": "Constant", "params": {"value": 50}}], "signals": [{"id": "price_above_ma", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma150"}]}, {"id": "rsi_bullish", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_rsi14"}, {"ref": "rsi_bullish_threshold"}]}, {"id": "combined_bullish", "type": "And", "inputs": [{"ref": "price_above_ma"}, {"ref": "rsi_bullish"}]}, {"id": "market_trend_confirmed", "type": "Streak", "params": {"condition": "true", "min_length": 2}, "inputs": [{"ref": "combined_bullish"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": false}, "inputs": [{"ref": "market_trend_confirmed"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "price_above_ma", "output_name": "price_above_ma"}, {"id": "rsi_bullish", "output_name": "rsi_bullish"}, {"id": "market_trend_confirmed", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma150", "output_name": "hs300_ma150"}, {"market": "000300.SH", "transformer": "hs300_rsi14", "output_name": "hs300_rsi"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}, "metadata": {"strategy_type": "Asset Rotation - Multi-Indicator", "risk_level": "Medium-Low", "optimization_stage": "Second iteration - multi-indicator confirmation", "changes_from_baseline": ["均线周期调整：200日 → 150日", "增加RSI动量指标确认", "多重确认：价格>均线 AND RSI>50 AND 连续2日", "调整默认倾向：default_to_stock=false"], "key_improvements": ["更高的信号质量", "多重技术指标确认", "更保守的入场条件"]}}, {"name": "沪深300股债轮动策略(双均线版)", "code": "stock_bond_switch_dual_ma", "description": "基于双均线系统的股债轮动：短期均线上穿长期均线买股票，下穿买债券，更好地捕捉趋势", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma20", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 20, "method": "simple", "field": "Close"}}, {"name": "hs300_ma60", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 60, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "constant_one", "type": "Constant", "params": {"value": 1}}], "signals": [{"id": "ma_crossover_raw", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_ma20"}, {"market": "000300.SH", "transformer": "hs300_ma60"}]}, {"id": "price_above_short_ma", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma20"}]}, {"id": "bullish_trend_confirmed", "type": "And", "inputs": [{"ref": "ma_crossover_raw"}, {"ref": "price_above_short_ma"}]}, {"id": "trend_confirmed", "type": "Streak", "params": {"condition": "true", "min_length": 2}, "inputs": [{"ref": "bullish_trend_confirmed"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": false}, "inputs": [{"ref": "trend_confirmed"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "ma_crossover_raw", "output_name": "ma_crossover"}, {"id": "price_above_short_ma", "output_name": "price_above_ma20"}, {"id": "trend_confirmed", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma20", "output_name": "hs300_ma20"}, {"market": "000300.SH", "transformer": "hs300_ma60", "output_name": "hs300_ma60"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}, "metadata": {"strategy_type": "Asset Rotation - Dual MA System", "risk_level": "Medium", "optimization_stage": "Third iteration - dual moving average", "changes_from_baseline": ["采用双均线系统：20日/60日均线", "双重确认：均线交叉 AND 价格高于短均线", "连续2日确认机制", "提高交易敏感度"], "limitations_discovered": ["交易频率过高（86次/7年）", "在震荡市中容易产生频繁切换", "收益率未达到预期水平"]}}, {"name": "沪深300股债轮动策略(趋势强度版) ⭐", "code": "stock_bond_switch_trend_strength", "description": "基于趋势强度确认的股债轮动：要求价格突破均线且有足够涨幅才买股票，最终优化版本", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "hs300_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "hs300_ma50", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 50, "method": "simple", "field": "Close"}}, {"name": "hs300_strength", "type": "RelativeStrengthTransformer", "params": {"indicator": "000300.SH", "reference": "ma", "window": 50, "field": "Close"}}]}, "trade_strategy": {"indicators": [{"id": "constant_one", "type": "Constant", "params": {"value": 1}}, {"id": "strength_threshold", "type": "Constant", "params": {"value": 105}}], "signals": [{"id": "price_above_ma50", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_raw"}, {"market": "000300.SH", "transformer": "hs300_ma50"}]}, {"id": "strong_trend", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "hs300_strength"}, {"ref": "strength_threshold"}]}, {"id": "bullish_confirmed", "type": "And", "inputs": [{"ref": "price_above_ma50"}, {"ref": "strong_trend"}]}, {"id": "trend_persistence", "type": "Streak", "params": {"condition": "true", "min_length": 5}, "inputs": [{"ref": "bullish_confirmed"}]}, {"id": "stock_bond_buy", "type": "StockBondSwitch", "params": {"default_to_stock": false}, "inputs": [{"ref": "trend_persistence"}]}, {"id": "stock_bond_sell", "type": "Not", "inputs": [{"ref": "stock_bond_buy"}]}], "outputs": {"buy_signal": "stock_bond_buy", "sell_signal": "stock_bond_sell", "indicators": [{"id": "price_above_ma50", "output_name": "price_above_ma50"}, {"id": "strong_trend", "output_name": "strong_trend"}, {"id": "trend_persistence", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "hs300_raw", "output_name": "hs300_price"}, {"market": "000300.SH", "transformer": "hs300_ma50", "output_name": "hs300_ma50"}, {"market": "000300.SH", "transformer": "hs300_strength", "output_name": "hs300_strength"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}, "metadata": {"strategy_type": "Asset Rotation - Trend Strength (Final Optimized)", "risk_level": "Medium", "optimization_stage": "Final iteration - breakthrough result", "changes_from_baseline": ["使用50日均线作为趋势基准", "引入相对强度阈值：价格需比均线高5%以上", "延长确认期：连续5日确认避免假突破", "质量优于数量：只在真正强势时买股票"], "breakthrough_results": {"annual_return_improvement": "1.65% → 5.42% (+229%)", "max_drawdown_improvement": "-12.29% → -7.76% (-37%)", "win_rate_improvement": "40.0% → 81.3% (+103%)", "profit_loss_ratio_improvement": "2.50 → 40.69 (+1528%)", "trade_frequency_reduction": "60次 → 16次 (-73%)"}, "core_philosophy": ["耐心等待：宁可错过机会，不做错误交易", "质量优先：只在高质量信号时入场", "风险优先：默认保守，激进需要强证据", "趋势确认：多重条件确保趋势真实性"]}}]}