"""
市场指数和标的物常量定义模块
用于统一管理市场指数标识、命名和类型
"""

# 主要市场指数映射（代码 -> 中文名称）
MARKET_INDICES = {
    # 中国市场指数
    "000001.SH": "上证综指",
    "000300.SH": "沪深300", 
    "000905.SH": "中证500", 
    "399001.SZ": "深证成指",
    "399006.SZ": "创业板指",
    # 美国市场指数
    "SPX": "标普500指数", 
    "IXIC": "纳斯达克指数",
    "VIX": "波动率指数",
    "NDX": "纳斯达克100指数",
    "DJIA": "道琼斯工业指数",
    "DJI": "道琼斯指数",
    "RUT": "罗素2000指数",
    # 国际市场指数
    "HSI": "恒生指数",
    "GDAXI": "德国DAX指数", 
    "N225": "日经225指数", 
    "KS11": "韩国综合指数",
    "AS51": "澳大利亚标普200指数", 
    "SENSEX": "印度孟买SENSEX指数"
}

# 市场指数代码列表（用于类型判断）
MARKET_INDEX_SYMBOLS = list(MARKET_INDICES.keys())

def is_market_index(symbol):
    """
    判断给定符号是否为市场指数
    
    Args:
        symbol: 要检查的股票或指数代码
        
    Returns:
        bool: 如果是市场指数则返回True，否则返回False
    """
    return symbol in MARKET_INDEX_SYMBOLS
