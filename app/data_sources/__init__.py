from io import StringIO
import os
import logging
import time
import requests
import sentry_sdk
from typing import Optional
from data_sources.redis import RedisClient
from data_sources.s3 import S3
from constants import S3_PUBLIC_BUCKET_NAME, S3_PRIVATE_BUCKET_NAME, S3_PUBLIC_PORTFOLIO_BASE_PATH, S3_PRIVATE_PORTFOLIO_BASE_PATH, INVEST_OHLC_PROXY_HOST
from utilities.helpers import get_today_str, is_china_stock, is_cryptocurrency
from data_sources.market_symbols import is_market_index
import pandas as pd

module_logger = logging.getLogger(__name__)

class PortfolioDataLoader:
    _instance = None

    def __init__(self):
        self.s3_client = S3()
        self.redis_client = RedisClient.get_instance("data")

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def _get_cache_key_from_api(self, data_type, params):
        """从 investOHLCProxy API 获取 cache key"""
        url = f"{INVEST_OHLC_PROXY_HOST}/api/v1/data/request"
        payload = {
            "data_type": data_type,
            "params": params
        }
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            return response.json().get("cache_key")
        except Exception as e:
            module_logger.error(f"Error getting cache key from API: {e}")
            raise

    def _fetch_from_invest_ohlc_proxy(self, data_type, params):
        """从 Redis 获取数据，如果不存在则等待结果"""
        try:
            cache_key = self._get_cache_key_from_api(data_type, params)
            max_wait_time = 300  # 最大等待时间（秒）
            poll_interval = 1  # 轮询间隔（秒）

            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    try:
                        # 解析 JSON 数据
                        df = pd.read_json(StringIO(cached_data), orient='split')

                        # 检查是否有 'date' 列（新格式）或 'trade_date' 列（旧格式）
                        if 'date' in df.columns:
                            date_column = 'date'
                        elif 'trade_date' in df.columns:
                            date_column = 'trade_date'
                        else:
                            module_logger.error(f"Neither 'date' nor 'trade_date' column found in cached data for {cache_key}. Columns: {list(df.columns)}")
                            raise ValueError(f"Essential date column missing in cached data for {cache_key}. Available columns: {list(df.columns)}")

                        # 将日期列转换为 datetime 并设置为索引
                        df[date_column] = pd.to_datetime(df[date_column])
                        df.set_index(date_column, inplace=True)
                        df.index.name = 'date'  # 确保索引名称为 'date'

                        # 排序数据
                        df = df.sort_index()

                        return df
                    except Exception as e:
                        module_logger.error(f"Error processing cached data for {cache_key}: {str(e)}")
                        raise
                time.sleep(poll_interval)
            raise Exception("Timeout waiting for data from investOHLCProxy")
        except Exception as e:
            sentry_sdk.capture_exception(e)
            raise

    def get_trading_days(self, start_date, end_date, market="US"):
        """
        获取指定时间段内的交易日

        此方法通过获取标的数据，然后提取其中的日期索引来确定交易日。
        根据市场参数选择合适的参考标的。

        Args:
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            market (str): 市场类型，可选值："US"、"CN"、"CRYPTO"，默认为"US"

        Returns:
            pd.DatetimeIndex: 包含所有交易日的DatetimeIndex对象
        """
        module_logger.info(f"Getting trading days for {market} market from {start_date} to {end_date}")
        try:
            # 根据市场选择参考标的
            if market == "CN":
                reference_symbol = "000001.SH"  # 上证指数
            elif market == "CRYPTO":
                reference_symbol = "BTC"    # 比特币
            else:
                reference_symbol = "SPY"        # 默认使用SPY（美股）
            
            # 获取历史数据
            params = {
                "symbol": reference_symbol,
                "market": market,
                "start_date": start_date,
                "end_date": end_date,
                "adjusted": True
            }
            
            data = self._fetch_from_invest_ohlc_proxy("ohlc", params)
            
            # 提取日期索引作为交易日
            trading_days = data.index
            
            module_logger.info(f"Found {len(trading_days)} trading days for {market} market")
            return trading_days
            
        except Exception as e:
            module_logger.error(f"Error getting trading days: {str(e)}")
            sentry_sdk.capture_exception(e)
            # 返回一个空的 DatetimeIndex
            return pd.DatetimeIndex([])

    def get_qfq_close_price(self, code, end=get_today_str()):
        module_logger.info(f"Loading qfq close price for {code} until {end}")
        if is_china_stock(code):
            market = "CN"
        elif is_cryptocurrency(code):
            market = "CRYPTO"
        else:
            market = "US"

        params = {
            "symbol": code,
            "market": market,
            'start_date': '2010-01-01', # investOHLCProxy 仅支持从 2010-01-01 开始的数据
            "end_date": end,
            "adjusted": True
        }

        data = self._fetch_from_invest_ohlc_proxy("ohlc", params)

        module_logger.info(f"Loaded {len(data)} rows of data for {code} from investOHLCProxy")
        module_logger.info(data)

        # 确保列名符合预期
        column_mapping = {
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume',
            'adj_close': 'Adj Close',
            'adj_factor': 'Adj Factor'
        }
        data.rename(columns={col: column_mapping.get(col, col) for col in data.columns}, inplace=True)

        # 确保所需的列都存在
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Adj Close', 'Adj Factor', 'symbol']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Required column '{col}' not found in data")

        return data[required_columns]

    def get_market_index(self, code, start_date, end_date=get_today_str()):
        params = {
            "symbol": code,
            "market": "CN",
            "start_date": start_date,
            "end_date": end_date
        }
        data = self._fetch_from_invest_ohlc_proxy("market_index", params)

        return data

    def upload_to_public_bucket(self, namespace, file_name):
        file_local_path = os.path.abspath(file_name)
        file_s3_name = os.path.join(S3_PUBLIC_PORTFOLIO_BASE_PATH, namespace, os.path.basename(file_local_path))
        try:
            self.s3_client.upload_file(S3_PUBLIC_BUCKET_NAME, file_local_path, file_s3_name)
            module_logger.info(f"File {file_local_path} uploaded to {file_s3_name} in bucket {S3_PUBLIC_BUCKET_NAME}.")
        except Exception as e:
            module_logger.info(f"Failed to upload {file_local_path} to {file_s3_name} in bucket {S3_PUBLIC_BUCKET_NAME}. Error: {e}")

    def get_market_data(self, code: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> pd.DataFrame:
        """
        统一的市场数据获取接口，根据代码类型自动判断使用合适的数据源
        
        根据代码是否为市场指数，自动选择合适的数据获取方法：
        - 如果是市场指数：使用get_market_index获取数据
        - 如果是普通标的：使用get_qfq_close_price获取数据
        
        Args:
            code: 市场数据代码（股票代码或指数代码）
            start_date: 开始日期，对市场指数为必填，普通标的可选
            end_date: 结束日期，默认为今天
            
        Returns:
            pd.DataFrame: 市场数据DataFrame
            
        Raises:
            ValueError: 当市场指数缺少必需的start_date参数时
        """
        end_date = end_date or get_today_str()
        module_logger.info(f"Loading market data for {code} from {start_date or 'default'} to {end_date}")
        
        if is_market_index(code):
            if not start_date:
                raise ValueError(f"start_date parameter is required for market index '{code}'")
            return self.get_market_index(code, start_date, end_date)
        else:
            if start_date:
                # 如果提供了start_date，则添加到params中
                market = "CN" if is_china_stock(code) else "CRYPTO" if is_cryptocurrency(code) else "US"
                params = {
                    "symbol": code,
                    "market": market,
                    "start_date": start_date,
                    "end_date": end_date,
                    "adjusted": True
                }
                data = self._fetch_from_invest_ohlc_proxy("ohlc", params)
                
                # 确保列名符合预期
                column_mapping = {
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume',
                    'adj_close': 'Adj Close',
                    'adj_factor': 'Adj Factor'
                }
                data.rename(columns={col: column_mapping.get(col, col) for col in data.columns}, inplace=True)
                
                # 确保所需的列都存在
                required_columns = ['Open', 'High', 'Low', 'Close', 'Volume', 'Adj Close', 'Adj Factor', 'symbol']
                for col in required_columns:
                    if col not in data.columns:
                        raise ValueError(f"Required column '{col}' not found in data")
                
                return data[required_columns]
            else:
                return self.get_qfq_close_price(code, end_date)
    
    def upload_to_private_bucket(self, namespace, file_name):
        file_local_path = os.path.abspath(file_name)
        file_s3_name = os.path.join(S3_PRIVATE_PORTFOLIO_BASE_PATH, namespace, os.path.basename(file_local_path))
        try:
            self.s3_client.upload_file(S3_PRIVATE_BUCKET_NAME, file_local_path, file_s3_name)
            module_logger.info(f"File {file_local_path} uploaded to {file_s3_name} in bucket {S3_PRIVATE_BUCKET_NAME}.")
        except Exception as e:
            module_logger.info(f"Failed to upload {file_local_path} to {file_s3_name} in bucket {S3_PRIVATE_BUCKET_NAME}. Error: {e}")
