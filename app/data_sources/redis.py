import logging
import pandas as pd
import redis
from datetime import datetime, timedelta
import pytz
import threading

from constants import FLY_UPSTASH_REDIS_HOST, FLY_UPSTASH_REDIS_PORT, FLY_UPSTASH_REDIS_PASSWORD, UPSTASH_REDIS_HOST, UPSTASH_REDIS_PASSWORD, UPSTASH_REDIS_PORT

module_logger = logging.getLogger(__name__)

class RedisClient:
    _instances = {}
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, client_type="data", config=None):
        if client_type not in cls._instances:
            with cls._lock:
                if client_type not in cls._instances:
                    if config is None:
                        if client_type == "data":
                            config = {
                                "host": FLY_UPSTASH_REDIS_HOST,
                                "port": FLY_UPSTASH_REDIS_PORT,
                                "password": FLY_UPSTASH_REDIS_PASSWORD,
                                "ssl": False,
                                "decode_responses": True
                            }
                        elif client_type == "queue":
                            config = {
                                "host": UPSTASH_REDIS_HOST,
                                "port": UPSTASH_REDIS_PORT,
                                "password": UPSTASH_REDIS_PASSWORD,
                                "ssl": True,
                                "decode_responses": False
                            }
                    cls._instances[client_type] = redis.StrictRedis(**config)
        return cls._instances[client_type]

class Redis:
    def __init__(self):
        self.client = RedisClient.get_instance()

    def set_df(self, key, df):
        """将DataFrame以JSON格式存入Redis"""
        value = df.to_json(date_format='iso', orient='split')
        self.set(key, value)

        # 设置过期时间为UTC+8的次日8点
        tomorrow_utc8 = datetime.now(pytz.timezone('Asia/Shanghai')).replace(hour=8, minute=0, second=0, microsecond=0) + timedelta(days=1)
        expire_at = tomorrow_utc8.astimezone(pytz.utc)
        self.client.expireat(key, expire_at)

        module_logger.info(f"Set DataFrame to Redis with key: {key}")

    def get_df(self, key):
        """从Redis获取JSON格式的DataFrame并反序列化"""
        value = self.get(key)
        if value:
            df = pd.read_json(value, orient='split')
            module_logger.info(f"Get DataFrame from Redis with key: {key}")
            return df
        return None

    def get(self, key):
        return self.client.get(key)

    def set(self, key, value, ex=None):
        return self.client.set(key, value, ex=ex)

    def delete(self, key):
        return self.client.delete(key)

    def keys(self, pattern):
        return self.client.keys(pattern)

    def flushdb(self):
        return self.client.flushdb()
