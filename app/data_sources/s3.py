import boto3
import logging
from botocore.exceptions import Client<PERSON>rror
from constants import S3_ENDPOINT_URL, S3_ACCESS_KEY, S3_SECRET_ACCESS_KEY

module_logger = logging.getLogger(__name__)

class S3:
    def __init__(self):
        self.s3_client = boto3.client(
            "s3", 
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=S3_ACCESS_KEY,
            aws_secret_access_key=S3_SECRET_ACCESS_KEY
        )
        self.s3_resource = boto3.resource(
            "s3",
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=S3_ACCESS_KEY,
            aws_secret_access_key=S3_SECRET_ACCESS_KEY
        )

    def upload_file(self, bucket_name, file_local_name, file_s3_name):
        """Upload a file to an S3 bucket"""
        try:
            self.s3_client.upload_file(
                file_local_name, bucket_name, file_s3_name
            )
            module_logger.info(f"File {file_local_name} uploaded to {bucket_name}")
        except ClientError as e:
            module_logger.error(e)
            return False
        return True
    
    def save_json_to_s3(self, bucket_name, json_data, file_s3_name):
        """Save json data to an S3 bucket"""
        try:
            self.s3_client.put_object(
                Bucket=bucket_name,
                Key=file_s3_name,
                Body=json_data
            )
            module_logger.info(f"Json data ({file_s3_name}) saved to {bucket_name}")
        except ClientError as e:
            module_logger.error(e)
            return False
        return True

    def download_file(self, bucket_name, file_s3_name, file_local_name):
        """Download a file from an S3 bucket"""
        try:
            self.s3_client.download_file(
                bucket_name, file_s3_name, file_local_name
            )
            module_logger.info(f"File {file_s3_name} downloaded from {bucket_name}")
        except ClientError as e:
            module_logger.error(e)
            return False
        return True

    def list_files(self, bucket_name):
        """List files in an S3 bucket"""
        try:
            contents = []
            for item in self.s3_resource.Bucket(bucket_name).objects.all():
                contents.append(item.key)
            return contents
        except ClientError as e:
            module_logger.error(e)
            return None

    def delete_file(self, bucket_name, file_name):
        """Delete a file from an S3 bucket"""
        try:
            self.s3_client.delete_object(Bucket=bucket_name, Key=file_name)
            module_logger.info(f"File {file_name} deleted from {bucket_name}")
        except ClientError as e:
            module_logger.error(e)
            return False
        return True
