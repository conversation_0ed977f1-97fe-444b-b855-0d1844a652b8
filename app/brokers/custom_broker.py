import backtrader as bt
import logging

from analyzers.xirr_analyzer import XIRRAnalyzer

module_logger = logging.getLogger(__name__)

class CustomBroker(bt.brokers.BackBroker):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.capital_records = []
        self._fundvalue = 100.0  # 默认基金净值
        self._fundmode = False
        self._total_investment = 0.0  # 跟踪总投资金额（仅用于定投策略）

    def set_cash(self, cash, date):
        super().set_cash(cash)
        self.record_capital_change('initial', cash, date)

    def add_cash(self, amount, date, change_type=None):
        """添加现金
        
        Args:
            amount: 金额
            date: 日期
            change_type: 资金变动类型，如果不指定，则根据金额正负判断为 'deposit' 或 'withdrawal'
        """
        super().add_cash(amount)
        if change_type is None:
            change_type = 'deposit' if amount > 0 else 'withdrawal'
        self.record_capital_change(change_type, amount, date)

    def set_fundmode(self, fundmode=True, fundstartval=100.0):
        """设置基金模式"""
        self._fundmode = fundmode
        if fundmode:
            self._fundvalue = fundstartval

    def get_fundvalue(self):
        """获取基金净值"""
        return self._fundvalue

    def set_fundvalue(self, value):
        """设置基金净值"""
        if self._fundmode:
            self._fundvalue = value

    def record_capital_change(self, change_type, amount, date):
        """记录资金变动"""
        module_logger.info(f"\n=== Recording Capital Change ===")
        module_logger.info(f"Type: {change_type}")
        module_logger.info(f"Amount: {amount}")
        module_logger.info(f"Date: {date}")
        module_logger.info(f"Available Cash: {self.getcash()}")
        module_logger.info(f"Total Assets: {self.getvalue()}")

        capital_change = {
            'date': date,
            'trade_type': change_type,
            'change_amount': amount,
            'available_cash': self.getcash(),
            'total_assets': self.getvalue()
        }

        if change_type == 'initial':
            self.capital_records.insert(0, capital_change)
            self._total_investment = amount
        elif change_type in ['annual_investment', 'monthly_investment']:
            self.capital_records.append(capital_change)
            self._total_investment += amount
        else:
            self.capital_records.append(capital_change)
            
        # 定投策略时，通知策略的分析器
        if hasattr(self, 'strategy'):
            module_logger.info(f"Notifying strategy of fund change: type={change_type}")
            if change_type in ['initial', 'annual_investment', 'monthly_investment']:
                for analyzer in self.strategy.analyzers:
                    if isinstance(analyzer, XIRRAnalyzer):
                        module_logger.info(f"Recording cashflow: type={change_type}, amount={amount}")
                        analyzer.record_cashflow(amount, date, change_type)

    def get_capital_records(self):
        return self.capital_records

    def getvalue(self, datas=None):
        value = super().getvalue(datas)
        if self._fundmode and hasattr(self, 'strategy'):
            if hasattr(self.strategy, 'sizer') and hasattr(self.strategy.sizer, 'params'):
                capital_strategy = self.strategy.sizer.params.capital_strategy
                # 使用接口方法检查,而不是isinstance
                if capital_strategy.is_fixed_investment():
                    if self._total_investment > 0:
                        self._fundvalue = value / self._total_investment * 100.0  # 使用100作为基准
                else:
                    self._fundvalue = value / self.startingcash * 100.0  # 使用100作为基准
        return value
