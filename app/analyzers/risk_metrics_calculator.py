"""
Risk Metrics Calculator - 风险指标计算引擎

这个模块提供了独立的风险指标计算功能，可以基于任何价值序列计算风险指标。
主要用于处理现金流调整后的净值数据。

设计原因：
- RiskMetricsAnalyzer在Backtrader运行时只能获得原始portfolio value
- 对于有现金流的组合，需要基于调整后净值重新计算价值相关指标
- 为了避免代码重复，将计算逻辑提取到独立的计算器中

使用场景：
1. 无现金流组合：直接使用RiskMetricsAnalyzer结果
2. 有现金流组合：使用本计算器基于调整后净值重新计算价值指标
"""

import pandas as pd
import logging
from utilities.helpers import round_floats

module_logger = logging.getLogger(__name__)


class RiskMetricsCalculator:
    """风险指标计算引擎"""
    
    @staticmethod
    def calculate_value_metrics(values, dates):
        """
        基于价值序列计算价值相关的风险指标
        
        Args:
            values: 价值序列 (pandas.Series 或 list)
            dates: 日期序列 (pandas.DatetimeIndex 或 list)
            
        Returns:
            dict: 包含价值相关风险指标的字典
            
        计算的指标：
        - cagr: 复合年化收益率
        - current_drawdown: 当前回撤
        - max_drawdown: 最大回撤
        - max_drawdown_duration: 最大回撤持续时间（天）
        - annual_returns: 年度收益率字典
        - calmar: Calmar比率
        - daily_returns: 日收益率列表
        """
        try:
            module_logger.info("Calculating value-based risk metrics")

            # 检查数据充分性
            if values is None or (hasattr(values, '__len__') and len(values) < 2):
                module_logger.warning(f"Insufficient data for value metrics calculation. Need at least 2 data points, got: {len(values) if hasattr(values, '__len__') else 'None'}")
                return {
                    'cagr': 0,
                    'current_drawdown': 0,
                    'max_drawdown': 0,
                    'max_drawdown_duration': 0,
                    'calmar': 0,
                    'annual_returns': {},
                    'daily_returns': []
                }

            # 检查数据质量
            if hasattr(values, 'iloc') and values.iloc[0] <= 0:
                module_logger.warning(f"First value is non-positive: {values.iloc[0]}. This may cause calculation issues.")
            elif hasattr(values, '__getitem__') and values[0] <= 0:
                module_logger.warning(f"First value is non-positive: {values[0]}. This may cause calculation issues.")

            # 转换为pandas Series以便计算
            if not isinstance(values, pd.Series):
                values = pd.Series(values, index=dates)
            if not isinstance(dates, pd.DatetimeIndex):
                dates = pd.DatetimeIndex(dates)

            # 确保数据对齐
            values.index = dates
            
            # 计算CAGR
            start_date = dates[0]
            end_date = dates[-1]
            years = (end_date - start_date).days / 365.25
            
            if years > 0 and values.iloc[0] > 0:
                start_value = float(values.iloc[0])
                end_value = float(values.iloc[-1])
                cagr = (end_value / start_value) ** (1/years) - 1
                module_logger.info(f"CAGR calculation: start={start_value:.4f}, end={end_value:.4f}, years={years:.2f}, CAGR={cagr:.4f}")
            else:
                cagr = 0
                
            # 计算回撤指标 (with zero-division protection)
            rolling_max = values.cummax()

            # 保护除零操作：如果rolling_max为0，则回撤也为0
            with pd.option_context('mode.use_inf_as_na', True):
                drawdown = (values - rolling_max) / rolling_max
                # 处理inf/NaN值：如果rolling_max为0，设置回撤为0
                drawdown = drawdown.fillna(0)
                drawdown = drawdown.replace([float('inf'), float('-inf')], 0)

            current_drawdown = float(drawdown.iloc[-1])
            max_drawdown = float(drawdown.min())

            module_logger.debug(f"Drawdown calculation: current={current_drawdown:.4f}, max={max_drawdown:.4f}")
            
            # 计算最大回撤持续时间
            max_drawdown_duration = 0
            if max_drawdown < 0:
                end_date_of_max_drawdown = drawdown.idxmin()
                start_date_of_max_drawdown = rolling_max.loc[:end_date_of_max_drawdown].idxmax()
                max_drawdown_duration = (end_date_of_max_drawdown - start_date_of_max_drawdown).days
                
            # 计算Calmar比率
            calmar = cagr / abs(max_drawdown) if max_drawdown < 0 else 0
            
            # 计算日收益率 (vectorized approach with zero-division handling)
            if len(values) > 1:
                daily_returns_series = values.pct_change()

                # Handle potential inf/NaN values from zero division
                daily_returns_series = daily_returns_series.replace([float('inf'), float('-inf')], 0)
                daily_returns_series = daily_returns_series.fillna(0)

                # Drop the first NaN and convert to list
                daily_returns_series = daily_returns_series.dropna()
                daily_returns_list = daily_returns_series.round(4).tolist()

                module_logger.debug(f"Calculated {len(daily_returns_list)} daily returns, range: [{min(daily_returns_list) if daily_returns_list else 'N/A'}, {max(daily_returns_list) if daily_returns_list else 'N/A'}]")
            else:
                daily_returns_list = []
                daily_returns_series = pd.Series([], dtype=float)
            
            # 计算年度收益率
            if len(daily_returns_list) > 0:
                # 使用与daily_returns_series相同的索引
                daily_returns = daily_returns_series  # 直接使用已计算的series
                annual_returns = daily_returns.groupby(lambda x: x.year).apply(
                    lambda x: (1 + x).prod() - 1
                )
                annual_returns_dict = {str(year): float(ret) for year, ret in annual_returns.items()}
            else:
                annual_returns_dict = {}
            
            # 组装结果
            value_metrics = {
                'cagr': float(cagr),
                'current_drawdown': current_drawdown,
                'max_drawdown': max_drawdown,
                'max_drawdown_duration': max_drawdown_duration,
                'calmar': float(calmar),
                'annual_returns': annual_returns_dict,
                'daily_returns': daily_returns_list
            }
            
            # 应用浮点数舍入
            value_metrics = round_floats(value_metrics)
            
            module_logger.info(f"Value metrics calculated: CAGR={cagr:.4f}, MaxDD={max_drawdown:.4f}, Calmar={calmar:.4f}")
            return value_metrics
            
        except Exception as e:
            module_logger.error(f"Failed to calculate value metrics: {e}")
            return {
                'cagr': 0,
                'current_drawdown': 0,
                'max_drawdown': 0,
                'max_drawdown_duration': 0,
                'calmar': 0,
                'annual_returns': {},
                'daily_returns': []
            }
    
    @staticmethod
    def get_trade_related_metrics(analyzer_result):
        """
        从RiskMetricsAnalyzer结果中提取交易相关指标
        
        Args:
            analyzer_result: RiskMetricsAnalyzer.get_analysis()的结果
            
        Returns:
            dict: 包含交易相关指标的字典
        """
        trade_metrics = {}
        
        # 交易相关指标（这些不受现金流影响）
        trade_related_keys = [
            'win_rate',
            'profit_loss_ratio', 
            'total_trades',
            'won_trades',
            'lost_trades'
        ]
        
        for key in trade_related_keys:
            if key in analyzer_result:
                trade_metrics[key] = analyzer_result[key]
                
        return trade_metrics
