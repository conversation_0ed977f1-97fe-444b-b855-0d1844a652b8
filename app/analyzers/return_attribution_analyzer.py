import backtrader as bt
import logging
import pandas as pd
from collections import defaultdict
import json

module_logger = logging.getLogger(__name__)

class ReturnAttributionAnalyzer(bt.Analyzer):
    """
    Analyzer that breaks down portfolio returns by year and by asset.
    
    This analyzer:
    1. Tracks portfolio value changes over time
    2. Breaks down returns by calendar year
    3. Analyzes contribution from each asset
    """
    
    def create_analysis(self):
        """Initialize the analyzer with empty results"""
        module_logger.info("Initializing Return Attribution Analyzer")
        self.rets = {
            'annual_returns': {},                    # Year -> return
            'annual_contribution_percentages': {},   # Year -> contribution percentage
            'asset_returns': {},                     # Asset -> return
            'asset_contributions': {},               # Asset -> contribution to portfolio
            'asset_contribution_percentages': {},    # Asset -> contribution percentage
            'monthly_returns': {},                   # Year-Month -> return
            'best_month': {},                        # Best month return
            'worst_month': {},                       # Worst month return
            'best_year': {},                         # Best year return
            'worst_year': {}                         # Worst year return
        }
        
        # For tracking portfolio value by date
        self.daily_values = {}
        self.daily_asset_values = defaultdict(dict)
        
        # For tracking positions
        self.asset_positions = {}
        
        # 添加年度跟踪变量
        self.prev_year_start_value = None
        self.prev_year = None
        self.current_year_start_value = None
        self.current_year = None
        
        # 月度跟踪变量
        self.prev_month_start_value = None
        self.prev_month = None
        self.current_month_start_value = None
        self.current_month = None
        self.current_month_year = None
    
    def notify_cashvalue(self, cash, value):
        """Record portfolio value on each day"""
        date = self.strategy.datetime.date(0)
        self.daily_values[date] = value
        
        # 处理年度切换
        current_year = date.year
        if self.current_year is None:
            # 首次记录
            self.current_year = current_year
            self.current_year_start_value = value
        elif current_year != self.current_year:
            # 年度切换
            self.prev_year = self.current_year
            self.prev_year_start_value = self.current_year_start_value
            self.current_year = current_year
            self.current_year_start_value = value
        
        # 处理月度切换
        current_month = date.month
        if self.current_month is None:
            # 首次记录
            self.current_month = current_month
            self.current_month_year = current_year
            self.current_month_start_value = value
        elif current_month != self.current_month or current_year != self.current_month_year:
            # 月度切换
            self.prev_month = self.current_month
            self.prev_month_start_value = self.current_month_start_value
            self.current_month = current_month
            self.current_month_year = current_year
            self.current_month_start_value = value
        
        # 记录资产价值
        for data in self.datas:
            position = self.strategy.getposition(data)
            if position.size != 0:
                price = data.close[0]
                asset_value = position.size * price
                self.daily_asset_values[date][data._name] = asset_value
                # 更新持仓
                self.asset_positions[data._name] = position.size
    

    def stop(self):
        """Calculate attribution at the end of the strategy"""
        module_logger.info("Calculating Return Attribution")
        
        if not self.daily_values:
            module_logger.warning("No daily values recorded, cannot calculate return attribution")
            return
            
        # 转换为pandas Series以便计算
        daily_series = pd.Series(self.daily_values)
        daily_series.index = pd.to_datetime(daily_series.index)
        daily_series = daily_series.sort_index()
        
        # 计算日收益率和总体收益率
        daily_returns = daily_series.pct_change().fillna(0)
        total_return = (daily_series.iloc[-1] / daily_series.iloc[0]) - 1 if len(daily_series) > 1 else 0
        
        # 计算年度收益率和贡献
        self._calculate_annual_contribution_percentages(daily_series, total_return)
        
        # 计算月度收益率
        monthly_returns = daily_returns.groupby([daily_returns.index.year, daily_returns.index.month]).apply(
            lambda x: (1 + x).prod() - 1
        )
        
        # 找出最佳和最差月份
        if not monthly_returns.empty:
            best_month_idx = monthly_returns.idxmax()
            worst_month_idx = monthly_returns.idxmin()
            
            best_month = {
                'year': int(best_month_idx[0]),
                'month': int(best_month_idx[1]),
                'return': float(monthly_returns.max())
            }
            
            worst_month = {
                'year': int(worst_month_idx[0]),
                'month': int(worst_month_idx[1]),
                'return': float(monthly_returns.min())
            }
            
            self.rets['best_month'] = best_month
            self.rets['worst_month'] = worst_month
        
        # 将月度收益率转换为字典
        for (year, month), ret in monthly_returns.items():
            month_key = f"{year}-{month:02d}"
            self.rets['monthly_returns'][month_key] = float(ret)
            
        # 计算资产收益率和贡献
        self._calculate_asset_attribution(daily_series, total_return)
        
        module_logger.info("Return Attribution Analysis completed")
        module_logger.info(f"Annual returns: {json.dumps(self.rets['annual_returns'])}")

    def _calculate_annual_contribution_percentages(self, daily_series, total_return):
        """Calculate the percentage contribution of each year to total return"""
        df = pd.DataFrame({'value': daily_series})
        yearly_returns = df.resample('Y').last()
        yearly_returns['return'] = yearly_returns['value'].pct_change()
        
        # 计算年度收益率和贡献百分比
        yearly_contributions = {}
        total_contribution = 0
        
        for year in yearly_returns.index.year:
            year_data = df[df.index.year == year]
            if len(year_data) >= 2:
                year_return = (year_data['value'].iloc[-1] / year_data['value'].iloc[0]) - 1
                year_weight = year_data['value'].iloc[0] / daily_series.iloc[0]
                year_contribution = year_return * year_weight
                
                if total_return != 0:
                    contribution_pct = (year_contribution / total_return) * 100
                else:
                    contribution_pct = 0
                
                self.rets['annual_returns'][str(year)] = float(year_return)
                yearly_contributions[str(year)] = float(contribution_pct)
                total_contribution += year_contribution
        
        # 找出最佳和最差年份
        if yearly_contributions:
            best_year = max(yearly_contributions.items(), key=lambda x: float(x[1]))
            worst_year = min(yearly_contributions.items(), key=lambda x: float(x[1]))
            
            self.rets['best_year'] = {
                'year': best_year[0],
                'return': self.rets['annual_returns'][best_year[0]],
                'contribution': best_year[1]
            }
            
            self.rets['worst_year'] = {
                'year': worst_year[0],
                'return': self.rets['annual_returns'][worst_year[0]],
                'contribution': worst_year[1]
            }
        
        # 存储年度贡献百分比（按贡献绝对值排序）
        sorted_contributions = sorted(
            yearly_contributions.items(),
            key=lambda x: abs(float(x[1])),
            reverse=True
        )
        self.rets['annual_contribution_percentages'] = dict(sorted_contributions)

    def _calculate_asset_attribution(self, daily_series, total_return):
        """Calculate returns and contributions by asset"""
        # 转换资产价值为DataFrame
        asset_values_df = self._convert_asset_values_to_df()
        
        if asset_values_df is None or asset_values_df.empty:
            module_logger.warning("No asset values data available")
            return
        
        # 获取初始资产配置
        initial_portfolio_value = daily_series.iloc[0]
        asset_contributions = {}
        asset_returns = {}
        asset_contribution_percentages = {}
        
        # 计算每个资产的收益率和贡献
        for asset in asset_values_df.columns:
            asset_series = asset_values_df[asset].dropna()
            
            if len(asset_series) >= 2:
                # 计算资产收益率
                asset_initial_value = asset_series.iloc[0]
                asset_final_value = asset_series.iloc[-1]
                asset_return = (asset_final_value / asset_initial_value) - 1
                
                # 计算权重和贡献
                asset_weight = asset_initial_value / initial_portfolio_value
                contribution = asset_weight * asset_return
                
                # 计算贡献百分比
                if total_return != 0:
                    contribution_pct = (contribution / total_return) * 100
                else:
                    contribution_pct = 0
                
                # 存储结果
                asset_returns[asset] = float(asset_return)
                asset_contributions[asset] = float(contribution)
                asset_contribution_percentages[asset] = float(contribution_pct)
        
        # 按贡献绝对值排序
        sorted_contributions = sorted(
            asset_contributions.items(), 
            key=lambda x: abs(x[1]), 
            reverse=True
        )
        
        sorted_percentages = sorted(
            asset_contribution_percentages.items(),
            key=lambda x: abs(x[1]),
            reverse=True
        )
        
        # 存储排序后的结果
        self.rets['asset_returns'] = asset_returns
        self.rets['asset_contributions'] = dict(sorted_contributions)
        self.rets['asset_contribution_percentages'] = dict(sorted_percentages)
    
    def _convert_asset_values_to_df(self):
        """Convert daily asset values to a DataFrame"""
        if not self.daily_asset_values:
            return None
            
        # 从嵌套字典创建DataFrame
        dates = sorted(self.daily_asset_values.keys())
        assets = set()
        for date_values in self.daily_asset_values.values():
            assets.update(date_values.keys())
            
        assets = sorted(assets)
        
        data = []
        for date in dates:
            row = [date]
            for asset in assets:
                row.append(self.daily_asset_values[date].get(asset, None))
            data.append(row)
            
        if not data:
            return None
            
        df = pd.DataFrame(data, columns=['date'] + list(assets))
        df.set_index('date', inplace=True)
        df.index = pd.to_datetime(df.index)
        
        return df
    
    def get_analysis(self):
        """Return analyzer results"""
        return self.rets
