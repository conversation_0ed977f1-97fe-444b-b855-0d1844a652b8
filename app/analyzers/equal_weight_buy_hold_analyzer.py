import backtrader as bt
import logging
import pandas as pd
import numpy as np

module_logger = logging.getLogger(__name__)

class EqualWeightBuyHoldAnalyzer(bt.Analyzer):
    """
    Analyzer that compares the strategy performance to an equal-weight buy and hold strategy.
    
    This analyzer:
    1. Takes the initial capital and list of assets
    2. Simulates an equal-weight buy and hold strategy
    3. Calculates the performance metrics of this strategy
    """
    
    params = (
        ('initial_capital', 100000),  # Default initial capital
    )
    
    def create_analysis(self):
        """Initialize the analyzer with empty results"""
        module_logger.info("Initializing Equal-Weight Buy Hold Analyzer")
        self.rets = {
            'initial_capital': self.p.initial_capital,
            'final_value': 0,
            'overall_return': 0,
            'cagr': 0,
            'assets': [],
            'max_drawdown': 0,
            'sharpe_ratio': 0
        }
        
        # For tracking prices over time
        self.asset_prices = {}
        self.portfolio_values = []
        self.dates = []
    
    def notify_cashvalue(self, cash, value):
        """Record the current date for calculating duration"""
        current_date = self.strategy.datetime.date(0)
        if not self.dates:
            self.dates.append(current_date)
        self.dates[-1] = current_date
    
    def next(self):
        """Record prices on each bar"""
        date = self.strategy.datetime.date(0)
        
        # Record all prices for this date
        date_prices = {}
        for data in self.datas:
            symbol = data._name
            price = data.close[0]
            if price > 0:  # Skip invalid prices
                date_prices[symbol] = price
                
        self.asset_prices[date] = date_prices
        self.dates.append(date)
    
    def stop(self):
        """Calculate equal-weight buy and hold performance at the end"""
        module_logger.info("Calculating Equal-Weight Buy Hold performance")
        
        if not self.asset_prices:
            module_logger.warning("No price data collected, cannot calculate equal-weight performance")
            return
            
        # Get the first and last dates with price data
        dates = sorted(self.asset_prices.keys())
        if not dates:
            module_logger.warning("No dates with price data found")
            return
            
        start_date = dates[0]
        end_date = dates[-1]
        
        # 确保日期是字符串类型，避免序列化问题
        start_date_str = str(start_date)
        end_date_str = str(end_date)
        
        # Find assets that have prices for both start and end dates
        valid_assets = []
        
        if start_date not in self.asset_prices or end_date not in self.asset_prices:
            module_logger.warning(f"Missing start or end date price data: {start_date} or {end_date}")
            return
            
        start_prices = self.asset_prices[start_date]
        end_prices = self.asset_prices[end_date]
        
        for symbol in start_prices:
            if symbol in end_prices:
                start_price = start_prices[symbol]
                end_price = end_prices[symbol]
                
                # 确保价格是有效的正数
                if start_price <= 0 or end_price <= 0:
                    continue
                    
                # Calculate individual asset return
                asset_return = (end_price - start_price) / start_price
                asset_info = {
                    'symbol': symbol,
                    'start_price': start_price,
                    'end_price': end_price,
                    'return': asset_return
                }
                valid_assets.append(asset_info)
        
        if not valid_assets:
            module_logger.warning("No valid assets for equal-weight calculation")
            return
            
        # Calculate equal-weight allocation
        num_assets = len(valid_assets)
        allocation_per_asset = self.p.initial_capital / num_assets
        
        # Calculate portfolio values over time
        portfolio_values = []
        for date in dates:
            date_prices = self.asset_prices[date]
            portfolio_value = 0
            
            for asset in valid_assets:
                symbol = asset['symbol']
                if symbol in date_prices:
                    # Calculate current asset value based on price ratio to starting price
                    current_price = date_prices[symbol]
                    start_price = asset['start_price']
                    if start_price > 0:
                        asset_value = allocation_per_asset * (current_price / start_price)
                        portfolio_value += asset_value
            
            if portfolio_value > 0:
                portfolio_values.append((date, portfolio_value))
        
        if not portfolio_values:
            module_logger.warning("Could not calculate portfolio values over time")
            return
            
        # Sort portfolio values by date
        portfolio_values.sort(key=lambda x: x[0])
        dates = [pv[0] for pv in portfolio_values]
        values = [pv[1] for pv in portfolio_values]
        
        # Calculate final portfolio value
        final_value = values[-1] if values else 0
        
        # Calculate overall return
        overall_return = (final_value - self.p.initial_capital) / self.p.initial_capital if final_value > 0 else 0
        
        # Calculate CAGR
        days = (end_date - start_date).days
        years = days / 365.25
        cagr = (final_value / self.p.initial_capital) ** (1/years) - 1 if years > 0 and final_value > 0 else 0
        
        # Calculate max drawdown
        max_drawdown = 0
        peak_value = values[0] if values else 0
        
        for value in values:
            if value > peak_value:
                peak_value = value
            elif peak_value > 0:
                drawdown = (peak_value - value) / peak_value
                max_drawdown = max(max_drawdown, drawdown)
        
        # Calculate Sharpe ratio (simplified)
        sharpe_ratio = 0
        if len(values) > 1:
            # Convert to pandas Series for easy calculation
            value_series = pd.Series(values)
            daily_returns = value_series.pct_change().dropna()
            
            if len(daily_returns) > 0:
                avg_return = daily_returns.mean()
                std_dev = daily_returns.std()
                # Annualized Sharpe ratio (assuming 252 trading days)
                sharpe_ratio = (avg_return * 252) / (std_dev * np.sqrt(252)) if std_dev > 0 else 0
        
        # For each asset, calculate its contribution to the final portfolio
        for asset in valid_assets:
            allocated_capital = allocation_per_asset
            final_asset_value = allocated_capital * (1 + asset['return'])
            asset['allocation'] = allocated_capital
            asset['final_value'] = final_asset_value
            asset['weight'] = allocated_capital / self.p.initial_capital
            asset['contribution'] = asset['weight'] * asset['return']
        
        # Sort assets by contribution
        valid_assets.sort(key=lambda x: x['contribution'], reverse=True)
        
        # Store the results as simple types to avoid serialization issues
        self.rets['final_value'] = float(final_value)
        self.rets['overall_return'] = float(overall_return)
        self.rets['cagr'] = float(cagr)
        self.rets['max_drawdown'] = float(max_drawdown)
        self.rets['sharpe_ratio'] = float(sharpe_ratio)
        self.rets['assets'] = valid_assets
        self.rets['start_date'] = start_date_str
        self.rets['end_date'] = end_date_str
        
        module_logger.info(f"Equal-Weight Buy Hold Analysis completed: CAGR={cagr:.4f}, Return={overall_return:.4f}")
    
    def get_analysis(self):
        """Return analyzer results"""
        return self.rets
