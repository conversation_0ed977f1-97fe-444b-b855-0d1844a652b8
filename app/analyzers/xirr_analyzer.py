import backtrader as bt
import logging
from utilities.helpers import calculate_xirr
from capital_strategies.cash_flow_interface import CashFlowTypes

module_logger = logging.getLogger(__name__)

class CashFlowReturnAnalyzer(bt.Analyzer):
    """通用现金流收益率分析器

    这个分析器支持所有类型的现金流策略，计算XIRR（内部收益率），它会：
    1. 收集所有类型的现金流（定投、分红再投资、止损撤资等）
    2. 支持统一的现金流接口
    3. 在策略结束时计算准确的XIRR
    4. 与CashFlowAdjustedNAVObserver协同工作
    """
    
    def create_analysis(self):
        # module_logger.info("=== Initializing XIRR Analyzer ===")
        self.rets = {}
        self.cashflows = []
        # module_logger.info("Initialized empty cashflows list")

    def notify_cashvalue(self, cash, value):
        """记录当前的现金和总资产价值"""
        # module_logger.info("\n=== XIRR Notify Cash Value ===")
        # module_logger.info(f"Cash: {cash}")
        # module_logger.info(f"Portfolio Value: {value}")
        # 在策略结束时记录最终市值
        self.final_value = value

    def record_cashflow(self, amount, date, flow_type):
        """记录现金流 - 支持所有类型的现金流

        Args:
            amount: 现金流金额（正数为流入，负数为流出）
            date: 日期
            flow_type: 现金流类型，支持所有CashFlowTypes定义的类型
        """
        # 支持所有统一现金流接口定义的类型
        supported_types = CashFlowTypes.get_all_types()

        # 向后兼容：支持旧的类型名称
        legacy_types = ['initial', 'cash_flow']

        if flow_type in supported_types or flow_type in legacy_types:
            # 对于XIRR计算，流入资金记录为负数，流出资金记录为正数
            # 这符合财务计算的惯例：投资（流出）为负，收益（流入）为正
            xirr_amount = -amount if amount > 0 else amount

            self.cashflows.append({
                'date': date,
                'amount': xirr_amount,
                'type': flow_type,
                'original_amount': amount  # 保留原始金额用于调试
            })

            module_logger.info(f"Recorded {flow_type} cash flow: {amount} on {date} (XIRR amount: {xirr_amount})")
        else:
            module_logger.warning(f"Unsupported cash flow type: {flow_type}. Supported types: {supported_types + legacy_types}")

    def stop(self):
        """策略结束时计算 XIRR - 不重复获取数据"""
        module_logger.info(f"Starting XIRR calculation with {len(self.cashflows)} recorded cash flows")

        if self.cashflows:
            # 检查是否已有初始投资记录，避免重复添加
            has_initial = any(cf['type'] == 'initial' for cf in self.cashflows)

            if not has_initial:
                # 只有在没有初始投资记录时才从broker获取
                capital_records = self.strategy.broker.get_capital_records()
                initial_investment = next((record for record in capital_records if record['trade_type'] == 'initial'), None)

                if initial_investment:
                    module_logger.info(f"Adding missing initial investment: {initial_investment['change_amount']} on {initial_investment['date']}")
                    self.cashflows.insert(0, {
                        'date': initial_investment['date'],
                        'amount': -initial_investment['change_amount'],  # 投资为负数
                        'type': 'initial'
                    })
                else:
                    module_logger.warning("No initial investment found in capital records")

            # 添加最终价值
            final_date = self.strategy.datetime.date(0)
            final_value = self.strategy.broker.getvalue()  # 获取准确的最终价值

            self.cashflows.append({
                'date': final_date,
                'amount': final_value,
                'type': 'final_value'
            })

            # 按日期排序现金流
            self.cashflows.sort(key=lambda x: x['date'])

            module_logger.info("All cashflows for XIRR calculation:")
            for cf in self.cashflows:
                module_logger.info(f"  {cf['date']}: {cf['amount']} ({cf['type']})")

            # 计算XIRR
            dates = [cf['date'] for cf in self.cashflows]
            amounts = [cf['amount'] for cf in self.cashflows]

            self.rets['xirr'] = calculate_xirr(dates, amounts)
            module_logger.info(f"Calculated XIRR: {self.rets['xirr']}")
        else:
            module_logger.warning("No cashflows recorded, XIRR set to 0.0")
            self.rets['xirr'] = 0.0

    def get_analysis(self):
        # module_logger.info(f"Getting XIRR analysis result: {self.rets}")
        return self.rets


# 向后兼容性别名
XIRRAnalyzer = CashFlowReturnAnalyzer
