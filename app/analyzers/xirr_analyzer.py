import backtrader as bt
import logging
from utilities.helpers import calculate_xirr

module_logger = logging.getLogger(__name__)

class XIRRAnalyzer(bt.Analyzer):
    """计算定投策略的 XIRR
    
    这个分析器专门用于计算定投策略的 XIRR，它会：
    1. 收集所有定投相关的现金流（初始投资、定期投资）
    2. 在策略结束时计算 XIRR
    """
    
    def create_analysis(self):
        # module_logger.info("=== Initializing XIRR Analyzer ===")
        self.rets = {}
        self.cashflows = []
        # module_logger.info("Initialized empty cashflows list")

    def notify_cashvalue(self, cash, value):
        """记录当前的现金和总资产价值"""
        # module_logger.info("\n=== XIRR Notify Cash Value ===")
        # module_logger.info(f"Cash: {cash}")
        # module_logger.info(f"Portfolio Value: {value}")
        # 在策略结束时记录最终市值
        self.final_value = value

    def record_cashflow(self, amount, date, flow_type):
        """记录现金流
        
        Args:
            amount: 现金流金额
            date: 日期
            flow_type: 现金流类型（initial/annual_investment/monthly_investment）
        """
        # module_logger.info("\n=== Recording Cashflow ===")
        # module_logger.info(f"Type: {flow_type}")
        # module_logger.info(f"Amount: {amount}")
        # module_logger.info(f"Date: {date}")
        
        # TODO: 重构此方法以支持通用现金流类型，而不仅限于定投策略
        # TODO: 考虑将XIRRAnalyzer重命名为CashFlowReturnAnalyzer，使其更加通用
        # TODO: 支持各种资金流入/流出类型，不仅限于'initial'和'*_investment'
        if flow_type in ['initial', 'annual_investment', 'monthly_investment', 'cash_flow']:  # 临时添加'cash_flow'支持
            self.cashflows.append({
                'date': date,
                'amount': -amount,  # 投资为负数
                'type': flow_type
            })
            # module_logger.info(f"Current cashflows list length: {len(self.cashflows)}")
            module_logger.info(f"Recorded {flow_type} cash flow: {amount} on {date}")

    def stop(self):
        """策略结束时计算 XIRR"""
        # module_logger.info("\n====== XIRR Calculation ======")
        # module_logger.info(f"Number of recorded cashflows: {len(self.cashflows)}")
        
        if self.cashflows:
            # 从 broker 的 capital_records 中获取初始投资记录
            capital_records = self.strategy.broker.get_capital_records()
            initial_investment = next((record for record in capital_records if record['trade_type'] == 'initial'), None)
            
            if initial_investment:
                module_logger.info(f"Found initial investment: {initial_investment['change_amount']} on {initial_investment['date']}")
                # 将初始投资添加到现金流列表的开头
                self.cashflows.insert(0, {
                    'date': initial_investment['date'],
                    'amount': -initial_investment['change_amount'],  # 投资为负数
                    'type': 'initial'
                })
            
            final_date = self.strategy.datetime.date(0)
            # module_logger.info(f"Final Date: {final_date}")
            # module_logger.info(f"Final Portfolio Value: {self.final_value}")
            
            self.cashflows.append({
                'date': final_date,
                'amount': self.final_value,
                'type': 'final_value'
            })
            
            module_logger.info("All cashflows:")
            for cf in self.cashflows:
                module_logger.info(f"  {cf['date']}: {cf['amount']} ({cf['type']})")
            
            dates = [cf['date'] for cf in self.cashflows]
            amounts = [cf['amount'] for cf in self.cashflows]
            
            self.rets['xirr'] = calculate_xirr(dates, amounts)
            # module_logger.info(f"Calculated XIRR: {self.rets['xirr']}")
        else:
            module_logger.warning("No cashflows recorded, XIRR set to 0.0")
            self.rets['xirr'] = 0.0
        
        # module_logger.info(f"Final XIRR result: {self.rets['xirr']}")

    def get_analysis(self):
        # module_logger.info(f"Getting XIRR analysis result: {self.rets}")
        return self.rets
