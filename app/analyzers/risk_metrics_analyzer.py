import backtrader as bt
import logging
import pandas as pd
import numpy as np

from utilities.helpers import round_floats

module_logger = logging.getLogger(__name__)

class RiskMetricsAnalyzer(bt.Analyzer):
    """
    Comprehensive risk metrics analyzer that calculates various portfolio 
    performance measures in one place.
    """
    
    def create_analysis(self):
        """Initialize the analyzer with empty results"""
        module_logger.info("Initializing Risk Metrics Analyzer")
        self.rets = {
            'cagr': 0,
            'current_drawdown': 0,
            'max_drawdown': 0,
            'max_drawdown_duration': 0,
            'win_rate': 0,
            'profit_loss_ratio': 0,
            'total_trades': 0,
            'won_trades': 0,
            'lost_trades': 0,
            'annual_returns': {},
            'calmar': 0,
            'daily_returns': [],
        }
        
        # For tracking portfolio value over time
        self.daily_values = []
        self.dates = []
    
    def notify_cashvalue(self, cash, value):
        """Record portfolio value on each day"""
        date = self.strategy.datetime.date(0)

        # Try to get cash flow adjusted NAV if available
        adjusted_value = self._get_adjusted_nav_value(date)
        calculation_value = adjusted_value if adjusted_value is not None else value

        self.daily_values.append(calculation_value)
        self.dates.append(date)

        # Calculate daily return if we have at least 2 values
        if len(self.daily_values) >= 2:
            daily_return = (calculation_value - self.daily_values[-2]) / self.daily_values[-2]
            self.rets['daily_returns'].append(round(daily_return, 4))
    
    def stop(self):
        """Calculate risk metrics at the end of the strategy"""
        module_logger.info("Calculating Risk Metrics")
        
        if not self.daily_values or len(self.daily_values) < 2:
            module_logger.warning("Insufficient data points for risk metrics calculation")
            return
        
        # Create a DataFrame with values
        df = pd.DataFrame({'value': self.daily_values}, index=self.dates)
        df.sort_index(inplace=True)
        
        # 计算CAGR
        start_date = df.index[0]
        end_date = df.index[-1]
        years = (end_date - start_date).days / 365.25
        
        if years > 0 and df['value'].iloc[0] > 0:
            start_value = float(df['value'].iloc[0])
            end_value = float(df['value'].iloc[-1])
            cagr = (end_value / start_value) ** (1/years) - 1
            module_logger.info(f"CAGR计算: 开始日期={start_date}, 结束日期={end_date}, "
                            f"起始值={start_value:.2f}, 结束值={end_value:.2f}, "
                            f"年数={years:.2f}, CAGR={cagr:.4f}")
        else:
            cagr = 0
            module_logger.warning(f"CAGR计算失败: 年数={years}, 起始值={df['value'].iloc[0]}")
            
        self.rets['cagr'] = float(cagr)
        
        # 计算回撤
        rolling_max = df['value'].cummax()
        drawdown = (df['value'] - rolling_max) / rolling_max
        current_drawdown = float(drawdown.iloc[-1])
        max_drawdown = float(drawdown.min())
        self.rets['current_drawdown'] = current_drawdown
        self.rets['max_drawdown'] = max_drawdown
        
        module_logger.info(f"回撤计算: 当前回撤={current_drawdown:.4f}, 最大回撤={max_drawdown:.4f}")
        
        # 计算最大回撤持续时间
        if max_drawdown < 0:
            end_date_of_max_drawdown = drawdown.idxmin()
            start_date_of_max_drawdown = rolling_max.loc[:end_date_of_max_drawdown].idxmax()
            max_drawdown_duration = (end_date_of_max_drawdown - start_date_of_max_drawdown).days
            module_logger.info(f"最大回撤区间: {start_date_of_max_drawdown} 至 {end_date_of_max_drawdown}, "
                           f"持续{max_drawdown_duration}天")
        else:
            max_drawdown_duration = 0
        self.rets['max_drawdown_duration'] = max_drawdown_duration
        
        # 计算Calmar比率
        if max_drawdown < 0:
            calmar = cagr / abs(max_drawdown)
        else:
            calmar = 0
        self.rets['calmar'] = float(calmar)
        module_logger.info(f"Calmar比率: {calmar:.4f} (CAGR={cagr:.4f} / |最大回撤|={abs(max_drawdown):.4f})")
        
        # Calculate annual returns
        daily_returns = pd.Series(self.rets['daily_returns'], index=self.dates[1:])
        annual_returns = daily_returns.groupby(lambda x: x.year).apply(
            lambda x: (1 + x).prod() - 1
        )
        self.rets['annual_returns'] = {str(year): float(ret) for year, ret in annual_returns.items()}
        
        # Get trade metrics from the TradeAnalyzer
        if hasattr(self.strategy.analyzers, 'TradeAnalyzer'):
            trade_stats = self.strategy.analyzers.TradeAnalyzer.get_analysis()
            
            total_trades = trade_stats.get('total', {}).get('total', 0)
            won_trades = trade_stats.get('won', {}).get('total', 0)
            lost_trades = trade_stats.get('lost', {}).get('total', 0)
            
            win_rate = won_trades / total_trades if total_trades > 0 else 0
            
            avg_won = trade_stats.get('won', {}).get('pnl', {}).get('average', 0)
            avg_lost = trade_stats.get('lost', {}).get('pnl', {}).get('average', 0)
            profit_loss_ratio = abs(avg_won / avg_lost) if avg_lost != 0 else 0
            
            self.rets['win_rate'] = float(win_rate)
            self.rets['profit_loss_ratio'] = float(profit_loss_ratio)
            self.rets['total_trades'] = total_trades
            self.rets['won_trades'] = won_trades
            self.rets['lost_trades'] = lost_trades
        
        # Apply rounding to all float values
        self.rets = round_floats(self.rets)
        
        module_logger.info(f"Risk Metrics Analysis completed: CAGR={self.rets['cagr']}, MaxDD={self.rets['max_drawdown']}")

    def _get_adjusted_nav_value(self, date):
        """Try to get cash flow adjusted NAV value for the given date"""
        try:
            # Look for CashFlowAdjustedNAVObserver in strategy observers
            for observer in self.strategy.observers:
                if hasattr(observer, '__class__') and 'CashFlowAdjustedNAVObserver' in observer.__class__.__name__:
                    # Get the current NAV value from the observer
                    if hasattr(observer, 'lines') and hasattr(observer.lines, 'nav_adjusted'):
                        current_nav = observer.lines.nav_adjusted[0]
                        if current_nav > 0:  # Valid NAV value
                            return current_nav
            return None
        except Exception as e:
            module_logger.debug(f"Failed to get adjusted NAV value: {e}")
            return None

    def get_analysis(self):
        """Return analyzer results"""
        return self.rets
