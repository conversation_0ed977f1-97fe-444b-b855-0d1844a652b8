"""
现金流管理接口定义

这个模块定义了统一的现金流管理接口，支持各种类型的资金进出操作。
所有需要处理现金流的策略都应该实现这些接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional
from datetime import date
import logging

module_logger = logging.getLogger(__name__)


class CashFlowInterface(ABC):
    """
    现金流管理的统一接口。
    
    这个接口定义了所有策略都应该支持的现金流操作，
    包括定投、分红再投资、止损撤资等各种场景。
    """
    
    @abstractmethod
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        """
        获取当前日期的所有现金流操作。
        
        Args:
            current_date: 当前交易日期
            current_cash: 当前可用现金
            current_value: 当前投资组合总价值
            
        Returns:
            Dict[str, float]: 现金流字典，格式为 {flow_type: amount}
                - amount > 0: 资金流入
                - amount < 0: 资金流出
                - amount = 0: 无现金流
                
        支持的现金流类型：
            - 'annual_investment': 年度定投
            - 'monthly_investment': 月度定投
            - 'dividend_reinvestment': 分红再投资
            - 'stop_loss_withdrawal': 止损撤资
            - 'rebalancing_adjustment': 再平衡调整
            - 'emergency_withdrawal': 紧急撤资
            - 'bonus_investment': 奖金投资
            - 'custom': 自定义现金流
        """
        pass


class CashFlowManager:
    """
    现金流管理器，提供通用的现金流处理功能。

    这个类可以被各种策略使用，提供标准化的现金流处理逻辑。
    """

    def __init__(self, max_daily_flow=1000000):
        """
        初始化现金流管理器

        Args:
            max_daily_flow: 单日最大现金流限额，默认100万
        """
        self.cash_flow_history = []
        self.total_inflows = 0.0
        self.total_outflows = 0.0
        self.max_daily_flow = max_daily_flow
    
    def process_cash_flows(self, cash_flows: Dict[str, float], current_date: date) -> Dict[str, float]:
        """
        处理现金流并记录历史。
        
        Args:
            cash_flows: 现金流字典
            current_date: 当前日期
            
        Returns:
            Dict[str, float]: 处理后的现金流（可能包含风控调整）
        """
        processed_flows = {}
        
        for flow_type, amount in cash_flows.items():
            if amount == 0:
                continue
                
            # 应用风控检查
            adjusted_amount = self._apply_risk_controls(amount, flow_type, current_date)
            
            if adjusted_amount != 0:
                processed_flows[flow_type] = adjusted_amount
                
                # 记录历史
                self._record_cash_flow(flow_type, adjusted_amount, current_date)
                
                # 更新统计
                if adjusted_amount > 0:
                    self.total_inflows += adjusted_amount
                else:
                    self.total_outflows += abs(adjusted_amount)
                
                module_logger.info(f"Processed cash flow: {flow_type} = {adjusted_amount} on {current_date}")
        
        return processed_flows
    
    def _apply_risk_controls(self, amount: float, flow_type: str, current_date: date) -> float:
        """
        应用风控规则到现金流。
        
        Args:
            amount: 原始金额
            flow_type: 现金流类型
            current_date: 当前日期
            
        Returns:
            float: 调整后的金额
        """
        # 基础风控：限制单日最大现金流
        if abs(amount) > self.max_daily_flow:
            module_logger.warning(f"Cash flow {amount} exceeds daily limit {self.max_daily_flow}, capping it")
            return self.max_daily_flow if amount > 0 else -self.max_daily_flow
        
        # 可以在这里添加更多风控规则
        # 例如：频率限制、总量限制、市场条件检查等
        
        return amount
    
    def _record_cash_flow(self, flow_type: str, amount: float, current_date: date) -> None:
        """
        记录现金流历史。
        
        Args:
            flow_type: 现金流类型
            amount: 金额
            current_date: 日期
        """
        record = {
            'date': current_date,
            'type': flow_type,
            'amount': amount,
            'cumulative_inflows': self.total_inflows,
            'cumulative_outflows': self.total_outflows
        }
        self.cash_flow_history.append(record)
    
    def get_cash_flow_summary(self) -> Dict[str, float]:
        """
        获取现金流汇总信息。
        
        Returns:
            Dict[str, float]: 汇总信息
        """
        return {
            'total_inflows': self.total_inflows,
            'total_outflows': self.total_outflows,
            'net_cash_flow': self.total_inflows - self.total_outflows,
            'total_transactions': len(self.cash_flow_history)
        }


class AdvancedCashFlowStrategy(CashFlowInterface):
    """
    高级现金流策略的基础实现。
    
    这个类提供了一些常见的现金流策略实现，
    可以被具体的策略类继承和扩展。
    """
    
    def __init__(self):
        self.cash_flow_manager = CashFlowManager()
        self.strategy_params = {}
    
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        """
        默认实现：返回空的现金流。
        子类应该重写这个方法来实现具体的现金流逻辑。
        """
        return {}
    
    def set_strategy_params(self, params: Dict) -> None:
        """
        设置策略参数。
        
        Args:
            params: 策略参数字典
        """
        self.strategy_params.update(params)
    
    def get_strategy_param(self, key: str, default=None):
        """
        获取策略参数。
        
        Args:
            key: 参数键
            default: 默认值
            
        Returns:
            参数值或默认值
        """
        return self.strategy_params.get(key, default)


# 预定义的现金流类型常量
class CashFlowTypes:
    """现金流类型常量定义"""
    ANNUAL_INVESTMENT = 'annual_investment'
    MONTHLY_INVESTMENT = 'monthly_investment'
    DIVIDEND_REINVESTMENT = 'dividend_reinvestment'
    STOP_LOSS_WITHDRAWAL = 'stop_loss_withdrawal'
    REBALANCING_ADJUSTMENT = 'rebalancing_adjustment'
    EMERGENCY_WITHDRAWAL = 'emergency_withdrawal'
    BONUS_INVESTMENT = 'bonus_investment'
    CUSTOM = 'custom'
    
    @classmethod
    def get_all_types(cls):
        """获取所有支持的现金流类型"""
        return [
            cls.ANNUAL_INVESTMENT,
            cls.MONTHLY_INVESTMENT,
            cls.DIVIDEND_REINVESTMENT,
            cls.STOP_LOSS_WITHDRAWAL,
            cls.REBALANCING_ADJUSTMENT,
            cls.EMERGENCY_WITHDRAWAL,
            cls.BONUS_INVESTMENT,
            cls.CUSTOM
        ]
