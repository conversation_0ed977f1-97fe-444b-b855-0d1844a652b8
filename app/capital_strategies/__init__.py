class BaseCapitalStrategy:
    def __init__(self, initial_capital):
        self.initial_capital = initial_capital

    def get_max_allocation_percentage(self):
        """获取单个标的可分配的最大资金百分比"""
        return 100  # 默认为100%，具体策略可覆盖

    def get_max_positions(self):
        """获取最大允许持仓数量"""
        return None  # 默认不限制，None表示不限制
    
    def check_periodic_investment(self, data, broker):
        """检查是否应执行定期投资（仅在定投策略中实现）"""
        return False  # 默认返回False，表示不是定投时机
        
    def get_cash_flow(self, data, broker):
        """
        Get cash flow for the current period.
        
        Args:
            data: Market data
            broker: Broker instance
            
        Returns:
            tuple: (amount, flow_type) where:
                - amount: Cash flow amount, positive for inflow, negative for outflow
                - flow_type: Type of cash flow (e.g., 'initial', 'annual_investment', 'cash_flow')
        """
        return (0.0, None)  # Default no cash flow
        
    @classmethod
    def from_dsl(cls, dsl_script):
        # 实现从 DSL 加载策略的逻辑
        pass

    def allocate_capital(self, cash, total_value, data, broker, *args, **kwargs):
        """
        获取一次买入交易的资金分配金额
        :param cash: 当前可用资金
        :param total_value: 总资产
        :param data: 当前交易标的的数据
        :param broker: 当前的经纪商
        :param args: 其他参数
        :param kwargs: 其他关键字参数
        """
        raise NotImplementedError("Must implement allocate_capital method!")

    def is_fixed_investment(self):
        """是否是定投策略"""
        return False

class FixedInvestmentBase(BaseCapitalStrategy):
    """定投策略基类"""
    def is_fixed_investment(self):
        return True
