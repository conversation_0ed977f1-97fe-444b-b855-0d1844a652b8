from capital_strategies import BaseCapitalStrategy
import logging

module_logger = logging.getLogger(__name__)

class SimplePercentCapitalStrategy(BaseCapitalStrategy):
    """使用可用现金百分比进行资金分配的策略
    
    与PercentCapitalStrategy不同，本策略基于当前可用现金而非总资产计算分配金额
    """

    # 默认参数
    params = {
        'initial_capital': 100000,
        'percents': 20,
        'max_positions': None,  # 最大持仓数量，None表示不限制
    }

    def __init__(self, params=None):
        if params:
            self.params.update(params)
        super().__init__(self.params['initial_capital'])

    def get_max_allocation_percentage(self):
        """获取单个标的可分配的最大资金百分比"""
        return self.params['percents']
        
    def get_max_positions(self):
        """获取最大允许持仓数量"""
        return self.params['max_positions']

    def allocate_capital(self, cash, total_value, data, broker, *args, **kwargs):
        """
        本策略基于可用现金计算分配金额
        例如，如果可用现金为10,000，percents为20，则每次买入交易的资金为2,000
        如果当前可用现金不足2,000，则分配现有现金
        如果总资产不足2,000，则分配总资产的20%
        如果总资产为0，则不分配资金
        """
        return cash * (self.params['percents'] / 100)
