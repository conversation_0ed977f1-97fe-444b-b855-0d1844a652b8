"""
高级现金流策略示例

这个文件展示了如何使用统一的现金流接口来实现复杂的现金流策略，
包括多种现金流类型的组合使用。
"""

from datetime import date, timedelta
from typing import Dict
from capital_strategies.cash_flow_interface import AdvancedCashFlowStrategy, CashFlowTypes
import logging

module_logger = logging.getLogger(__name__)


class DynamicInvestmentStrategy(AdvancedCashFlowStrategy):
    """
    动态投资策略示例
    
    这个策略展示了如何实现复杂的现金流逻辑：
    1. 定期定投
    2. 市场下跌时加仓
    3. 盈利时部分撤资
    4. 分红再投资
    """
    
    def __init__(self, params=None):
        super().__init__()
        
        # 默认参数
        default_params = {
            'monthly_investment': 1000,      # 月度定投金额
            'dip_buy_threshold': -0.05,      # 下跌5%时加仓
            'dip_buy_multiplier': 2.0,       # 加仓倍数
            'profit_taking_threshold': 0.20,  # 盈利20%时部分撤资
            'profit_taking_ratio': 0.1,      # 撤资比例10%
            'dividend_reinvest_ratio': 1.0,  # 分红100%再投资
            'max_monthly_investment': 5000,   # 单月最大投资限额
        }
        
        if params:
            default_params.update(params)
        
        self.set_strategy_params(default_params)
        
        # 状态跟踪
        self.last_investment_month = None
        self.last_portfolio_value = None
        self.profit_taking_history = []
    
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        """
        实现复杂的现金流逻辑
        """
        cash_flows = {}
        
        # 1. 月度定投
        monthly_flow = self._calculate_monthly_investment(current_date)
        if monthly_flow > 0:
            cash_flows[CashFlowTypes.MONTHLY_INVESTMENT] = monthly_flow
        
        # 2. 市场下跌加仓
        dip_buy_flow = self._calculate_dip_buying(current_value)
        if dip_buy_flow > 0:
            cash_flows[CashFlowTypes.BONUS_INVESTMENT] = dip_buy_flow
        
        # 3. 盈利撤资
        profit_taking_flow = self._calculate_profit_taking(current_value, current_date)
        if profit_taking_flow < 0:
            cash_flows[CashFlowTypes.REBALANCING_ADJUSTMENT] = profit_taking_flow
        
        # 4. 分红再投资（模拟）
        dividend_flow = self._calculate_dividend_reinvestment(current_date)
        if dividend_flow > 0:
            cash_flows[CashFlowTypes.DIVIDEND_REINVESTMENT] = dividend_flow
        
        # 5. 应用总量限制
        cash_flows = self._apply_monthly_limits(cash_flows, current_date)
        
        # 更新状态
        self.last_portfolio_value = current_value
        
        return self.cash_flow_manager.process_cash_flows(cash_flows, current_date)
    
    def _calculate_monthly_investment(self, current_date: date) -> float:
        """计算月度定投金额"""
        current_month = current_date.strftime('%Y-%m')
        
        if self.last_investment_month == current_month:
            return 0  # 本月已投资
        
        # 检查是否是月初（前5个工作日）
        if current_date.day <= 5:
            self.last_investment_month = current_month
            amount = self.get_strategy_param('monthly_investment', 1000)
            module_logger.info(f"Monthly investment: {amount} on {current_date}")
            return amount
        
        return 0
    
    def _calculate_dip_buying(self, current_value: float) -> float:
        """计算市场下跌时的加仓金额"""
        if self.last_portfolio_value is None:
            return 0
        
        # 计算跌幅
        decline_ratio = (current_value - self.last_portfolio_value) / self.last_portfolio_value
        threshold = self.get_strategy_param('dip_buy_threshold', -0.05)
        
        if decline_ratio <= threshold:
            base_amount = self.get_strategy_param('monthly_investment', 1000)
            multiplier = self.get_strategy_param('dip_buy_multiplier', 2.0)
            
            # 跌幅越大，加仓越多
            intensity = abs(decline_ratio) / abs(threshold)
            amount = base_amount * multiplier * intensity
            
            module_logger.info(f"Dip buying triggered: decline={decline_ratio:.2%}, amount={amount}")
            return amount
        
        return 0
    
    def _calculate_profit_taking(self, current_value: float, current_date: date) -> float:
        """计算盈利撤资金额"""
        if self.last_portfolio_value is None:
            return 0
        
        # 计算盈利比例
        profit_ratio = (current_value - self.last_portfolio_value) / self.last_portfolio_value
        threshold = self.get_strategy_param('profit_taking_threshold', 0.20)
        
        if profit_ratio >= threshold:
            # 检查是否最近已经撤资过
            recent_taking = any(
                record['date'] > current_date - timedelta(days=30)
                for record in self.profit_taking_history
            )
            
            if not recent_taking:
                taking_ratio = self.get_strategy_param('profit_taking_ratio', 0.1)
                amount = current_value * taking_ratio
                
                # 记录撤资历史
                self.profit_taking_history.append({
                    'date': current_date,
                    'amount': amount,
                    'profit_ratio': profit_ratio
                })
                
                module_logger.info(f"Profit taking: profit={profit_ratio:.2%}, amount={amount}")
                return -amount  # 负数表示资金流出
        
        return 0
    
    def _calculate_dividend_reinvestment(self, current_date: date) -> float:
        """计算分红再投资金额（模拟）"""
        # 这里是模拟逻辑，实际应该从数据源获取分红信息
        # 假设每季度末有分红
        if current_date.month in [3, 6, 9, 12] and current_date.day >= 25:
            # 模拟分红金额
            estimated_dividend = 500  # 假设分红500元
            reinvest_ratio = self.get_strategy_param('dividend_reinvest_ratio', 1.0)
            amount = estimated_dividend * reinvest_ratio
            
            if amount > 0:
                module_logger.info(f"Dividend reinvestment: {amount} on {current_date}")
                return amount
        
        return 0
    
    def _apply_monthly_limits(self, cash_flows: Dict[str, float], current_date: date) -> Dict[str, float]:
        """应用月度投资限额"""
        max_monthly = self.get_strategy_param('max_monthly_investment', 5000)
        
        # 计算本月总流入
        total_inflow = sum(amount for amount in cash_flows.values() if amount > 0)
        
        if total_inflow > max_monthly:
            # 按比例缩减
            scale_factor = max_monthly / total_inflow
            for flow_type, amount in cash_flows.items():
                if amount > 0:
                    cash_flows[flow_type] = amount * scale_factor
            
            module_logger.warning(f"Monthly investment limit applied: {total_inflow} -> {max_monthly}")
        
        return cash_flows


class StopLossStrategy(AdvancedCashFlowStrategy):
    """
    止损策略示例
    
    展示如何实现自动止损和资金撤出逻辑。
    """
    
    def __init__(self, params=None):
        super().__init__()
        
        default_params = {
            'stop_loss_threshold': -0.15,    # 15%止损线
            'emergency_threshold': -0.25,    # 25%紧急撤资线
            'partial_stop_ratio': 0.5,       # 部分止损比例
            'emergency_stop_ratio': 0.8,     # 紧急止损比例
        }
        
        if params:
            default_params.update(params)
        
        self.set_strategy_params(default_params)
        self.initial_value = None
        self.stop_loss_triggered = False
    
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        """实现止损逻辑"""
        if self.initial_value is None:
            self.initial_value = current_value
            return {}
        
        cash_flows = {}
        
        # 计算当前损失比例
        loss_ratio = (current_value - self.initial_value) / self.initial_value
        
        stop_loss_threshold = self.get_strategy_param('stop_loss_threshold', -0.15)
        emergency_threshold = self.get_strategy_param('emergency_threshold', -0.25)
        
        if loss_ratio <= emergency_threshold and not self.stop_loss_triggered:
            # 紧急止损
            emergency_ratio = self.get_strategy_param('emergency_stop_ratio', 0.8)
            amount = current_value * emergency_ratio
            cash_flows[CashFlowTypes.EMERGENCY_WITHDRAWAL] = -amount
            self.stop_loss_triggered = True
            
            module_logger.warning(f"Emergency stop loss triggered: loss={loss_ratio:.2%}, withdrawal={amount}")
            
        elif loss_ratio <= stop_loss_threshold and not self.stop_loss_triggered:
            # 部分止损
            partial_ratio = self.get_strategy_param('partial_stop_ratio', 0.5)
            amount = current_value * partial_ratio
            cash_flows[CashFlowTypes.STOP_LOSS_WITHDRAWAL] = -amount
            self.stop_loss_triggered = True
            
            module_logger.warning(f"Stop loss triggered: loss={loss_ratio:.2%}, withdrawal={amount}")
        
        return self.cash_flow_manager.process_cash_flows(cash_flows, current_date)


# 使用示例
if __name__ == "__main__":
    # 创建动态投资策略
    strategy = DynamicInvestmentStrategy({
        'monthly_investment': 2000,
        'dip_buy_threshold': -0.03,  # 3%下跌时加仓
        'max_monthly_investment': 8000
    })
    
    # 模拟一些现金流操作
    from datetime import date
    
    test_date = date(2024, 1, 5)  # 月初
    cash_flows = strategy.get_cash_flows(test_date, 10000, 100000)
    print(f"Cash flows on {test_date}: {cash_flows}")
    
    # 模拟市场下跌
    test_date = date(2024, 1, 15)
    cash_flows = strategy.get_cash_flows(test_date, 10000, 95000)  # 下跌5%
    print(f"Cash flows on {test_date} (market dip): {cash_flows}")
