from capital_strategies import FixedInvestmentBase
from capital_strategies.cash_flow_interface import CashFlowInterface, CashFlowTypes
import logging

module_logger = logging.getLogger(__name__)

class FixedInvestmentStrategy(FixedInvestmentBase, CashFlowInterface):
    """定期定投资金策略，支持年度和月度定投
    """

    # 默认参数
    params = {
        'initial_capital': 100000,  # 初始资本
        'investment_amount': 10000,  # 定投金额
        'investment_frequency': 'y',  # 'y' 或 'm'
        'percents': 20,  # 每个标的最大买入比例
        'fund_val_start': 100.0,  # 初始净值
        'max_positions': None,  # 最大持仓数量，None表示不限制
    }

    def __init__(self, params=None):
        if params:
            self.params.update(params)
        super().__init__(self.params['initial_capital'])
        self.last_investment_year = 0
        self.last_investment_month = 0
        self._last_investment_type = None  # 用于记录最近一次投资的类型
        self.total_investment = 0  # 用于跟踪总投资金额

    def get_max_allocation_percentage(self):
        """获取单个标的可分配的最大资金百分比"""
        return self.params['percents']
        
    def get_max_positions(self):
        """获取最大允许持仓数量"""
        return self.params['max_positions']

    def get_cash_flow(self, data, broker):
        """
        Get cash flow for fixed investment strategy.
        Implements the cash flow interface for periodic investments.
        
        Args:
            data: The current data feed (Backtrader data object)
            broker: The broker instance
            
        Returns:
            tuple: (amount, flow_type) where:
                - amount: Investment amount if it's time to invest, otherwise 0.0
                - flow_type: Type of cash flow ('annual_investment' or 'monthly_investment')
        """
        amount = self.check_periodic_investment(data, broker)
        
        if amount > 0:
            # 根据投资频率确定现金流类型
            flow_type = 'annual_investment' if self.params['investment_frequency'] == 'y' else 'monthly_investment'
            return (amount, flow_type)
            
        return (0.0, None)

    def check_periodic_investment(self, data, broker) -> float:
        """
        Check if a periodic investment should be executed.

        Args:
            data: The current data feed (Backtrader data object)
            broker: The broker instance
        Returns:
            float: The investment amount if it's time to invest, otherwise 0.0
        """
        current_date = data.datetime.date(0)
        if self._should_invest(current_date):
            investment_amount = self.params['investment_amount']
            self.last_investment_year = current_date.year
            self.last_investment_month = current_date.month
            module_logger.info(f"Periodic investment triggered for {current_date}: amount={investment_amount}")
            return investment_amount
        return 0.0

    def _should_invest(self, current_date):
        """判断是否应该进行定投"""
        # module_logger.info(f"------- Checking Investment Decision -------")
        # module_logger.info(f"Current Date: {current_date}")
        # module_logger.info(f"Last Investment Year: {self.last_investment_year}")
        # module_logger.info(f"Last Investment Month: {self.last_investment_month}")
        # module_logger.info(f"Investment Frequency: {self.params['investment_frequency']}")

        if self.last_investment_year == 0:
            self.last_investment_year = current_date.year
            self.last_investment_month = current_date.month
            # module_logger.info("First investment triggered (initialization), should not add cash this time")
            return False
                
        if self.params['investment_frequency'] == 'y':
            should_invest = current_date.year > self.last_investment_year
            # module_logger.info(f"Annual investment check: current_year={current_date.year}, last_year={self.last_investment_year}, should_invest={should_invest}")
            return should_invest
        elif self.params['investment_frequency'] == 'm':
            should_invest = (current_date.year > self.last_investment_year or 
                        (current_date.year == self.last_investment_year and 
                        current_date.month > self.last_investment_month))
            # module_logger.info(f"Monthly investment check: current={current_date.year}-{current_date.month}, last={self.last_investment_year}-{self.last_investment_month}, should_invest={should_invest}")
            return should_invest

    def allocate_capital(self, cash, total_value, data, broker, *args, **kwargs):
        """
        获取一次买入交易的资金分配金额, 本策略基于可用现金计算分配金额，即每次定投固定金额
        """
        return cash * (self.params['percents'] / 100)

    def get_cash_flows(self, current_date, current_cash, current_value):
        """
        统一的现金流接口，返回所有类型的现金流。
        这是新的推荐接口，支持多种现金流类型。

        Args:
            current_date: Current trading date
            current_cash: Available cash in the portfolio
            current_value: Total portfolio value

        Returns:
            dict: Dictionary of {flow_type: amount} for all cash flows
        """
        cash_flows = {}

        # 检查是否需要定投
        if self._should_invest(current_date):
            investment_amount = self.params['investment_amount']
            self.last_investment_year = current_date.year
            self.last_investment_month = current_date.month

            # 根据投资频率确定现金流类型
            if self.params['investment_frequency'] == 'y':
                cash_flows[CashFlowTypes.ANNUAL_INVESTMENT] = investment_amount
                module_logger.info(f"Annual investment triggered on {current_date}, amount: {investment_amount}")
            elif self.params['investment_frequency'] == 'm':
                cash_flows[CashFlowTypes.MONTHLY_INVESTMENT] = investment_amount
                module_logger.info(f"Monthly investment triggered on {current_date}, amount: {investment_amount}")

        # 未来可以在这里添加其他类型的现金流
        # 例如：分红再投资、止损撤资、动态调仓等

        return cash_flows

    def invest_conditionally(self, current_date, current_cash, current_value):
        """
        Legacy method for annual investment (backward compatibility).

        Args:
            current_date: Current trading date
            current_cash: Available cash in the portfolio
            current_value: Total portfolio value

        Returns:
            float: Investment amount if investment should be made, 0 otherwise
        """
        if self.params['investment_frequency'] != 'y':
            return 0

        if self._should_invest(current_date):
            investment_amount = self.params['investment_amount']
            self.last_investment_year = current_date.year
            self.last_investment_month = current_date.month
            module_logger.info(f"Annual investment triggered on {current_date}, amount: {investment_amount}")
            return investment_amount
        return 0

    def invest_monthly(self, current_date, current_cash, current_value):
        """
        Legacy method for monthly investment (backward compatibility).

        Args:
            current_date: Current trading date
            current_cash: Available cash in the portfolio
            current_value: Total portfolio value

        Returns:
            float: Investment amount if investment should be made, 0 otherwise
        """
        if self.params['investment_frequency'] != 'm':
            return 0

        if self._should_invest(current_date):
            investment_amount = self.params['investment_amount']
            self.last_investment_year = current_date.year
            self.last_investment_month = current_date.month
            module_logger.info(f"Monthly investment triggered on {current_date}, amount: {investment_amount}")
            return investment_amount
        return 0
