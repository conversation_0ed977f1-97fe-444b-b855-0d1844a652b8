import logging
import threading
import json
import time
from typing import Any, Dict
from confluent_kafka import Consumer, KafkaError, KafkaException
from abc import ABC, abstractmethod

import sentry_sdk
from data_sources.redis import RedisClient
from utilities.backoff import ExponentialBackoff
from constants import KAFKA_TOPIC
from utilities.time_utils import TimeUtils

module_logger = logging.getLogger(__name__)

class BaseEventPoller(ABC):
    @abstractmethod
    def start(self):
        pass
        
    @abstractmethod
    def stop(self):
        pass
    
    @abstractmethod
    def poll_events(self):
        pass

class KafkaEventPoller(BaseEventPoller):
    def __init__(self, kafka_config, task_runner, portfolio_factory):
        self.kafka_config = kafka_config
        self.task_runner = task_runner
        self.portfolio_factory = portfolio_factory
        self.should_continue = True
        self.backoff = ExponentialBackoff()
        self.consumer = Consumer(self.kafka_config)
        self.consumer.subscribe([KAFKA_TOPIC])

    def start(self):
        # 在新线程中启动循环
        thread = threading.Thread(target=self.poll_events)
        thread.start()

    def poll_events(self):
        while self.should_continue:
            try:
                with sentry_sdk.push_scope() as scope:
                    scope.set_tag("component", "event_poller")
                
                self.consumer = Consumer(self.kafka_config)
                module_logger.info(f"Subscribing to topic: {KAFKA_TOPIC}")
                self.consumer.subscribe([KAFKA_TOPIC])

                while self.should_continue:
                    module_logger.info("Polling for messages...")
                    msg = self.consumer.poll(timeout=30.0)

                    if msg is None:
                        continue
                    if msg.error():
                        if msg.error().code() == KafkaError._PARTITION_EOF:
                            module_logger.info('End of partition reached {0}/{1}'
                                  .format(msg.topic(), msg.partition()))
                        else:
                            raise KafkaException(msg.error())
                    else:
                        self._handle_message(msg.value())
            except KafkaException as e:
                module_logger.error(f"Kafka exception: {e}")
                self.backoff.wait() # 应用退避策略等待
            except Exception as e:
                sentry_sdk.capture_exception(e)
                module_logger.error(f"Unexpected exception: {e}")
                self.backoff.wait() # 应用退避策略等待
            finally:
                self.consumer.close()
                self.backoff.reset()  # 在重新开始新的轮询循环前重置退避策略

    def _handle_message(self, message):
        try:
            job_message = json.loads(message)
            portfolio_config = job_message["portfolio_config"]
            action = job_message["action"]
            job_id = job_message.get("job_id")
            
            if not portfolio_config or not action or not job_id:
                raise ValueError("Missing required fields in job message")
            
            self.portfolio_factory.register_portfolio(portfolio_config)
            
            if action == "create_or_update":
                self.task_runner.handle_message({
                    "code": portfolio_config["code"],
                    "job_id": job_id,
                    'parameters': {'sync_to_s3': True}
                })

        except Exception as e:
            sentry_sdk.capture_exception(e)
            module_logger.error(f"Error processing message: {e}")

    def stop(self):
        self.should_continue = False
        self.consumer.close()

    def __del__(self):
        self.stop()

class RedisEventPoller(BaseEventPoller):
    def __init__(self, redis_config: Dict[str, Any], task_runner, portfolio_factory):
        self.redis_client = RedisClient.get_instance("queue")
        self.task_runner = task_runner
        self.portfolio_factory = portfolio_factory
        self.should_continue = True
        self.backoff = ExponentialBackoff()
        self.queue_name = KAFKA_TOPIC  # 使用相同的队列名称
        
    def start(self):
        thread = threading.Thread(target=self.poll_events)
        thread.start()
        
    def poll_events(self):
        while self.should_continue:
            polling_interval = TimeUtils.get_polling_interval()
            module_logger.info("Polling for messages, interval: %s", polling_interval)
            try:
                with sentry_sdk.push_scope() as scope:
                    scope.set_tag("component", "redis_event_poller")
                                        
                    start_time = time.time()
                    # 使用较短的blpop超时时间（例如1秒）
                    result = self.redis_client.blpop([self.queue_name], timeout=1)
                    
                    if result is not None:
                        _, message = result
                        try:
                            event = json.loads(message)
                            self._handle_message(event)
                            self.backoff.reset()
                        except json.JSONDecodeError as e:
                            sentry_sdk.capture_exception(e)
                            module_logger.error(f"Failed to parse message: {e}")
                    
                    # 计算需要sleep的时间以达到预期的轮询间隔
                    elapsed_time = time.time() - start_time
                    sleep_time = max(0, polling_interval - elapsed_time)
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                        
            except Exception as e:
                sentry_sdk.capture_exception(e)
                module_logger.error(f"Error in poll_events: {e}")
                self.backoff.wait()

    def _handle_message(self, message):
        try:
            portfolio_config = message.get('portfolio_config')
            action = message.get('action')
            job_id = message.get('job_id')

            if not all([portfolio_config, action, job_id]):
                raise ValueError("Missing required fields in job message")
            
            self.portfolio_factory.register_portfolio(portfolio_config)
            
            if action == "create_or_update":
                self.task_runner.handle_message({
                    "code": portfolio_config["code"],
                    "job_id": job_id,
                    'parameters': {'sync_to_s3': True}
                })

        except Exception as e:
            sentry_sdk.capture_exception(e)
            module_logger.error(f"Error processing message: {e}")

    def stop(self):
        self.should_continue = False
        if hasattr(self, 'redis_client'):
            self.redis_client.close()

    def __del__(self):
        self.stop()

class EventPollerFactory:
    @staticmethod
    def create_poller(poller_type: str, config: dict, task_runner, portfolio_factory):
        if poller_type == "kafka":
            return KafkaEventPoller(config, task_runner, portfolio_factory)
        elif poller_type == "redis":
            return RedisEventPoller(config, task_runner, portfolio_factory)
        raise ValueError(f"Unknown poller type: {poller_type}")
