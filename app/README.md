# 策略服务

## 启动服务

因为使用到了Redis缓存，所以需要先Tunnel到Fly的Redis服务：

```bash
fly redis connect
```

数据来源于`invest-ohlc-proxy`服务，所以需要Tunnel到Fly的服务：

```bash
flyctl proxy 8000 -a invest-ohlc-proxy
```

配置好环境变量：

```bash
cp .env.example .env
```

然后修改`.env`文件中的配置。

### V2

- 本地模式：

```bash
python main_v2.py --mode local --code myinvestpilot_us_1
python main_v2.py --mode local --s3 --code myinvestpilot_us_1
python main_v2.py --mode local --video --code myinvestpilot_us_1
```

- 服务器模式：

```bash
python main_v2.py --mode server
```

## 添加投资组合

1. 选择交易策略，如果没有合适的策略，可以自行编写策略；
2. 选择资金策略，如果没有合适的资金策略，可以自行编写资金策略；
3. 在`portfolio_config.json`中添加投资组合配置；

## 存在的问题

1. ~~需要注意的是，一旦投资组合中某个交易标的的行情数据最早的日期超过了投资组合的开始日期，那么整个投资组合的开始日期将被更新为该标的的行情数据最早的日期；~~ 已经修复；
2. ~~目前框架回测处理非交易日存在问题，在非交易日是没有交易信号数据的，假设在09月30日发出卖出信号，正常会在10月1日卖出，但是10月1日至10月7日是国庆假期，所以在10月8日才能卖出，这会导致回测框架在10月8日重复卖出同一标的多次；~~
   1. ~~考虑到目前的组合在收到卖出信号时并没有分批卖出的设计，所以解决的办法是在卖出时检查是否有可卖数量，如果没有则忽略卖出信号；~~
   2. ~~生成的组合交易记录、持仓记录存在重复记录的问题，可能是框架回测过程中重复执行了某些操作；~~
   3. ~~以上错误全部是因为在处理补全数据时，在非交易日上补全了交易数据，导致回测框架在非交易日上执行了交易操作。回测框架判断非交易日的方法是通过判断是否有行情数据，所以在补全数据时，应该只补全交易日的数据；~~ 已经修复；
3. 目前回测框架在做交易资金分配时没有考虑到多个标的的同时买入，所以会使最后部分剩余资金无法买入交易标的。这个影响会导致组合的资金利用率降低，很难达到满仓的状态，进而对组合的收益率产生一定影响；
4. 资金策略中不能设置99%以上的比例，因为在计算资金分配时，有佣金的开销，如果过大会导致订单无法成交；
