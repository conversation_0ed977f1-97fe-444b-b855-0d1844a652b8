import json
import logging
from multiprocessing import Process
from slack_sdk.errors import SlackApiError

from utilities.exceptions import TimeoutException
from utilities.timeout_decorator import timeout
from data_sources import PortfolioDataLoader
from utilities.helpers import json_serial

import sys
import traceback
import sentry_sdk

module_logger = logging.getLogger(__name__)

class TaskRunner:
    def __init__(self, portfolio_factory, slack_client, slack_admin_channel):
        self.portfolio_factory = portfolio_factory
        self.slack_client = slack_client
        self.slack_admin_channel = slack_admin_channel
        
    def _handle_exception(self, e, code, job_id, error_type):
        exc_type, exc_value, exc_traceback = sys.exc_info()
        stack_trace = traceback.format_exception(exc_type, exc_value, exc_traceback)
        stack_trace_str = "".join(stack_trace)
        
        error_message = f"Portfolio {code} {error_type}. Job ID: {job_id}. Error: {str(e)}"
        
        module_logger.error(error_message)
        
        # Post error message and stack trace to Slack
        thread_ts = self.post_slack_message(error_message)
        if thread_ts:
            self.post_slack_message(f"Stack trace:\n```\n{stack_trace_str}\n```", thread_ts=thread_ts)
        
        with sentry_sdk.push_scope() as scope:
            scope.set_extra("stack_trace", stack_trace_str)
            scope.set_extra("portfolio_code", code)
            scope.set_extra("job_id", job_id)
            scope.set_extra("error_type", error_type)
            sentry_sdk.capture_exception(e)

    def handle_message(self, message):
        """
        Handles a message by creating a new process to run the update task.
        """
        module_logger.info(f"Received message: {message}")
        process = Process(target=self.run, args=(message,))
        process.start()
        process.join()

    @timeout(600)  # 10分钟超时
    def run(self, message):
        """
        Executes the portfolio update in a separate process.
        """
        file_handler = None
        code = None
        job_id = None

        try:
            code = message['code']
            job_id = message['job_id']
            parameters = message.get('parameters', {})

            if not code:
                raise ValueError("Portfolio code is required")
            
            with sentry_sdk.push_scope() as scope:
                scope.set_tag("portfolio_code", code)
                scope.set_tag("job_id", job_id)
            
            try:
                portfolio = self.portfolio_factory.get_portfolio(code, PortfolioDataLoader.get_instance(), parameters)
            except Exception as e:
                module_logger.error(f"Failed to get portfolio for code: {code}. Error: {e}")
                raise
            
            # 日志添加临时文件处理器
            file_handler = logging.FileHandler(portfolio.portfolio_log_file)
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            logging.getLogger().addHandler(file_handler)
            
            start_message = f'Start processing {code}...'
            module_logger.info(start_message)
            self.post_slack_message(start_message)

            # 执行回测
            data_collector = portfolio.execute_trades()

            module_logger.info('--- Portfolio Status ---')
            module_logger.info(json.dumps(portfolio.get_portfolio_status(), indent=4, ensure_ascii=False, default=json_serial))
            module_logger.info('------------------------')
            
            if parameters.get('export_video'):
                portfolio.export_portfolio_video(data_collector) 
            elif parameters.get('sync_to_s3'):
                portfolio.sync_to_s3()

            message = f"Portfolio {code} updated successfully. Job ID: {job_id}"
            module_logger.info(message)
            self.post_slack_message(message)
        except TimeoutException as e:
            self._handle_exception(e, code, job_id, "timed out")
        except Exception as e:
            self._handle_exception(e, code, job_id, "failed to update")
        finally:
            # 移除临时文件处理器
            if file_handler:
                logging.getLogger().removeHandler(file_handler)

    def post_slack_message(self, message, thread_ts=None):
        if self.slack_client is None:
            return None
        try:
            response = self.slack_client.chat_postMessage(
                channel=self.slack_admin_channel, 
                text=message,
                thread_ts=thread_ts
            )
            return response['ts']  # 返回消息的时间戳，可用作thread_ts
        except SlackApiError as e:
            module_logger.error(f"Slack API Error: {e.response['error']}")
            return None
