#!/bin/bash
# 信号比较测试工具脚本
# 用于比较原始策略与原语策略的信号输出一致性

set -e

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
# 测试数据目录
DATA_DIR="$SCRIPT_DIR/tests/data"
# 输出目录
OUTPUT_DIR="$SCRIPT_DIR/tests/output/signal_comparison"

# 颜色配置，用于标识输出类型
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 帮助信息
function show_help {
    echo "信号比较测试工具"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                   显示此帮助信息"
    echo "  -d, --data FILE              指定OHLCV数据文件路径"
    echo "  -s, --symbol SYMBOL          指定测试标的代码"
    echo "  -t, --strategy STRATEGY      指定原始策略类名"
    echo "  -c, --config FILE            指定原语策略配置文件路径"
    echo "  -p, --params PARAMS          指定策略参数 (格式: key1=value1,key2=value2)"
    echo "  -o, --output-dir DIR         指定输出目录"
    echo "  -i, --ignore N               忽略前N个数据点"
    echo "  -v, --verbose                启用详细日志"
    echo "  --show-plot                  显示信号比较图表"
    echo ""
    echo "示例:"
    echo "  $0 -d tests/data/SPY.csv -s SPY -t ChandelierExitMAStrategy -c config/portfolio_config_complex_primitive.json"
    echo "  $0 -d tests/data/SPY.csv -s SPY -t ChandelierExitMAStrategy -p n_atr=60,atr_multiplier=4.0,n_ma=250 -c config/portfolio_config_complex_primitive.json"
    echo ""
}

# 检查必要的目录
if [ ! -d "$DATA_DIR" ]; then
    mkdir -p "$DATA_DIR"
    print_message "$BLUE" "创建测试数据目录: $DATA_DIR"
fi

if [ ! -d "$OUTPUT_DIR" ]; then
    mkdir -p "$OUTPUT_DIR"
    print_message "$BLUE" "创建输出目录: $OUTPUT_DIR"
fi

# 默认值
DATA_FILE=""
SYMBOL=""
STRATEGY=""
CONFIG_FILE=""
STRATEGY_PARAMS=""
IGNORE_FIRST=0
VERBOSE=""
SHOW_PLOT=""
OUTPUT_DIR_OVERRIDE=""

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--data)
            DATA_FILE="$2"
            shift 2
            ;;
        -s|--symbol)
            SYMBOL="$2"
            shift 2
            ;;
        -t|--strategy)
            STRATEGY="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -p|--params)
            STRATEGY_PARAMS="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR_OVERRIDE="$2"
            shift 2
            ;;
        -i|--ignore)
            IGNORE_FIRST="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="--verbose"
            shift
            ;;
        --show-plot)
            SHOW_PLOT="--show-plot"
            shift
            ;;
        *)
            print_message "$RED" "错误: 未知选项 $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必要参数
if [ -z "$DATA_FILE" ] || [ -z "$SYMBOL" ] || [ -z "$STRATEGY" ] || [ -z "$CONFIG_FILE" ]; then
    print_message "$RED" "错误: 缺少必要参数"
    show_help
    exit 1
fi

# 检查文件存在
if [ ! -f "$DATA_FILE" ]; then
    print_message "$RED" "错误: 数据文件不存在: $DATA_FILE"
    exit 1
fi

if [ ! -f "$CONFIG_FILE" ]; then
    print_message "$RED" "错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 设置最终输出目录
if [ -n "$OUTPUT_DIR_OVERRIDE" ]; then
    FINAL_OUTPUT_DIR="$OUTPUT_DIR_OVERRIDE"
else
    FINAL_OUTPUT_DIR="$OUTPUT_DIR/$SYMBOL"
    mkdir -p "$FINAL_OUTPUT_DIR"
fi

# 构建命令
CMD="python -m tests.core.signal_comparison_runner \
    --data \"$DATA_FILE\" \
    --symbol \"$SYMBOL\" \
    --strategy \"$STRATEGY\" \
    --config \"$CONFIG_FILE\" \
    --output-dir \"$FINAL_OUTPUT_DIR\" \
    --ignore-first-n $IGNORE_FIRST"

# 添加可选参数
if [ -n "$STRATEGY_PARAMS" ]; then
    CMD="$CMD --strategy-params \"$STRATEGY_PARAMS\""
fi

if [ -n "$VERBOSE" ]; then
    CMD="$CMD $VERBOSE"
fi

if [ -n "$SHOW_PLOT" ]; then
    CMD="$CMD $SHOW_PLOT"
fi

# 执行命令
print_message "$BLUE" "运行信号比较测试..."
print_message "$YELLOW" "$CMD"
cd "$SCRIPT_DIR" && eval "$CMD"

# 输出结果
print_message "$GREEN" "测试完成! 结果保存在: $FINAL_OUTPUT_DIR"
