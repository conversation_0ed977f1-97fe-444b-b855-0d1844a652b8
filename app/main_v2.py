import argparse
import logging
import sys
import threading
import requests
from constants import *
from portfolio_factory_v2 import PortfolioFactory
from task_runner import TaskRunner
from event_poller import EventPollerFactory
from flask import Flask, request
from slack_sdk import WebClient
from dotenv import load_dotenv
from sentry_sdk.integrations.flask import FlaskIntegration

load_dotenv()

# 设置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

for handler in logger.handlers[:]:
    logger.removeHandler(handler)

formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(logging.INFO)
stream_handler.setFormatter(formatter)
logger.addHandler(stream_handler)

# Flask 应用，仅在 Server 模式下使用
app = Flask(__name__)

portfolio_factory = PortfolioFactory()

def run_local(args):
    """
    Local 模式下的操作。
    """
    logger.info("Starting investStrategyService in local mode")
    task_runner = TaskRunner(portfolio_factory, None, None)

    # 加载 config 目录下的所有配置文件
    files_loaded = portfolio_factory.load_all_config_files_from_directory('config')
    logger.info(f"Loaded {files_loaded} portfolio configuration files")
    
    code = args.code or args.code_local or args.code_video

    logger.info(f"Processing portfolio with code: {code}")
    task_runner.handle_message({
            'code': code,
            'job_id': 'LOCAL_JOB',
            'parameters': {
                'sync_to_s3': bool(args.s3),
                'export_video': bool(args.video),
                'start_date': args.start_date,
                'end_date': args.end_date
            }
        })
    logger.info(f"Finished processing portfolio: {code}")

def run_server():
    """
    Server 模式下的初始化和配置。
    """
    logger.info("Starting investStrategyService in server mode")
    
    import sentry_sdk

    sentry_sdk.init(
        dsn=os.getenv("SENTRY_DSN"),
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        traces_sample_rate=1.0,
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=1.0,
        integrations=[
            FlaskIntegration(),
        ],
    )
    logger.info("Sentry initialized")
    
    slack_client = WebClient(token=SLACK_BOT_TOKEN) if SLACK_BOT_TOKEN else None
    task_runner = TaskRunner(portfolio_factory, slack_client, SLACK_ADMIN_CHANNEL)
    logger.info("TaskRunner initialized")

    # 初始化 EventPoller 和 TaskRunner
    poller_factory = EventPollerFactory()
    
    # 创建Redis Poller
    redis_poller = poller_factory.create_poller("redis", None, task_runner, portfolio_factory)
    logger.info("Redis poller created")
    
    # 启动Redis Poller
    redis_poller.start()
    logger.info("Redis poller started")
    
    ENABLE_KAFKA_POLLER = os.getenv("ENABLE_KAFKA_POLLER", "true").lower() == "true"

    if ENABLE_KAFKA_POLLER:
        # Kafka 配置
        kafka_basic_config = {
            'bootstrap.servers': KAFKA_BOOTSTRAP_SERVERS,
            'security.protocol': 'SASL_SSL',
            'sasl.mechanism': 'SCRAM-SHA-256',
            'sasl.username': KAFKA_USERNAME,
            'sasl.password': KAFKA_PASSWORD,
        }

        kafka_producer_config = kafka_basic_config.copy()
        kafka_producer_config['acks'] = 'all'

        kafka_consumer_config = kafka_basic_config.copy()
        kafka_consumer_config['group.id'] = KAFKA_GROUP_ID
        kafka_consumer_config['auto.offset.reset'] = 'earliest'
        
        logger.info("Kafka consumer configured")
        
        kafka_poller = poller_factory.create_poller("kafka", kafka_consumer_config, task_runner, portfolio_factory)
        logger.info("Kafka poller created")
        kafka_poller.start()
        logger.info("Kafka poller started")
    
    # 设置 Flask 路由，用于处理 Slack 命令
    @app.route("/slack/commands", methods=["POST"])
    def handle_slack_commands():
        data = request.form
        command_text = data.get("text", "").strip()
        response_url = data.get("response_url")
        
        if data.get("command") == "/update_portfolio" and command_text:
            portfolio_code = command_text
            # 立即返回初始响应
            threading.Thread(target=process_update, args=(portfolio_code, response_url)).start()
            return "Update command received. Processing will begin shortly.", 200

        return "Command not recognized or invalid.", 200

    def process_update(portfolio_code, response_url):
        try:
            task_runner.handle_message({
                'code': portfolio_code,
                'job_id': 'SLACK_JOB',
                'parameters': {'sync_to_s3': True}
            })
            # 使用 response_url 发送最终结果
            requests.post(response_url, json={
                "text": f"Update for portfolio {portfolio_code} completed successfully."
            })
        except Exception as e:
            error_message = f"Error processing update command for portfolio {portfolio_code}: {str(e)}"
            logger.error(error_message)
            requests.post(response_url, json={"text": error_message})

    # 启动 Flask 应用
    logger.info("Flask app starting...")
    app.run(host='0.0.0.0', port=int(PORT))

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Run the portfolio management system in local or server mode.')
    parser.add_argument('--mode', choices=['local', 'server'], required=True, help='The mode to run the application in.')
    parser.add_argument('--s3', action='store_true', help='Sync the portfolio to S3.')
    parser.add_argument('--video', action='store_true', help='Export the portfolio to video.')
    parser.add_argument('--code', help='Portfolio code to update.')
    parser.add_argument('--start-date', help='Start date for portfolio (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='End date for portfolio (YYYY-MM-DD)')
    args = parser.parse_args()
    
    logger.info(f"investStrategyService starting in {args.mode} mode")

    if args.mode == 'local':
        run_local(args)
    elif args.mode == 'server':
        run_server()
    
    logger.info("investStrategyService shutting down")
