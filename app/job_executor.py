import logging
import uuid
from confluent_kafka import Producer
import json
from datetime import datetime
import schedule
import time
import threading

import sentry_sdk

from constants import KAFKA_TOPIC

module_logger = logging.getLogger(__name__)

class JobExecutor:
    def __init__(self, kafka_config, portfolio_configs):
        self.producer = Producer(kafka_config)
        self.portfolio_configs = portfolio_configs
        self.topic_name = KAFKA_TOPIC

    def start(self):
        """
        初始化定时任务并开始循环。
        """
        thread = threading.Thread(target=self.start_scheduler_loop)
        thread.start()

    def send_message(self, message):
        """
        向 Kafka 发送消息。
        """
        try:
            with sentry_sdk.push_scope() as scope:
                scope.set_tag("component", "job_executor")
                scope.set_extra("kafka_message", message)
            
            self.producer.produce(self.topic_name, json.dumps(message).encode('utf-8'))
            self.producer.flush()
        except Exception as e:
            sentry_sdk.capture_exception(e)
            module_logger.info(f"Failed to send message: {e}")

    def construct_message(self, portfolio_code, action, parameters=None):
        """
        构造 Kafka 消息。
        """
        job_message = {
            "job_id": str(uuid.uuid4()),
            "type": "portfolio_update",
            "timestamp": datetime.now().isoformat(),
            "payload": {
                "portfolio_code": portfolio_code,
                "action": action,
                "parameters": parameters or {}
            }
        }
        return job_message

    def schedule_updates(self):
        """
        根据组合配置中的 update_time 安排定时任务。
        """
        for code, portfolio in self.portfolio_configs.items():
            if 'update_time' in portfolio:
                schedule_time = portfolio['update_time']
                
                # 安排定时任务
                schedule.every().day.at(schedule_time).do(self.trigger_update, code)

                module_logger.info(f"Scheduled update for portfolio: {code} at {schedule_time}")
                
    def trigger_update(self, portfolio_code, parameters=None):
        """
        触发组合更新的定时任务。
        """
        module_logger.info(f"Triggering update for portfolio: {portfolio_code}")
        
        if parameters is not None:
            parameters.update({"sync_to_s3": True, "export_video": False})
        else:
            parameters = {"sync_to_s3": True, "export_video": False}
            
        message = self.construct_message(portfolio_code, 'update', parameters)
        self.send_message(message)
        module_logger.info(f"Scheduled update for portfolio: {portfolio_code}")

    def start_scheduler_loop(self):
        """
        启动定时任务的循环。
        """
        self.schedule_updates()
        module_logger.info("Starting scheduler loop...")
        while True:
            schedule.run_pending()
            time.sleep(1)
