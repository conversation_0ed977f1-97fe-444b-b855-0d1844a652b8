#!/bin/bash

# Check if baseline date is provided
if [ -z "$1" ]; then
    echo "Error: Please provide a baseline date (YYYY-MM-DD)"
    echo "Usage: $0 <baseline_date>"
    exit 1
fi

BASELINE_DATE=$1

# Function to cleanup on script exit
cleanup() {
    echo "Cleaning up..."
    if [ ! -z "$REDIS_CONNECT_PID" ]; then
        kill $REDIS_CONNECT_PID 2>/dev/null || true
    fi
    if [ ! -z "$OHLC_PROXY_PID" ]; then
        kill $OHLC_PROXY_PID 2>/dev/null || true
    fi
    pkill flyctl 2>/dev/null || true
}

# Set up cleanup trap
trap cleanup EXIT

echo "Starting required services..."

# Maximum number of retries
MAX_RETRIES=6
RETRY_INTERVAL=5

# Start Redis connection
echo "Starting Redis connection..."
$(dirname $PWD)/connect_redis.exp &
REDIS_CONNECT_PID=$!

# Start OHLC proxy
echo "Starting OHLC proxy..."
flyctl proxy 8000 -a invest-ohlc-proxy &
OHLC_PROXY_PID=$!

# Function to check if a service is ready
wait_for_service() {
    local host=$1
    local port=$2
    local service=$3
    local retries=0

    echo "Waiting for $service to be ready..."
    while ! nc -z $host $port >/dev/null 2>&1; do
        retries=$((retries + 1))
        if [ $retries -ge $MAX_RETRIES ]; then
            echo "Error: $service failed to start after $MAX_RETRIES attempts"
            return 1
        fi
        echo "Waiting for $service... (attempt $retries/$MAX_RETRIES)"
        sleep $RETRY_INTERVAL
    done
    echo "$service is ready"
    return 0
}

echo "Checking services..."

# Wait for services to be ready
if ! wait_for_service localhost 16379 "Redis connection"; then
    echo "Failed to connect to Redis"
    exit 1
fi

if ! wait_for_service localhost 8000 "OHLC proxy"; then
    echo "Failed to connect to OHLC proxy"
    exit 1
fi

echo "Services check passed."

# Define portfolios for baseline
PORTFOLIOS=(
    "myinvestpilot_us_1"
    "myinvestpilot_cn_1"
    "myinvestpilot_cc_1"
    "myinvestpilot_us_dip_1"
    "myinvestpilot_us_dip_2"
    "myinvestpilot_market_filtered"
    "myinvestpilot_market_trend"
    "us_etf_momentum_rotation"
)

# Execute backtests
for portfolio in "${PORTFOLIOS[@]}"; do
    echo "Running backtest for $portfolio..."
    python main_v2.py --mode local --code $portfolio --end-date "$BASELINE_DATE" || {
        echo "Error: Failed to run backtest for $portfolio"
        exit 1
    }
done

# Python script to extract portfolio_status to baseline CSV files
BASELINE_DATE_ENV="$BASELINE_DATE" python - << EOF
import os
import sqlite3
import pandas as pd
import json
from datetime import datetime

def export_table_to_csv(db_path, table_name, output_dir):
    """Extract table from SQLite database to CSV file."""
    if not os.path.exists(db_path):
        print(f"Warning: Database file not found: {db_path}")
        return

    conn = sqlite3.connect(db_path)
    query = f"SELECT * FROM {table_name}"
    df = pd.read_sql_query(query, conn)
    conn.close()

    os.makedirs(output_dir, exist_ok=True)
    csv_path = os.path.join(output_dir, f"{table_name}.csv")
    df.to_csv(csv_path, index=False)
    print(f"Exported {table_name} to {csv_path}")

PORTFOLIOS = [
    "myinvestpilot_us_1",
    "myinvestpilot_cn_1",
    "myinvestpilot_cc_1",
    "myinvestpilot_us_dip_1",
    "myinvestpilot_us_dip_2",
    "myinvestpilot_market_filtered",
    "myinvestpilot_market_trend",
    "us_etf_momentum_rotation"
]

print("\nExtracting baseline data...")
for portfolio_id in PORTFOLIOS:
    print(f"\nProcessing {portfolio_id}...")

    # Create baseline directory
    baseline_dir = os.path.join("tests", "baseline", portfolio_id)
    os.makedirs(baseline_dir, exist_ok=True)

    # Export portfolio_status
    portfolio_db = f"data/{portfolio_id}/{portfolio_id}_portfolio.db"
    export_table_to_csv(portfolio_db, "portfolio_status", baseline_dir)

    # Save metadata
    metadata = {
        "portfolio_id": portfolio_id,
        "generation_time": datetime.now().isoformat(),
        "tables": ["portfolio_status"],
        "baseline_date": os.environ["BASELINE_DATE_ENV"]
    }

    with open(os.path.join(baseline_dir, "metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)

print("\nBaseline data generation complete.")
EOF

echo "Done."
