#!/bin/bash
set -e  # Exit on any error

# Function to setup environment
setup_environment() {
    # If local .env file exists, use it
    if [ -f ".env" ]; then
        echo "Using existing .env file..."
        ENV_FILE=".env"
    else
        echo "No .env file found, creating .env.test..."
        cat > .env.test << EOL
FLY_UPSTASH_REDIS_HOST=localhost
FLY_UPSTASH_REDIS_PORT=16379
FLY_UPSTASH_REDIS_PASSWORD=${FLY_UPSTASH_REDIS_PASSWORD}
INVEST_OHLC_PROXY_HOST=http://localhost:8000
EOL
        ENV_FILE=".env.test"
    fi
}

# Function to cleanup on script exit
cleanup() {
    echo "Cleaning up integration test environment..."
    if [ ! -z "$REDIS_CONNECT_PID" ]; then
        kill $REDIS_CONNECT_PID 2>/dev/null || true
    fi
    if [ ! -z "$OHLC_PROXY_PID" ]; then
        kill $OHLC_PROXY_PID 2>/dev/null || true
    fi
    pkill flyctl 2>/dev/null || true
    # Only remove .env.test if we created it
    if [ "$ENV_FILE" = ".env.test" ]; then
        rm -f .env.test
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Setup environment
setup_environment

echo "Setting up integration test environment..."

# Maximum number of retries
MAX_RETRIES=6
RETRY_INTERVAL=5

# Start Redis connection
echo "Starting Redis connection for integration tests..."
$(dirname $PWD)/connect_redis.exp &
REDIS_CONNECT_PID=$!

# Start OHLC proxy
echo "Starting OHLC proxy for integration tests..."
flyctl proxy 8000 -a invest-ohlc-proxy &
OHLC_PROXY_PID=$!

# Function to check if a service is ready
wait_for_service() {
    local host=$1
    local port=$2
    local service=$3
    local retries=0
    
    echo "Waiting for $service to be ready..."
    while ! nc -z $host $port >/dev/null 2>&1; do
        retries=$((retries + 1))
        if [ $retries -ge $MAX_RETRIES ]; then
            echo "Error: $service failed to start after $MAX_RETRIES attempts"
            return 1
        fi
        echo "Waiting for $service... (attempt $retries/$MAX_RETRIES)"
        sleep $RETRY_INTERVAL
    done
    echo "$service is ready"
    return 0
}

echo "Checking integration test services..."

# Wait for services to be ready
if ! wait_for_service localhost 16379 "Redis connection"; then
    echo "Failed to connect to Redis"
    exit 1
fi

if ! wait_for_service localhost 8000 "OHLC proxy"; then
    echo "Failed to connect to OHLC proxy"
    exit 1
fi

echo "Integration test environment ready."

# Define portfolios to test
PORTFOLIOS=(
    "myinvestpilot_us_1"
    "myinvestpilot_cn_1"
    "myinvestpilot_cc_1"
    "myinvestpilot_us_dip_1"
    "myinvestpilot_us_dip_2"
    myinvestpilot_cn_1_primitive
    myinvestpilot_us_1_primitive
    "myinvestpilot_market_filtered"
    "myinvestpilot_market_trend"
    "us_etf_momentum_rotation"
)

# Read baseline dates from metadata
echo "Reading baseline dates for integration tests..."
eval "$(python tests/utils/read_baseline_dates.py)"

# Execute backtests
for portfolio in "${PORTFOLIOS[@]}"; do
    # Get the baseline date for this portfolio
    eval "baseline_date=\$${portfolio}_BASELINE_DATE"
    if [ -z "$baseline_date" ]; then
        echo "Warning: No baseline date found for $portfolio, skipping..."
        continue
    fi
    
    echo "----------------------------------------"
    echo "Processing portfolio: $portfolio"
    echo "Baseline date: $baseline_date"
    
    # Check data directory before backtest
    echo "Data directory before backtest:"
    ls -la data/$portfolio/ 2>/dev/null || echo "Directory does not exist"
    
    echo "Running backtest..."
    env $(cat $ENV_FILE) python main_v2.py --mode local --code $portfolio \
        --end-date "$baseline_date" || {
        echo "Error: Failed to run backtest for $portfolio"
        exit 1
    }
    
    # Check data directory after backtest
    echo "Data directory after backtest:"
    ls -la data/$portfolio/ || echo "Directory does not exist"
    
    # Verify portfolio database exists
    if [ ! -f "data/$portfolio/${portfolio}_portfolio.db" ]; then
        echo "Warning: Portfolio database was not generated!"
    else
        echo "Portfolio database generated successfully."
    fi
    echo "----------------------------------------"
done

# Run integration tests
echo "Running integration tests..."
env $(cat $ENV_FILE) pytest tests/integration/ -v || {
    echo "Error: Integration tests failed"
    exit 1
}

echo "All integration tests passed successfully ✓"
