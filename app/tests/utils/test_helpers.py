import pandas as pd
import numpy as np
from datetime import datetime

class MockMinimalDataLoader:
    """提供最小化测试数据的加载器

    这个加载器生成可预测的测试数据，用于单元测试和集成测试。它生成三种不同的价格模式：
    1. SPY类型：上升趋势（100 -> 120）
    2. QQQ类型：下降趋势（400 -> 350）
    3. 其他：震荡趋势（以100为中心，±10振幅）

    所有数据都包含标准的OHLCV格式，并使用大写列名以匹配策略期望：
    - Date: 日期列，作为索引
    - Open: 开盘价 = 收盘价 * 0.99
    - High: 最高价 = 收盘价 * 1.02
    - Low: 最低价 = 收盘价 * 0.97
    - Close: 收盘价（根据趋势生成）
    - Volume: 随机生成的成交量（1000-10000）

    用法示例：
    ```python
    loader = MockMinimalDataLoader()
    spy_data = loader.get_price_data("SPY")  # 获取上升趋势数据
    qqq_data = loader.get_price_data("QQQ")  # 获取下降趋势数据
    ```
    """

    def __init__(self):
        self.start_date = datetime(2022, 1, 1)
        # Extend data period to 300 days for better indicator calculation
        self.end_date = self.start_date + pd.Timedelta(days=299)

    def get_price_data(self, symbol, start_date=None, end_date=None):
        """返回用于测试的价格数据集

        Args:
            symbol (str): 股票代码，不同代码生成不同的价格模式
                - SPY开头：上升趋势
                - QQQ开头：下降趋势
                - 其他：震荡趋势
            start_date (datetime, optional): 开始日期，默认为 2022-01-01
            end_date (datetime, optional): 结束日期，默认为 2022-01-30

        Returns:
            pd.DataFrame: 包含OHLCV数据的DataFrame，列名使用大写
        """
        # 创建30天的数据
        dates = pd.date_range(self.start_date, self.end_date)
        n = len(dates)

        # 根据股票代码生成不同的价格模式
        if symbol.startswith("SPY"):
            # 上升趋势
            close_prices = np.linspace(100, 120, n)
        elif symbol.startswith("QQQ"):
            # 下降趋势
            close_prices = np.linspace(400, 350, n)
        else:
            # 震荡趋势
            close_prices = 100 + 10 * np.sin(np.linspace(0, 2*np.pi, n))

        # 生成OHLC数据 - 使用大写列名以匹配上游服务的变化
        data = pd.DataFrame({
            'Date': dates,
            'Open': close_prices * 0.99,
            'High': close_prices * 1.02,
            'Low': close_prices * 0.97,
            'Close': close_prices,
            'Volume': np.random.randint(1000, 10000, n)
        })

        return data

    def get_symbols_data(self, symbols, start_date=None, end_date=None):
        """返回多个股票的数据"""
        result = {}
        for symbol in symbols:
            result[symbol] = self.get_price_data(symbol, start_date, end_date)
        return result

    def get_market_index(self, symbol, start_date=None, end_date=None):
        """返回模拟的市场指数数据

        Args:
            symbol (str): 指数代码，例如 '000300.SH'
            start_date (datetime, optional): 开始日期
            end_date (datetime, optional): 结束日期

        Returns:
            pd.DataFrame: 以Date为索引的OHLCV数据，使用大写列名以匹配测试代码期望
        """
        # Get data with uppercase columns
        df = self.get_price_data(symbol, start_date, end_date)
        # 设置Date作为索引
        df.set_index('Date', inplace=True)
        return df

    def get_qfq_close_price(self, symbol, end_date=None):
        """获取前复权数据，用于生成交易信号

        此方法返回完整的OHLCV数据以支持技术指标计算。数据已设置Date作为索引。

        Args:
            symbol (str): 股票代码
            end_date (datetime, optional): 结束日期

        Returns:
            pd.DataFrame: 包含所有OHLCV数据的DataFrame，以Date为索引
        """
        df = self.get_price_data(symbol, None, end_date)
        if df.empty:
            return pd.DataFrame()
        # 保留所有OHLCV数据以支持技术指标计算
        result = df.copy()
        result.set_index('Date', inplace=True)
        return result

    def get_market_data(self, indicator_code, start_date=None, end_date=None):
        """获取市场指标数据，用于市场指标策略

        此方法返回模拟的市场指标数据，如VIX、SPX等。

        Args:
            indicator_code (str): 指标代码，如'VIX'、'SPX'等
            start_date (datetime, optional): 开始日期
            end_date (datetime, optional): 结束日期

        Returns:
            pd.DataFrame: 包含市场指标数据的DataFrame，以Date为索引
        """
        # 创建日期范围
        dates = pd.date_range(self.start_date, self.end_date)
        n = len(dates)

        # 根据指标代码生成不同的数据模式
        if indicator_code == 'VIX':
            # VIX通常在10-30之间波动，偶尔有尖峰
            base = 20 + 10 * np.sin(np.linspace(0, 4*np.pi, n))
            # 添加一些随机波动和尖峰
            noise = np.random.normal(0, 2, n)
            spikes = np.zeros(n)
            spike_indices = np.random.choice(range(n), size=5, replace=False)
            spikes[spike_indices] = np.random.uniform(10, 20, 5)
            values = base + noise + spikes
            # 确保所有值都是正数
            values = np.maximum(values, 5)
        elif indicator_code == 'SPX':
            # S&P 500指数，上升趋势
            values = np.linspace(4000, 4500, n) + np.random.normal(0, 50, n)
        else:
            # 默认模式：小幅波动
            values = 100 + 5 * np.sin(np.linspace(0, 2*np.pi, n)) + np.random.normal(0, 1, n)

        # 创建DataFrame
        data = pd.DataFrame({
            'Date': dates,
            'Open': values * 0.99,
            'High': values * 1.02,
            'Low': values * 0.97,
            'Close': values,
            'Volume': np.random.randint(1000, 10000, n)
        })

        # 设置Date为索引
        data.set_index('Date', inplace=True)

        return data

    def get_trading_days(self, start_date, end_date, market="US"):
        """返回指定时间段内的交易日
        
        Args:
            start_date (str): 开始日期，格式 'YYYY-MM-DD'
            end_date (str): 结束日期，格式 'YYYY-MM-DD'
            market (str): 市场类型，可选值："US"、"CN"、"CRYPTO"，默认为"US"
            
        Returns:
            pd.DatetimeIndex: 包含所有交易日的DatetimeIndex对象
        """
        # 转换字符串日期为datetime对象
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)
            
        # 确保日期在合理范围内
        start_date = max(start_date, self.start_date)
        end_date = min(end_date, self.end_date)
        
        # 生成工作日日期序列（排除周末）
        trading_days = pd.date_range(start=start_date, end=end_date, freq='B')
        
        return trading_days

class EmptyDataLoader:
    """返回空数据的加载器，用于测试错误处理

    该加载器返回结构完整但仅包含零值的数据，用于测试系统在极端情况下的表现。
    返回的数据包含：
    - 正确的列结构（Date, OHLCV） - 使用大写列名
    - 单行全零数据
    - 正确的日期索引

    主要用途：
    1. 测试策略对无效/空数据的处理能力
    2. 验证系统的错误处理机制
    3. 确保在边缘情况下系统不会崩溃

    用法示例：
    ```python
    loader = EmptyDataLoader()
    empty_data = loader.get_price_data("ANY_SYMBOL")  # 返回带有零值的数据框
    ```

    注意：
    - 所有方法都返回结构完整的数据对象，而不是None
    - 时间戳固定为2022-01-01
    - 所有数值都为0
    """

    def get_price_data(self, symbol, start_date=None, end_date=None):
        """返回空的DataFrame，但包含所需的列（大写）"""
        return pd.DataFrame(columns=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])

    def get_symbols_data(self, symbols, start_date=None, end_date=None):
        """返回空的数据字典"""
        result = {}
        for symbol in symbols:
            result[symbol] = self.get_price_data(symbol) # Use get_price_data to get empty DF with columns
        return result

    def get_market_index(self, symbol, start_date=None, end_date=None):
        """返回空的市场指数数据"""
        df = self.get_price_data(symbol) # Use get_price_data to get empty DF with columns
        df.set_index('Date', inplace=True)
        return df

    def get_qfq_close_price(self, symbol, end_date=None):
        """返回空的前复权价格数据"""
        df = self.get_price_data(symbol) # Use get_price_data to get empty DF with columns
        df.set_index('Date', inplace=True)
        return df

    def get_market_data(self, indicator_code, start_date=None, end_date=None):
        """返回空的市场指标数据"""
        df = self.get_price_data(indicator_code) # Use get_price_data to get empty DF with columns
        df.set_index('Date', inplace=True)
        return df

    def get_trading_days(self, start_date, end_date, market="US"):
        """返回空的交易日序列
        
        Args:
            start_date (str): 开始日期，格式 'YYYY-MM-DD'
            end_date (str): 结束日期，格式 'YYYY-MM-DD'
            market (str): 市场类型，可选值："US"、"CN"、"CRYPTO"，默认为"US"
            
        Returns:
            pd.DatetimeIndex: 空的DatetimeIndex
        """
        # 生成一个只包含起始日期的序列，确保有至少一个数据点
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
            
        # 返回只包含一个日期的序列
        return pd.DatetimeIndex([start_date])
