"""测试辅助工具包

此包提供测试所需的工具类和辅助函数：

1. 数据加载器 (test_helpers.py)
   - MockMinimalDataLoader: 提供可预测的测试数据
   - EmptyDataLoader: 提供零值数据用于健壮性测试

2. 基线数据读取 (read_baseline_dates.py)
   - 用于集成测试的基线数据处理

使用示例:
```python
from tests.utils.test_helpers import MockMinimalDataLoader
from tests.utils.read_baseline_dates import get_baseline_date

# 使用模拟数据加载器
loader = MockMinimalDataLoader()
data = loader.get_price_data("SPY")

# 读取基线数据日期
date = get_baseline_date("portfolio_id")
```

注意：测试中应优先使用MockMinimalDataLoader而非真实数据源，以确保测试的可预测性和稳定性。
"""

from .test_helpers import MockMinimalDataLoader, EmptyDataLoader
from .read_baseline_dates import get_baseline_date

__all__ = ['MockMinimalDataLoader', 'EmptyDataLoader', 'get_baseline_date']
