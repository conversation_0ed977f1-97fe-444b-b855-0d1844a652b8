#!/usr/bin/env python
import os
import json

def get_baseline_date(portfolio_id):
    """Get baseline date from portfolio's metadata.json.

    Args:
        portfolio_id (str): Portfolio identifier

    Returns:
        str or None: Baseline date if found, None if not found
    """
    metadata_path = os.path.join("tests", "baseline", portfolio_id, "metadata.json")
    if not os.path.exists(metadata_path):
        print(f"Warning: No metadata found for {portfolio_id}")
        return None

    with open(metadata_path) as f:
        metadata = json.load(f)
    baseline_date = metadata.get("baseline_date", "")
    # Handle both quoted and unquoted date formats
    return baseline_date.strip("'\"")

def main():
    """Main function to print baseline dates as shell export commands."""
    portfolios = [
        "myinvestpilot_us_1",
        "myinvestpilot_cn_1",
        "myinvestpilot_cc_1",
        "myinvestpilot_us_dip_1",
        "myinvestpilot_us_dip_2",
        "myinvestpilot_cn_1_primitive",
        "myinvestpilot_us_1_primitive",
        "myinvestpilot_market_filtered",
        "myinvestpilot_market_trend",
        "us_etf_momentum_rotation"
    ]

    for portfolio_id in portfolios:
        baseline_date = get_baseline_date(portfolio_id)
        if baseline_date:
            print(f"export {portfolio_id}_BASELINE_DATE='{baseline_date}'")
        else:
            print(f"export {portfolio_id}_BASELINE_DATE=''")

if __name__ == "__main__":
    main()
