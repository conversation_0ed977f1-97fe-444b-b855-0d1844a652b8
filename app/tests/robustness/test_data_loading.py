import pytest
import datetime
from tests.utils.test_helpers import MockMinimalDataLoader, EmptyDataLoader

def test_invalid_symbol_handling():
    """测试无效股票代码处理

    期望行为：
    - 对于无效的股票代码，系统应返回空数据而不是崩溃
    """
    loader = MockMinimalDataLoader()
    
    try:
        data = loader.get_price_data("INVALID_SYMBOL_XYZABC")
        assert data is not None, "应该返回一个DataFrame，即使是空的"
    except Exception as e:
        pytest.fail(f"无效股票代码导致崩溃: {str(e)}")

def test_future_date_handling():
    """测试未来日期处理
    
    期望行为：
    - 对于未来日期的请求，系统应返回到当前为止的数据
    - 不应因未来日期而崩溃
    """
    loader = MockMinimalDataLoader()
    future_date = datetime.datetime.now() + datetime.timedelta(days=365)
    
    try:
        data = loader.get_price_data("SPY", end_date=future_date)
        assert data is not None, "应该返回一个DataFrame"
        assert not data.empty, "应该返回历史数据"
    except Exception as e:
        pytest.fail(f"未来日期导致崩溃: {str(e)}")

def test_empty_data_handling():
    """测试空数据处理
    
    期望行为：
    - 当数据为空时，系统应该抛出适当的异常
    - 异常信息应该清晰明确
    """
    from portfolio_factory_v2 import PortfolioFactory
    
    # 使用返回空数据的加载器
    data_loader = EmptyDataLoader()
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")
    
    # 创建投资组合
    try:
        portfolio = factory.get_portfolio("myinvestpilot_us_1", data_loader)
        assert portfolio is not None, "应该能创建投资组合对象"
    except Exception as e:
        pytest.fail(f"创建投资组合失败: {str(e)}")

    # 执行回测 - 期望因数据为空而引发IndexError
    with pytest.raises(IndexError) as exc_info:
        portfolio.execute_trades()
    
    # 验证异常类型是否符合预期
    assert isinstance(exc_info.value, IndexError), "空数据应引发IndexError"
    assert "out of bounds" in str(exc_info.value), "异常信息应包含'out of bounds'"
