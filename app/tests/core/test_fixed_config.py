#!/usr/bin/env python
"""
测试修复后的吊灯止损策略配置
"""

import pandas as pd
import numpy as np
import sys
import os
import json
from pathlib import Path
import argparse

# 添加app目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from components.registry import ComponentRegistry
from components.signal_evaluator import SignalEvaluator
from trade_strategies.chandelier_exit_ma_strategy import ChandelierExitMAStrategy

# 设置日志
import logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_config_comparison(data_path, config_path):
    """测试修复后的配置文件NaN处理"""
    logger.info(f"加载数据: {data_path}")
    data = pd.read_csv(data_path, parse_dates=['Date'], index_col='Date')
    
    # 1. 使用原始策略计算指标和信号
    original_strategy = ChandelierExitMAStrategy()
    original_strategy.set_data("TEST", data)
    original_signals = original_strategy.generate_signals()
    
    # 2. 使用原语策略计算信号
    logger.info(f"使用配置: {config_path}")
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    registry = ComponentRegistry()
    evaluator = SignalEvaluator(registry)
    primitive_signals = evaluator.evaluate_for_symbol("TEST", config, data)
    
    # 3. 添加原语信号到原始结果DataFrame中，方便对比
    original_signals['primitive_signal'] = primitive_signals
    
    # 4. 统计并显示信号分布
    logger.info("--- 信号分布统计 ---")
    logger.info("原始策略信号分布:")
    logger.info(original_signals['signal'].value_counts())
    
    logger.info("原语策略信号分布:")
    logger.info(original_signals['primitive_signal'].value_counts())
    
    # 5. 计算一致性指标
    same_signals = (original_signals['signal'] == original_signals['primitive_signal'])
    consistency = same_signals.mean() * 100
    logger.info(f"整体信号一致性: {consistency:.2f}%")
    
    # 6. 找出信号不同的位置
    diff_mask = ~same_signals
    diff_count = diff_mask.sum()
    
    if diff_count > 0:
        logger.info("--- 信号不一致的位置 ---")
        diff_percent = (diff_count / len(original_signals)) * 100
        logger.info(f"信号不一致的行数: {diff_count} ({diff_percent:.2f}%)")
        
        # 显示前5个不一致的行
        diff_rows = original_signals[diff_mask].iloc[:5]
        logger.info("前5个不一致的行:")
        
        # 添加ma和chandelier_stop的NaN信息
        diff_rows['ma_isna'] = diff_rows['ma'].isna()
        diff_rows['chandelier_isna'] = diff_rows['chandelier_stop'].isna()
        
        logger.info(diff_rows[['signal', 'primitive_signal', 'close', 'ma', 'chandelier_stop', 'ma_isna', 'chandelier_isna']])
    else:
        logger.info("所有信号完全一致！")
    
    # 7. 检查NaN区域
    logger.info("--- NaN处理分析 ---")
    
    # 两个指标都是NaN的区域
    ma_nan_mask = original_signals['ma'].isna()
    chandelier_nan_mask = original_signals['chandelier_stop'].isna()
    both_nan_mask = ma_nan_mask & chandelier_nan_mask
    both_nan_count = both_nan_mask.sum()
    
    if both_nan_count > 0:
        logger.info(f"两个指标都有NaN的行数: {both_nan_count}")
        # 在NaN区域的信号一致性
        nan_consistency = (original_signals.loc[both_nan_mask, 'signal'] == 
                         original_signals.loc[both_nan_mask, 'primitive_signal']).mean() * 100
        logger.info(f"NaN区域信号一致性: {nan_consistency:.2f}%")
    
    # 8. 检查有效区域
    valid_mask = ~ma_nan_mask & ~chandelier_nan_mask
    valid_count = valid_mask.sum()
    
    if valid_count > 0:
        logger.info(f"两个指标都有效的行数: {valid_count}")
        valid_consistency = (original_signals.loc[valid_mask, 'signal'] == 
                           original_signals.loc[valid_mask, 'primitive_signal']).mean() * 100
        logger.info(f"有效区域信号一致性: {valid_consistency:.2f}%")
    
    # 9. 检查买卖信号
    buy_mask = original_signals['signal'] == 'B'
    buy_count = buy_mask.sum()
    if buy_count > 0:
        primitive_buy_mask = original_signals['primitive_signal'] == 'B'
        primitive_buy_count = primitive_buy_mask.sum()
        logger.info(f"原始策略买入信号数: {buy_count}")
        logger.info(f"原语策略买入信号数: {primitive_buy_count}")
        
        buy_consistency = (original_signals.loc[buy_mask, 'primitive_signal'] == 'B').mean() * 100
        logger.info(f"买入信号一致性: {buy_consistency:.2f}%")
    
    sell_mask = original_signals['signal'] == 'S'
    sell_count = sell_mask.sum()
    if sell_count > 0:
        primitive_sell_mask = original_signals['primitive_signal'] == 'S'
        primitive_sell_count = primitive_sell_mask.sum()
        logger.info(f"原始策略卖出信号数: {sell_count}")
        logger.info(f"原语策略卖出信号数: {primitive_sell_count}")
        
        sell_consistency = (original_signals.loc[sell_mask, 'primitive_signal'] == 'S').mean() * 100
        logger.info(f"卖出信号一致性: {sell_consistency:.2f}%")
    
    # 10. 保存结果到CSV
    result_path = Path(__file__).parent.parent / "output" / "fixed_config_results.csv"
    os.makedirs(result_path.parent, exist_ok=True)
    original_signals.to_csv(result_path)
    logger.info(f"结果已保存到: {result_path}")
    
    return consistency == 100.0  # 返回是否完全一致

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='测试原语配置文件')
    parser.add_argument('--config', type=str, default='../data/chandelier_exit_ma_simple_v2.json',
                        help='配置文件路径')
    parser.add_argument('--data', type=str, default='../data/AAPL_synthetic_600d.csv',
                        help='数据文件路径')
    
    args = parser.parse_args()
    
    config_path = Path(__file__).parent.parent / Path(args.config).relative_to("..") if args.config.startswith("../") else args.config
    data_path = Path(__file__).parent.parent / Path(args.data).relative_to("..") if args.data.startswith("../") else args.data
    
    if not data_path.exists():
        logger.error(f"找不到测试数据: {data_path}")
        sys.exit(1)
        
    if not config_path.exists():
        logger.error(f"找不到配置文件: {config_path}")
        sys.exit(1)
    
    success = run_config_comparison(data_path, config_path)
    sys.exit(0 if success else 1)  # 成功时返回0，失败时返回1
