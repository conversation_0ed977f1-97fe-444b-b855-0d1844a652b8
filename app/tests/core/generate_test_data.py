import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def generate_synthetic_price_data(days=600, symbol="AAPL", volatility=0.015, trend=0.0002):
    """生成合成价格数据，包含趋势和波动性"""
    # 设置随机种子以确保可复现
    np.random.seed(42)
    
    # 创建日期范围（跳过周末）
    start_date = datetime(2022, 1, 1)
    dates = []
    current_date = start_date
    
    while len(dates) < days:
        # 跳过周末 (5=周六, 6=周日)
        if current_date.weekday() < 5:
            dates.append(current_date)
        current_date += timedelta(days=1)
    
    # 生成收盘价 - 包含趋势和随机波动
    start_price = 150.0  # 起始价格
    price_changes = np.random.normal(trend, volatility, days)
    prices = [start_price]
    
    for change in price_changes:
        # 确保价格保持正值
        new_price = max(0.1, prices[-1] * (1 + change))
        prices.append(new_price)
    
    # 最终价格序列，删除第一个占位元素
    prices = prices[1:]
    
    # 创建开盘价、最高价、最低价
    opens = [p * (1 + np.random.normal(0, 0.005)) for p in prices]
    highs = [max(o, c) * (1 + abs(np.random.normal(0, 0.008))) for o, c in zip(opens, prices)]
    lows = [min(o, c) * (1 - abs(np.random.normal(0, 0.008))) for o, c in zip(opens, prices)]
    
    # 生成成交量 (基于价格变动)
    volumes = [int(abs(np.random.normal(5000000, 2000000)) * (1 + 2*abs(p/prices[i-1]-1)) if i > 0 else 1) 
              for i, p in enumerate(prices)]
    
    # 创建DataFrame
    df = pd.DataFrame({
        'Date': dates,
        'Open': opens,
        'High': highs,
        'Low': lows,
        'Close': prices,
        'Volume': volumes
    })
    
    # 设置日期为索引并格式化
    df['Date'] = pd.to_datetime(df['Date'])
    
    # 添加一些现实的波动 - 模拟几次大波动
    # 添加一次急剧下跌
    crash_idx = days // 3
    adjustment = 0.85  # 下跌15%
    df.iloc[crash_idx:, 1:5] = df.iloc[crash_idx:, 1:5] * adjustment
    
    # 添加一次强劲反弹
    recovery_idx = 2 * days // 3
    adjustment = 1.20  # 上涨20%
    df.iloc[recovery_idx:, 1:5] = df.iloc[recovery_idx:, 1:5] * adjustment
    
    # 确保最高价永远大于等于开盘价和收盘价
    df['High'] = df.apply(lambda row: max(row['High'], row['Open'], row['Close']), axis=1)
    
    # 确保最低价永远小于等于开盘价和收盘价
    df['Low'] = df.apply(lambda row: min(row['Low'], row['Open'], row['Close']), axis=1)
    
    # 重置索引，保留Date列
    df.reset_index(drop=True, inplace=True)
    
    return df

if __name__ == "__main__":
    # 生成数据
    synthetic_data = generate_synthetic_price_data(600, "AAPL")

    # 确保输出目录存在
    output_dir = "tests/data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存到CSV
    output_file = os.path.join(output_dir, "AAPL_synthetic_600d.csv")
    synthetic_data.to_csv(output_file, index=False)

    print(f"已生成合成数据并保存到 {output_file}")
    print(f"数据范围: {synthetic_data['Date'].min()} 至 {synthetic_data['Date'].max()}")
    print(f"总交易日数: {len(synthetic_data)}")
