"""Tests for SignalEvaluator class."""

# Third-party imports
import numpy as np
import pandas as pd
import pytest

# Local imports
from components.registry import ComponentRegistry
from components.signals.comparison import GreaterThan, LessThan
from components.signal_evaluator import SignalEvaluator
from trade_strategies import TradeSignalState
from tests.core.mocks.indicators import MockMultiOutputIndicator

@pytest.fixture
def mock_multi_registry(evaluator):
    """Setup registry with test components."""
    # Back up the original registry
    original_registry = evaluator._registry
    
    try:
        # Create fresh registry for test
        registry = ComponentRegistry()
        
        # Register all required components
        registry.indicators['MOCK_MULTI'] = {
            'module_path': 'tests.core.mocks.indicators',
            'class_name': 'MockMultiOutputIndicator'
        }
        registry.signals['GreaterThan'] = {
            'module_path': 'components.signals.comparison',
            'class_name': 'GreaterThan'
        }
        registry.signals['LessThan'] = {
            'module_path': 'components.signals.comparison',
            'class_name': 'LessThan'
        }
        
        # Initialize registry cache
        registry._registry_cache = {
            'MOCK_MULTI': MockMultiOutputIndicator,
            'GreaterThan': GreaterThan,
            'LessThan': LessThan
        }
        
        # Replace registry
        evaluator._registry = registry
        
        yield evaluator
        
    finally:
        # Restore original registry
        evaluator._registry = original_registry

def create_test_data(periods=100, trend='flat'):
    """Create test price data with specified trend."""
    dates = pd.date_range(start='2020-01-01', periods=periods, freq='D')
    
    if trend == 'flat':
        prices = np.ones(periods) * 100.0
    elif trend == 'up':
        prices = np.linspace(100, 200, periods)
    elif trend == 'down':
        prices = np.linspace(200, 100, periods)
    elif trend == 'oscillate':
        # Base oscillation with higher volatility
        base = np.sin(np.linspace(0, 4*np.pi, periods)) * 30 + 100
        
        # Add random walk component
        random_walk = np.random.normal(0, 1, periods).cumsum() * 2
        
        # Combine for more realistic price movement
        prices = base + random_walk
        
    return pd.DataFrame({
        'Open': prices,
        'High': prices * 1.02,
        'Low': prices * 0.98,
        'Close': prices,
        'Volume': np.ones(periods) * 1000000
    }, index=dates)

# Test configurations
@pytest.fixture
def simple_config():
    """Create a simple test configuration."""
    return {
        'indicators': [
            {
                'id': 'sma',
                'type': 'SMA',
                'params': {'period': 10, 'column': 'Close'}
            }
        ],
        'signals': [
            {
                'id': 'price_above_ma',
                'type': 'GreaterThan',
                'inputs': [
                    {'column': 'Close'},
                    {'ref': 'sma'}
                ]
            },
            {
                'id': 'price_below_ma',
                'type': 'LessThan',
                'inputs': [
                    {'column': 'Close'},
                    {'ref': 'sma'}
                ]
            }
        ],
        'outputs': {
            'buy_signal': 'price_above_ma',
            'sell_signal': 'price_below_ma'
        }
    }

@pytest.fixture
def evaluator():
    """Create SignalEvaluator with basic components."""
    registry = ComponentRegistry()
    
    # Register components
    registry.register_signal('GreaterThan', 'components.signals.comparison', 'GreaterThan')
    registry.register_signal('LessThan', 'components.signals.comparison', 'LessThan')
    registry.register_indicator('SMA', 'components.indicators.moving_averages', 'SMA')
    
    return SignalEvaluator(registry)

def test_evaluator_initialization(evaluator):
    """Test evaluator object creation."""
    assert evaluator is not None
    assert hasattr(evaluator, '_registry')

def test_state_machine_basic_transitions(evaluator, simple_config):
    """Test basic state transitions: EMPTY -> BUY -> HOLD -> SELL -> EMPTY."""
    # Create price data that crosses above and below MA
    data = create_test_data(periods=100, trend='oscillate')
    signals = evaluator.evaluate_for_symbol('TEST', simple_config, data)
    
    # Validate state sequence
    assert signals.iloc[0] == TradeSignalState.EMPTY.value  # Start with EMPTY
    
    # Find first BUY signal
    buy_mask = signals == TradeSignalState.BUY.value
    assert buy_mask.any(), "Should find a BUY signal"
    buy_index = buy_mask.idxmax()
    
    # Verify state sequence after BUY
    next_state_index = signals.index[signals.index.get_loc(buy_index) + 1]
    assert signals.loc[next_state_index] == TradeSignalState.HOLD.value, \
        "BUY should be followed by HOLD"

def test_state_machine_hold_persistence(evaluator, simple_config):
    """Test that HOLD state persists until valid sell signal."""
    data = create_test_data(periods=50, trend='up')  # Consistently rising prices
    signals = evaluator.evaluate_for_symbol('TEST', simple_config, data)
    
    # Find first BUY
    buy_mask = signals == TradeSignalState.BUY.value
    assert buy_mask.any(), "Should find a BUY signal"
    buy_index = buy_mask.idxmax()
    
    # Get next N states after buy
    next_states = signals.loc[signals.index > buy_index].head(10)
    assert all(s == TradeSignalState.HOLD.value for s in next_states), \
        "Should maintain HOLD state when above MA"

def test_state_machine_empty_persistence(evaluator, simple_config):
    """Test that EMPTY state persists until valid buy signal."""
    data = create_test_data(periods=50, trend='down')  # Consistently falling prices
    signals = evaluator.evaluate_for_symbol('TEST', simple_config, data)
    
    # First few signals should be EMPTY as price is below MA
    initial_signals = signals.head(10)
    assert all(s == TradeSignalState.EMPTY.value for s in initial_signals), \
        "Should maintain EMPTY state when below MA"

def test_signal_conflicts(evaluator):
    """Test handling of conflicting buy/sell signals."""
    # Create configuration where buy and sell could conflict
    conflict_config = {
        'indicators': [
            {'id': 'ma_fast', 'type': 'SMA', 'params': {'period': 5, 'column': 'Close'}},
            {'id': 'ma_slow', 'type': 'SMA', 'params': {'period': 10, 'column': 'Close'}}
        ],
        'signals': [
            {
                'id': 'buy_condition',
                'type': 'GreaterThan',
                'inputs': [{'column': 'Close'}, {'ref': 'ma_fast'}]
            },
            {
                'id': 'sell_condition',
                'type': 'LessThan',
                'inputs': [{'column': 'Close'}, {'ref': 'ma_slow'}]
            }
        ],
        'outputs': {
            'buy_signal': 'buy_condition',
            'sell_signal': 'sell_condition'
        }
    }
    
    data = create_test_data(periods=50, trend='oscillate')
    signals = evaluator.evaluate_for_symbol('TEST', conflict_config, data)
    
    # In case of conflict, sell should take precedence
    for prev_idx, curr_idx in zip(signals.index[:-1], signals.index[1:]):
        if signals.loc[prev_idx] == TradeSignalState.HOLD.value:
            assert signals.loc[curr_idx] in [TradeSignalState.HOLD.value, TradeSignalState.SELL.value], \
                "From HOLD, should only transition to SELL or stay HOLD"

def test_cross_reference_resolution(evaluator):
    """Test resolution of cross-referenced components."""
    cross_ref_config = {
        'indicators': [
            {'id': 'fast_ma', 'type': 'SMA', 'params': {'period': 5, 'column': 'Close'}},
            {'id': 'slow_ma', 'type': 'SMA', 'params': {'period': 10, 'column': 'Close'}}
        ],
        'signals': [
            {
                'id': 'cross_over',
                'type': 'GreaterThan',
                'inputs': [{'ref': 'fast_ma'}, {'ref': 'slow_ma'}]
            },
            {
                'id': 'cross_under',
                'type': 'LessThan',
                'inputs': [{'ref': 'fast_ma'}, {'ref': 'slow_ma'}]
            }
        ],
        'outputs': {
            'buy_signal': 'cross_over',
            'sell_signal': 'cross_under'
        }
    }
    
    data = create_test_data(periods=30, trend='oscillate')
    signals = evaluator.evaluate_for_symbol('TEST', cross_ref_config, data)
    assert len(signals) == len(data), "Should generate signals for all data points"

def test_multi_output_reference_handling(mock_multi_registry):
    """Test handling references to multi-output indicator fields."""
    config = {
        'indicators': [
            {
                'id': 'multi',
                'type': 'MOCK_MULTI',
                'params': {}
            }
        ],
        'signals': [
            {
                'id': 'price_above_high',
                'type': 'GreaterThan',
                'inputs': [
                    {'column': 'Close'},
                    {'ref': 'multi.high'}  # Compare against high for buy
                ]
            },
            {
                'id': 'price_below_low',
                'type': 'LessThan',
                'inputs': [
                    {'column': 'Close'},
                    {'ref': 'multi.low'}  # Compare against low for sell
                ]
            }
        ],
        'outputs': {
            'buy_signal': 'price_above_high',
            'sell_signal': 'price_below_low'
        }
    }
    
    # Create longer oscillating data to ensure multiple transitions
    data = create_test_data(periods=50, trend='oscillate')
    
    # Modify the Close prices to ensure they cross above high and below low thresholds
    # This ensures we get all four trade signal states
    for i in range(10, 40, 10):
        # Set some prices higher to trigger buy signals
        data.loc[data.index[i:i+5], 'Close'] = data.loc[data.index[i:i+5], 'High'] * 1.06
        # Set some prices lower to trigger sell signals
        data.loc[data.index[i+5:i+10], 'Close'] = data.loc[data.index[i+5:i+10], 'Low'] * 0.94
    signals = mock_multi_registry.evaluate_for_symbol('TEST', config, data)
    
    # Verify basic signal properties
    assert len(signals) == len(data), "Signal length should match data length"
    assert signals.iloc[0] == TradeSignalState.EMPTY.value, "Should start with EMPTY state"
    
    # Verify all states are present and state counts make sense
    states = signals.value_counts()
    assert len(states) >= 4, "Should have all four states present"
    assert states[TradeSignalState.EMPTY.value] > 0, "Should have EMPTY periods"
    assert states[TradeSignalState.BUY.value] > 0, "Should have BUY signals"
    assert states[TradeSignalState.SELL.value] > 0, "Should have SELL signals"
    assert states[TradeSignalState.HOLD.value] > 0, "Should have HOLD periods"
    
    # Verify state transition sequence
    for i in range(1, len(signals)):
        prev_state = signals.iloc[i-1]
        curr_state = signals.iloc[i]
        
        if prev_state == TradeSignalState.BUY.value:
            assert curr_state == TradeSignalState.HOLD.value, \
                f"BUY must be followed by HOLD at index {i}"
        
        if curr_state == TradeSignalState.BUY.value:
            assert prev_state in [TradeSignalState.EMPTY.value, TradeSignalState.SELL.value], \
                f"BUY can only follow EMPTY or SELL at index {i}"
                
        if curr_state == TradeSignalState.SELL.value:
            assert prev_state in [TradeSignalState.HOLD.value, TradeSignalState.BUY.value], \
                f"SELL can only follow HOLD or BUY at index {i}"
    
    # Verify multi-output reference functionality
    component_cache = mock_multi_registry._evaluated_components
    assert 'multi' in component_cache, "Multi-output indicator should be cached"
    assert isinstance(component_cache['multi'], dict), "Multi-output should return dict"
    assert all(field in component_cache['multi'] for field in ['high', 'low', 'avg']), \
        "All output fields should be present"

def test_invalid_reference_handling(evaluator):
    """Test handling of invalid component references."""
    invalid_config = {
        'indicators': [],
        'signals': [
            {
                'id': 'test_signal',
                'type': 'GreaterThan',
                'inputs': [
                    {'ref': 'non_existent'},
                    {'column': 'Close'}
                ]
            }
        ],
        'outputs': {
            'buy_signal': 'test_signal'
        }
    }
    
    data = create_test_data(periods=10)
    with pytest.raises(KeyError):
        evaluator.evaluate_for_symbol('TEST', invalid_config, data)
