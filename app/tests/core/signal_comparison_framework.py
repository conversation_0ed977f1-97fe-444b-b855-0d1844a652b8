"""
Signal Comparison Test Framework

This framework is used to compare trading signals generated by original strategy code 
with those generated by primitive strategy configurations, to accurately verify whether 
the primitive strategy correctly replicates the behavior of the original strategy.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Optional
import logging

from trade_strategies import BaseTradeStrategy, TradeSignalState
from components.signal_evaluator import SignalEvaluator
from components.registry import ComponentRegistry

# 设置日志
logger = logging.getLogger(__name__)


class SignalComparisonTest:
    """
    比较原始策略与原语化策略信号生成的测试框架
    
    该类提供了工具来:
    1. 从相同的输入数据运行代码策略和原语策略
    2. 比较生成的交易信号
    3. 可视化差异
    4. 生成详细的分析报告
    
    使用示例:
    ```python
    # 创建测试实例
    test = SignalComparisonTest("SPY_data.csv", "SPY")
    
    # 设置原始策略
    from app.trade_strategies.chandelier_exit_ma_strategy import ChandelierExitMAStrategy
    original_params = {"n_atr": 60, "atr_multiplier": 4.0, "n_ma": 250}
    original_strategy = ChandelierExitMAStrategy(original_params)
    
    # 设置原语策略配置
    primitive_config = {...}  # 原语策略配置
    
    # 运行比较
    result = test.run_comparison(original_strategy, primitive_config)
    
    # 保存比较报告
    test.save_report("comparison_report.html")
    ```
    """
    
    def __init__(self, data_path: str, symbol: str, 
                output_dir: Optional[str] = None, 
                ignore_first_n: int = 0):
        """
        Initialize signal comparison test class
        
        Args:
            data_path: Path to OHLCV data file
            symbol: Symbol code for testing
            output_dir: Output directory (optional)
            ignore_first_n: Ignore first n data points (optional)
        """
        self.data = self._load_data(data_path)
        self.symbol = symbol
        self.output_dir = output_dir or os.path.join(os.getcwd(), "signal_test_output")
        self.ignore_first_n = ignore_first_n
        self.registry = ComponentRegistry()
        self.signal_evaluator = SignalEvaluator(self.registry)
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize result storage
        self.results = {
            "original_signals": None,
            "primitive_signals": None,
            "comparison": None,
            "diff_indices": None,
            "summary": None
        }
    
    def _load_data(self, data_path: str) -> pd.DataFrame:
        """Load OHLCV data and preprocess"""
        logger.info(f"Loading data: {data_path}")
        df = pd.read_csv(data_path)
        
        # Ensure date column is datetime type
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'])
            df.set_index('Date', inplace=True)
        
        # Ensure required columns exist
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing = [col for col in required_columns if col not in df.columns]
        if missing:
            raise ValueError(f"Data file is missing required columns: {missing}")
        
        return df
    
    def run_original_strategy(self, strategy: BaseTradeStrategy) -> pd.Series:
        """
        Run original strategy code to get signals
        
        Args:
            strategy: Original strategy instance
        
        Returns:
            pd.Series: Signal series
        """
        logger.info(f"Running original strategy: {type(strategy).__name__}")
        
        # Set data
        strategy.set_data(self.symbol, self.data)
        
        # Generate signals
        signals_df = strategy.generate_signals()
        
        # Extract signal column
        if 'signal' in signals_df.columns:
            return signals_df['signal']
        else:
            raise ValueError("Strategy did not generate 'signal' column")
    
    def run_primitive_strategy(self, config: Dict) -> pd.Series:
        """
        Run primitive strategy configuration to get signals
        
        Args:
            config: Primitive strategy configuration
        
        Returns:
            pd.Series: Signal series
        """
        logger.info("Running primitive strategy configuration")
        
        # Use SignalEvaluator to evaluate signals
        signals = self.signal_evaluator.evaluate_for_symbol(
            self.symbol, config, self.data)
        
        return signals
    
    def compare_signals(self, original_signals: pd.Series, 
                      primitive_signals: pd.Series) -> Dict:
        """
        Compare differences between two sets of signals
        
        Args:
            original_signals: Original strategy signals
            primitive_signals: Primitive strategy signals
        
        Returns:
            Dict: Comparison result summary
        """
        logger.info("Comparing signal differences")
        
        # Store results
        self.results["original_signals"] = original_signals
        self.results["primitive_signals"] = primitive_signals
        
        # Ensure index consistency
        orig = pd.Series(original_signals, index=self.data.index)
        prim = pd.Series(primitive_signals, index=self.data.index)
        
        # Ignore first n data points
        if self.ignore_first_n > 0:
            orig = orig.iloc[self.ignore_first_n:]
            prim = prim.iloc[self.ignore_first_n:]
        
        # Calculate differences
        diff = (orig != prim)
        diff_count = diff.sum()
        diff_percent = (diff_count / len(diff)) * 100
        
        # Find difference indices
        diff_indices = diff[diff].index.tolist() if diff_count > 0 else []
        self.results["diff_indices"] = diff_indices
        
        # Create comparison DataFrame
        comparison_df = pd.DataFrame({
            'Date': self.data.index,
            'Close': self.data['Close'],
            'Original': orig,
            'Primitive': prim,
            'Difference': diff
        })
        self.results["comparison"] = comparison_df
        
        # Generate result summary
        summary = {
            "total_signals": len(diff),
            "different_signals": diff_count,
            "difference_percent": diff_percent,
            "is_consistent": diff_count == 0
        }
        self.results["summary"] = summary
        
        return summary
    
    def generate_report(self) -> str:
        """
        Generate HTML report
        
        Returns:
            str: HTML report content
        """
        if not self.results["summary"]:
            raise ValueError("Must run compare_signals before generating report")
        
        summary = self.results["summary"]
        comparison = self.results["comparison"]
        diff_indices = self.results["diff_indices"]
        
        # Create HTML report
        report = f"""
        <html>
        <head>
            <title>Signal Comparison Result: {self.symbol}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                .summary {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .consistent {{ background-color: #d4edda; }}
                .inconsistent {{ background-color: #f8d7da; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr.different {{ background-color: #ffcccc; }}
                .chart {{ margin: 30px 0; }}
            </style>
        </head>
        <body>
            <h1>Signal Comparison Result: {self.symbol}</h1>
            
            <div class="summary {'consistent' if summary['is_consistent'] else 'inconsistent'}">
                <h2>Summary</h2>
                <p><b>Total Signals:</b> {summary['total_signals']}</p>
                <p><b>Different Signals:</b> {summary['different_signals']} ({summary['difference_percent']:.2f}%)</p>
                <p><b>Consistency:</b> {'Consistent' if summary['is_consistent'] else 'Inconsistent'}</p>
            </div>
        """
        
        # If there are differences, add difference details
        if diff_indices:
            report += f"""
            <h2>First Difference</h2>
            <p>Occurred at: {diff_indices[0]}</p>
            """
            
            # Add difference context table
            if len(diff_indices) > 0:
                first_diff = diff_indices[0]
                # Find index of first difference
                # Ensure index is not already in DataFrame
                temp_df = comparison.copy()
                if temp_df.index.name == 'Date' or 'Date' in temp_df.columns:
                    diff_rows = temp_df.reset_index(drop=True)
                else:
                    diff_rows = temp_df.reset_index()
                diff_rows = diff_rows[diff_rows['Date'] == first_diff]
                if not diff_rows.empty:
                    first_diff_pos = diff_rows.index[0]
                    # Show 5 trading days before and after difference
                    start_pos = max(0, first_diff_pos - 5)
                    end_pos = min(len(comparison) - 1, first_diff_pos + 5)
                    
                    # Use index to select data
                    temp_context = comparison.copy()
                    if temp_context.index.name == 'Date' or 'Date' in temp_context.columns:
                        diff_context = temp_context.reset_index(drop=True).iloc[start_pos:end_pos+1]
                    else:
                        diff_context = temp_context.reset_index().iloc[start_pos:end_pos+1]
                        diff_context = diff_context.drop('index', axis=1, errors='ignore')
                else:
                    # If no difference row is found, show first 5 rows
                    diff_context = comparison.iloc[:5]
                
                report += """
                <h3>Difference Context</h3>
                <table>
                    <tr>
                        <th>Date</th>
                        <th>Close</th>
                        <th>Original Strategy Signals</th>
                        <th>Primitive Strategy Signals</th>
                        <th>Difference</th>
                    </tr>
                """
                
                for _, row in diff_context.iterrows():
                    diff_class = 'different' if row['Difference'] else ''
                    report += f"""
                    <tr class="{diff_class}">
                        <td>{row['Date']}</td>
                        <td>{row['Close']}</td>
                        <td>{row['Original']}</td>
                        <td>{row['Primitive']}</td>
                        <td>{'Different' if row['Difference'] else 'Same'}</td>
                    </tr>
                    """
                
                report += """
                </table>
                """
        
        report += """
            <h2>Chart</h2>
            <p>View signal_comparison_chart.png for visual comparison result</p>
            
            <h2>Full Comparison Data</h2>
            <p>Results saved to comparison_results.csv</p>
        </body>
        </html>
        """
        
        return report
    
    def plot_comparison(self, show_plot: bool = False) -> str:
        """
        Draw signal comparison chart
        
        Args:
            show_plot: Whether to display the chart
        
        Returns:
            str: Chart file path
        """
        if not self.results["original_signals"] is not None:
            raise ValueError("Must run comparison before generating charts")
        
        orig_signals = self.results["original_signals"]
        prim_signals = self.results["primitive_signals"]
        diff_indices = self.results["diff_indices"]
        
        # Create chart
        plt.figure(figsize=(15, 10))
        
        # Draw price chart
        ax1 = plt.subplot(211)
        ax1.plot(self.data.index, self.data['Close'], label='Close Price')
        
        # Mark signal differences
        for idx in diff_indices:
            ax1.axvline(x=idx, color='red', linestyle='--', alpha=0.3)
        
        ax1.set_title(f'{self.symbol} Price Trend and Signal Differences')
        ax1.set_ylabel('Price')
        ax1.legend()
        ax1.grid(True)
        
        # Draw signal chart
        ax2 = plt.subplot(212, sharex=ax1)
        
        # Convert signal state values to numeric for plotting
        def signal_to_numeric(signal_series):
            mapping = {
                TradeSignalState.EMPTY.value: 0,
                TradeSignalState.BUY.value: 1,
                TradeSignalState.HOLD.value: 0.5,
                TradeSignalState.SELL.value: -1
            }
            return signal_series.map(mapping)
        
        orig_numeric = signal_to_numeric(orig_signals)
        prim_numeric = signal_to_numeric(prim_signals)
        
        ax2.plot(self.data.index, orig_numeric, label='Original Strategy Signals', marker='o', markersize=4)
        ax2.plot(self.data.index, prim_numeric, label='Primitive Strategy Signals', marker='x', markersize=4)
        
        # Mark signal differences
        for idx in diff_indices:
            ax2.axvline(x=idx, color='red', linestyle='--', alpha=0.3)
        
        ax2.set_title(f'{self.symbol} Signal Comparison')
        ax2.set_xlabel('Date')
        ax2.set_ylabel('Signal State')
        ax2.legend()
        ax2.grid(True)
        
        # Set y-axis ticks to signal state names
        signal_labels = {
            'B': 'BUY',
            'S': 'SELL',
            'H': 'HOLD',
            'E': 'EMPTY'
        }
        
        # Set y-axis ticks
        ax2.set_yticks([-1, 0, 0.5, 1])
        ax2.set_yticklabels([signal_labels.get(s, s) for s in ['S', 'E', 'H', 'B']])
        
        plt.tight_layout()
        
        # Save chart
        output_path = os.path.join(self.output_dir, f'signal_comparison_{self.symbol}.png')
        plt.savefig(output_path)
        
        if show_plot:
            plt.show()
            
        plt.close()
        
        return output_path
    
    def save_results(self) -> Tuple[str, str, str]:
        """
        Save comparison results to files
        
        Returns:
            Tuple[str, str, str]: (CSV file path, HTML report path, Chart path)
        """
        if self.results["comparison"] is None:
            raise ValueError("Must run comparison before saving results")
            
        # Save CSV results
        csv_path = os.path.join(self.output_dir, f'comparison_results_{self.symbol}.csv')
        self.results["comparison"].to_csv(csv_path)
        
        # Generate and save HTML report
        report = self.generate_report()
        html_path = os.path.join(self.output_dir, f'comparison_report_{self.symbol}.html')
        with open(html_path, 'w') as f:
            f.write(report)
        
        # Generate and save chart
        chart_path = self.plot_comparison()
        
        logger.info(f"Results saved to: {self.output_dir}")
        return csv_path, html_path, chart_path
    
    def run_comparison(self, original_strategy: BaseTradeStrategy, 
                     primitive_config: Dict) -> Dict:
        """
        Run full comparison process
        
        Args:
            original_strategy: Original strategy instance
            primitive_config: Primitive strategy configuration
        
        Returns:
            Dict: Comparison result summary
        """
        # Run original strategy
        original_signals = self.run_original_strategy(original_strategy)
        
        # Run primitive strategy
        primitive_signals = self.run_primitive_strategy(primitive_config)
        
        # Compare signals
        summary = self.compare_signals(original_signals, primitive_signals)
        
        # Save results
        self.save_results()
        
        # Return summary
        return summary


# Helper function
def run_signal_test(data_path: str, symbol: str, 
                   original_strategy: BaseTradeStrategy,
                   primitive_config: Dict,
                   output_dir: Optional[str] = None,
                   ignore_first_n: int = 0,
                   show_plot: bool = False) -> Dict:
    """
    Run signal comparison test for a single symbol
    
    Args:
        data_path: Path to OHLCV data file
        symbol: Symbol code for testing
        original_strategy: Original strategy instance
        primitive_config: Primitive strategy configuration
        output_dir: Output directory (optional)
        ignore_first_n: Ignore first n data points (optional)
        show_plot: Whether to display the chart (optional)
    
    Returns:
        Dict: Comparison result summary
    """
    test = SignalComparisonTest(data_path, symbol, output_dir, ignore_first_n)
    summary = test.run_comparison(original_strategy, primitive_config)
    
    if show_plot:
        test.plot_comparison(show_plot=True)
        
    return summary
