"""
ComponentRegistry 核心功能测试

验证 ComponentRegistry 能够正确发现和注册策略组件，并提供对它们的访问。
这些测试确保注册表能够正确工作，为策略原语化提供基础支持。
"""

import pytest
import os
import sys
from components.registry import ComponentRegistry

# Correct project_root calculation: go up 3 levels from test file directory
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

@pytest.fixture(scope="module")
def registry():
    """Provides a module-scoped ComponentRegistry instance."""
    # Consider resetting the instance if tests interfere, though singleton should handle it
    # ComponentRegistry._instance = None
    return ComponentRegistry.get_instance()

def test_registry_singleton(registry):
    """Tests the singleton pattern of ComponentRegistry."""
    instance1 = ComponentRegistry.get_instance()
    instance2 = ComponentRegistry.get_instance()
    assert instance1 is instance2
    assert instance1 is registry

def _get_expected_strategies(registry_instance, strategy_dir_rel_path):
    """
    Dynamically gets expected strategy class names from a directory.
    Args:
        registry_instance: The ComponentRegistry instance.
        strategy_dir_rel_path: Relative path from project root (e.g., 'app/trade_strategies').
    Returns:
        List[str]: Sorted list of expected class names.
    """
    expected = []
    # Now joins correctly: project_root/app/trade_strategies
    strategy_dir_abs_path = os.path.join(project_root, strategy_dir_rel_path)
    component_type = os.path.basename(strategy_dir_rel_path)

    if not os.path.isdir(strategy_dir_abs_path):
        pytest.fail(f"Test setup error: Directory not found '{strategy_dir_abs_path}'")

    for filename in os.listdir(strategy_dir_abs_path):
        if filename.endswith('.py') and not filename.startswith('__') and not filename.startswith('.'):
            module_name = filename[:-3]
            try:
                # Check if there's a special mapping for this module
                full_module_path = f"{component_type}.{module_name}"
                if full_module_path in registry_instance._name_mappings:
                    class_name = registry_instance._name_mappings[full_module_path]
                else:
                    # Fall back to the standard name generation
                    class_name = registry_instance._generate_class_name(module_name, component_type)
                expected.append(class_name)
            except Exception as e:
                 pytest.fail(f"Error generating class name for {module_name} in {component_type}: {e}")

    if not expected:
         pytest.fail(f"Test setup error: No Python modules found in '{strategy_dir_abs_path}'")

    return sorted(expected)

def test_trade_strategy_registration(registry):
    """Tests if trade strategies are registered correctly."""
    # Pass relative path from the (corrected) project_root
    expected_strategies = _get_expected_strategies(registry, 'app/trade_strategies')
    # Use .keys() to get registered strategy names from the dictionary
    registered_strategies = sorted(registry.trade_strategies.keys())

    assert registered_strategies == expected_strategies, \
        f"Trade strategy registration mismatch.\\nExpected: {expected_strategies}\\nGot:      {registered_strategies}"

    for strategy_name in registered_strategies:
        info = registry.get_component_info('trade_strategies', strategy_name)
        assert info is not None, f"Could not get info for trade strategy {strategy_name}"
        assert 'module_path' in info
        assert 'class_name' in info
        assert info['class_name'] == strategy_name

def test_capital_strategy_registration(registry):
    """Tests if capital management strategies are registered correctly."""
    # Pass relative path from the (corrected) project_root
    expected_strategies = _get_expected_strategies(registry, 'app/capital_strategies')
    # Use .keys() to get registered strategy names from the dictionary
    registered_strategies = sorted(registry.capital_strategies.keys())

    assert registered_strategies == expected_strategies, \
        f"Capital strategy registration mismatch.\\nExpected: {expected_strategies}\\nGot:      {registered_strategies}"

    for strategy_name in registered_strategies:
        info = registry.get_component_info('capital_strategies', strategy_name)
        assert info is not None, f"Could not get info for capital strategy {strategy_name}"
        assert 'module_path' in info
        assert 'class_name' in info
        assert info['class_name'] == strategy_name

def test_nonexistent_strategy(registry):
    """Tests behavior when requesting non-existent strategies."""
    assert registry.get_trade_strategy("NonExistentStrategy") is None
    assert registry.get_capital_strategy("NonExistentCapitalStrategy") is None

def test_indicator_registration(registry):
    """Tests if indicator components are registered correctly."""
    # At minimum, our manually registered SMA should be present
    assert "SMA" in registry.indicators
    
    # Verify we can retrieve the indicator class
    sma_class = registry.get_indicator("SMA")
    assert sma_class is not None
    
    # Verify component info
    info = registry.get_component_info('indicators', 'SMA')
    assert info is not None
    assert 'module_path' in info
    assert 'class_name' in info
    assert info['class_name'] == 'SMA'
    
    # Test manual registration of a new indicator
    registry.register_indicator('TestIndicator', 'components.indicators.moving_averages', 'SMA')
    assert 'TestIndicator' in registry.indicators
    test_indicator = registry.get_indicator('TestIndicator')
    assert test_indicator is not None
    
    # Verify indicator behaves as expected by checking module path
    test_info = registry.get_component_info('indicators', 'TestIndicator')
    assert test_info['module_path'] == 'components.indicators.moving_averages'

def test_signal_registration(registry):
    """Tests if signal components are registered correctly."""
    # At minimum, our manually registered signals should be present
    expected_signals = ['Crossover', 'Crossunder', 'LessThan']
    for signal in expected_signals:
        assert signal in registry.signals
        
        # Verify we can retrieve the signal class
        signal_class = registry.get_signal(signal)
        assert signal_class is not None
        
        # Verify component info
        info = registry.get_component_info('signals', signal)
        assert info is not None
        assert 'module_path' in info
        assert 'class_name' in info
        assert info['class_name'] == signal
    
    # Test manual registration of a new signal
    registry.register_signal('TestSignal', 'components.signals.comparison', 'Crossover')
    assert 'TestSignal' in registry.signals
    test_signal = registry.get_signal('TestSignal')
    assert test_signal is not None
    
    # Verify signal behaves as expected by checking module path
    test_info = registry.get_component_info('signals', 'TestSignal')
    assert test_info['module_path'] == 'components.signals.comparison'

def test_nonexistent_primitives(registry):
    """Tests behavior when requesting non-existent primitives."""
    assert registry.get_indicator("NonExistentIndicator") is None
    assert registry.get_signal("NonExistentSignal") is None
    assert registry.get_market_transformer("NonExistentTransformer") is None
