"""
Unit tests for Market Transformers.

Tests the functionality of the market transformer classes for processing
market indicator data.
"""

import unittest
import pandas as pd
import numpy as np

from components.market_transformers import (
    BaseMarketTransformer,
    MovingAverageTransformer,
    PercentileRankTransformer,
    RelativeStrengthTransformer,
    ZScoreTransformer,
    IdentityTransformer
)

class TestBaseMarketTransformer(unittest.TestCase):
    """Test cases for BaseMarketTransformer."""
    
    def test_get_field(self):
        """Test getting field from data frame."""
        # Create a test implementation of BaseMarketTransformer
        class TestTransformer(BaseMarketTransformer):
            def transform(self, data):
                return self._get_field(data)
                
        # Create sample data
        dates = pd.date_range(start='2020-01-01', end='2020-01-10')
        data = pd.DataFrame({
            'open': np.random.uniform(10, 20, size=len(dates)),
            'high': np.random.uniform(20, 30, size=len(dates)),
            'low': np.random.uniform(5, 15, size=len(dates)),
            'close': np.random.uniform(15, 25, size=len(dates)),
            'volume': np.random.uniform(1000, 2000, size=len(dates))
        }, index=dates)
        
        # Test default field (close)
        transformer = TestTransformer(params={})
        result = transformer.transform(data)
        pd.testing.assert_series_equal(result, data['close'])
        
        # Test specified field
        transformer = TestTransformer(params={'field': 'open'})
        result = transformer.transform(data)
        pd.testing.assert_series_equal(result, data['open'])
        
        # Test non-existent field (should default to close)
        transformer = TestTransformer(params={'field': 'non_existent'})
        result = transformer.transform(data)
        pd.testing.assert_series_equal(result, data['close'])
        
class TestMovingAverageTransformer(unittest.TestCase):
    """Test cases for MovingAverageTransformer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.dates = pd.date_range(start='2020-01-01', end='2020-01-20')
        self.data = pd.DataFrame({
            'close': [10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0,
                     20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0]
        }, index=self.dates)
        
    def test_validate_params(self):
        """Test parameter validation."""
        # Test missing window parameter
        with self.assertRaises(ValueError):
            MovingAverageTransformer(params={})
            
        # Test invalid window parameter
        with self.assertRaises(ValueError):
            MovingAverageTransformer(params={'window': 'invalid'})
            
        # Test negative window parameter
        with self.assertRaises(ValueError):
            MovingAverageTransformer(params={'window': -5})
            
        # Test invalid MA type
        with self.assertRaises(ValueError):
            MovingAverageTransformer(params={'window': 5, 'type': 'invalid'})
            
        # Test valid parameters
        transformer = MovingAverageTransformer(params={'window': 5})
        self.assertEqual(transformer.params['window'], 5)
        
    def test_simple_ma_transform(self):
        """Test simple moving average transformation."""
        transformer = MovingAverageTransformer(params={'window': 5, 'type': 'simple'})
        result = transformer.transform(self.data)
        
        # Calculate expected values
        expected = self.data['close'].rolling(window=5, min_periods=1).mean()
        expected.name = 'close_ma5_s'
        
        # Verify the result
        pd.testing.assert_series_equal(result, expected)
        
    def test_exponential_ma_transform(self):
        """Test exponential moving average transformation."""
        transformer = MovingAverageTransformer(params={'window': 5, 'type': 'exponential'})
        result = transformer.transform(self.data)
        
        # Calculate expected values
        expected = self.data['close'].ewm(span=5, min_periods=1, adjust=False).mean()
        expected.name = 'close_ma5_e'
        
        # Verify the result
        pd.testing.assert_series_equal(result, expected)
        
    def test_weighted_ma_transform(self):
        """Test weighted moving average transformation."""
        transformer = MovingAverageTransformer(params={'window': 5, 'type': 'weighted'})
        result = transformer.transform(self.data)
        
        # Check result properties
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_ma5_w')
        
class TestPercentileRankTransformer(unittest.TestCase):
    """Test cases for PercentileRankTransformer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample data with a clear pattern for percentile calculation
        self.dates = pd.date_range(start='2020-01-01', end='2020-01-10')
        self.data = pd.DataFrame({
            'close': [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0]
        }, index=self.dates)
        
    def test_validate_params(self):
        """Test parameter validation."""
        # Test missing lookback parameter
        with self.assertRaises(ValueError):
            PercentileRankTransformer(params={})
            
        # Test invalid lookback parameter
        with self.assertRaises(ValueError):
            PercentileRankTransformer(params={'lookback': 'invalid'})
            
        # Test negative lookback parameter
        with self.assertRaises(ValueError):
            PercentileRankTransformer(params={'lookback': -5})
            
        # Test valid parameters
        transformer = PercentileRankTransformer(params={'lookback': 5})
        self.assertEqual(transformer.params['lookback'], 5)
        
    def test_percentile_rank_transform(self):
        """Test percentile rank transformation."""
        transformer = PercentileRankTransformer(params={'lookback': 5})
        result = transformer.transform(self.data)
        
        # Verify the result
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_pctrank_5')
        
        # For the last values in a window of 5, the percentile should be 100
        # (they are the highest in their window)
        for i in range(4, len(self.data)):
            self.assertAlmostEqual(result.iloc[i], 100.0)
            
class TestRelativeStrengthTransformer(unittest.TestCase):
    """Test cases for RelativeStrengthTransformer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.dates = pd.date_range(start='2020-01-01', end='2020-01-10')
        self.data = pd.DataFrame({
            'close': [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0]
        }, index=self.dates)
        
    def test_validate_params(self):
        """Test parameter validation."""
        # Test missing reference parameter
        with self.assertRaises(ValueError):
            RelativeStrengthTransformer(params={})
            
        # Test invalid reference parameter
        with self.assertRaises(ValueError):
            RelativeStrengthTransformer(params={'reference': 'invalid'})
            
        # Test missing window for MA reference
        with self.assertRaises(ValueError):
            RelativeStrengthTransformer(params={'reference': 'ma'})
            
        # Test missing window for lookback reference
        with self.assertRaises(ValueError):
            RelativeStrengthTransformer(params={'reference': 'lookback'})
            
        # Test missing value for value reference
        with self.assertRaises(ValueError):
            RelativeStrengthTransformer(params={'reference': 'value'})
            
        # Test valid parameters for MA reference
        transformer = RelativeStrengthTransformer(params={'reference': 'ma', 'window': 5})
        self.assertEqual(transformer.params['reference'], 'ma')
        self.assertEqual(transformer.params['window'], 5)
        
        # Test valid parameters for value reference
        transformer = RelativeStrengthTransformer(params={'reference': 'value', 'value': 50})
        self.assertEqual(transformer.params['reference'], 'value')
        self.assertEqual(transformer.params['value'], 50)
        
    def test_ma_reference_transform(self):
        """Test MA reference transformation."""
        transformer = RelativeStrengthTransformer(params={'reference': 'ma', 'window': 5})
        result = transformer.transform(self.data)
        
        # Verify the result
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_rs_ma5')
        
        # Calculate expected values for the last row
        # 5-day MA of [60, 70, 80, 90, 100] = 80
        # Relative strength = 100 / 80 * 100 = 125
        expected_last = 100.0 / 80.0 * 100.0
        self.assertAlmostEqual(result.iloc[-1], expected_last)
        
    def test_value_reference_transform(self):
        """Test value reference transformation."""
        transformer = RelativeStrengthTransformer(params={'reference': 'value', 'value': 50})
        result = transformer.transform(self.data)
        
        # Verify the result
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_rs_val50')
        
        # Calculate expected values for all rows
        expected = self.data['close'] / 50.0 * 100.0
        
        # Verify each value
        for i in range(len(self.data)):
            self.assertAlmostEqual(result.iloc[i], expected.iloc[i])
            
    def test_lookback_reference_transform(self):
        """Test lookback reference transformation."""
        transformer = RelativeStrengthTransformer(params={'reference': 'lookback', 'window': 3})
        result = transformer.transform(self.data)
        
        # Verify the result
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_rs_lb3')
        
        # For indexes 3-9, compare with values 3 periods ago
        for i in range(3, len(self.data)):
            expected = self.data['close'].iloc[i] / self.data['close'].iloc[i-3] * 100.0
            self.assertAlmostEqual(result.iloc[i], expected)
            
class TestZScoreTransformer(unittest.TestCase):
    """Test cases for ZScoreTransformer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.dates = pd.date_range(start='2020-01-01', end='2020-01-20')
        self.data = pd.DataFrame({
            'close': [10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0,
                     20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.0, 13.0, 12.0, 11.0]
        }, index=self.dates)
        
    def test_validate_params(self):
        """Test parameter validation."""
        # Test missing window parameter
        with self.assertRaises(ValueError):
            ZScoreTransformer(params={})
            
        # Test invalid window parameter
        with self.assertRaises(ValueError):
            ZScoreTransformer(params={'window': 'invalid'})
            
        # Test negative window parameter
        with self.assertRaises(ValueError):
            ZScoreTransformer(params={'window': -5})
            
        # Test valid parameters
        transformer = ZScoreTransformer(params={'window': 5})
        self.assertEqual(transformer.params['window'], 5)
        
    def test_zscore_transform(self):
        """Test Z-score transformation."""
        transformer = ZScoreTransformer(params={'window': 5})
        result = transformer.transform(self.data)
        
        # Verify the result
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.data))
        self.assertEqual(result.name, 'close_zscore_5')
        
        # For the last 5 elements, calculate expected Z-scores
        window = self.data['close'].iloc[-5:]
        mean = window.mean()
        std = window.std()
        expected_last = (window.iloc[-1] - mean) / std
        
        # Allow for small numerical differences
        self.assertAlmostEqual(result.iloc[-1], expected_last, places=10)
        
class TestIdentityTransformer(unittest.TestCase):
    """Test cases for IdentityTransformer."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.dates = pd.date_range(start='2020-01-01', end='2020-01-10')
        self.data = pd.DataFrame({
            'close': [10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0, 90.0, 100.0],
            'open': [9.0, 19.0, 29.0, 39.0, 49.0, 59.0, 69.0, 79.0, 89.0, 99.0]
        }, index=self.dates)
    
    def test_validate_params(self):
        """Test parameter validation."""
        # Test missing indicator parameter
        with self.assertRaises(ValueError):
            IdentityTransformer(params={})
        
        # Test valid parameters
        transformer = IdentityTransformer(params={'indicator': 'SPX'})
        self.assertEqual(transformer.params['indicator'], 'SPX')
        
    def test_identity_transform(self):
        """Test identity transformation."""
        # Create transformer with required parameters
        transformer = IdentityTransformer(params={'indicator': 'SPX'})
        result = transformer.transform(self.data)
        
        # Verify the result - default field should be 'close'
        pd.testing.assert_series_equal(result, self.data['close'])
        self.assertEqual(result.name, 'close')
        
        # Test with specified field parameter
        transformer = IdentityTransformer(params={'indicator': 'SPX', 'field': 'open'})
        result = transformer.transform(self.data)
        
        # Verify the result matches the specified field
        pd.testing.assert_series_equal(result, self.data['open'])
        self.assertEqual(result.name, 'open')


if __name__ == '__main__':
    unittest.main()
