import pytest
from tests.utils.test_helpers import MockMinimalDataLoader
from portfolio_factory_v2 import PortfolioFactory

def test_capital_allocation():
    """测试资金分配策略
    
    通过分析实际交易记录来验证资金分配行为：
    1. 检查单次交易的资金使用是否符合百分比限制
    2. 分析多次交易的资金使用模式
    """
    # 准备测试数据
    data_loader = MockMinimalDataLoader()
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")
    portfolio = factory.get_portfolio("myinvestpilot_us_1", data_loader)
    
    # 执行回测
    collector = portfolio.execute_trades()
    
    # 获取交易记录
    trade_records = collector.trade_records
    
    if not trade_records.empty:
        # 获取正确的初始资金
        initial_capital = portfolio.capital_strategy.initial_capital
        max_trade_amount = max(abs(trade_records['amount'])) if 'amount' in trade_records.columns else 0
        
        assert max_trade_amount > 0, "应该有实际的交易发生"
        # 验证单笔交易金额不超过总初始资金（基本健全性检查）
        assert max_trade_amount <= initial_capital, f"单笔交易金额({max_trade_amount})不应超过初始资金({initial_capital})"

def test_position_management():
    """测试持仓管理
    
    验证系统是否正确遵守持仓限制：
    1. 检查同时持仓的股票数量
    2. 验证新开仓时是否遵守限制
    """
    # 准备测试数据
    data_loader = MockMinimalDataLoader()
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")
    portfolio = factory.get_portfolio("myinvestpilot_us_1", data_loader)
    
    # 执行回测
    collector = portfolio.execute_trades()
    
    # 分析持仓记录
    position_records = collector.position_records
    
    if not position_records.empty:
        # 按日期分组，计算每天的持仓数量
        daily_positions = position_records.groupby('date').size()
        max_positions = daily_positions.max()
        
        # 验证最大持仓数量不超过配置限制
        config = portfolio.strategy.params
        position_limit = config.get('max_positions', float('inf'))
        
        if position_limit < float('inf'):
            assert max_positions <= position_limit, \
                f"最大同时持仓数量({max_positions})超过限制({position_limit})"
