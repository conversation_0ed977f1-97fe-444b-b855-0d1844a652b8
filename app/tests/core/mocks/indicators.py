"""Mock indicators for testing."""

from typing import Dict
import pandas as pd
from components.base.primitives import BaseIndicatorPrimitive

class MockMultiOutputIndicator(BaseIndicatorPrimitive):
    """Mock indicator that returns multiple outputs for testing."""
    
    def __init__(self, params: Dict = None) -> None:
        super().__init__(params or {})
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate multiple output fields with distinct thresholds.
        
        Returns:
            Dict with fields:
            - high: High prices + 5% margin
            - low: Low prices - 5% margin
            - avg: Average of high and low
        """
        high = data['High'] * 1.05  # Add 5% margin for clear buy signals
        low = data['Low'] * 0.95   # Subtract 5% margin for clear sell signals
        return {
            'high': high,
            'low': low,
            'avg': (high + low) / 2
        }
