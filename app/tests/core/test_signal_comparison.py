"""
测试信号比较框架

该脚本提供了信号比较测试框架的示例用法和测试用例。
"""

import os
import sys
import pandas as pd
import numpy as np
import unittest
import logging
import pytest
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入测试框架
from tests.core.signal_comparison_framework import SignalComparisonTest, run_signal_test
from trade_strategies.chandelier_exit_ma_strategy import ChandelierExitMAStrategy
from components.registry import ComponentRegistry

# 测试数据路径
TEST_DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
os.makedirs(TEST_DATA_DIR, exist_ok=True)

# 定义信号比较框架测试标记
pytest.mark.signal_comparison = pytest.mark.skip(reason="Signal comparison tests should be run separately")


@pytest.mark.signal_comparison
class TestSignalComparisonFramework(unittest.TestCase):
    """信号比较框架测试用例"""
    
    @classmethod
    def setUpClass(cls):
        """测试类设置"""
        # 确保测试数据存在
        cls.prepare_test_data()
        
        # 创建测试输出目录
        cls.output_dir = os.path.join(TEST_DATA_DIR, 'signal_test_output')
        os.makedirs(cls.output_dir, exist_ok=True)
        
        # 初始化组件注册表
        cls.registry = ComponentRegistry()
        
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 运行结束后可以清理临时文件
        pass
        
    @classmethod
    def prepare_test_data(cls):
        """准备测试数据"""
        # 检查测试数据是否存在，不存在则创建
        sample_data_path = os.path.join(TEST_DATA_DIR, 'FNGU_sample.csv')
        
        if not os.path.exists(sample_data_path):
            logger.info(f"创建测试数据: {sample_data_path}")
            
            # 创建示例数据
            dates = pd.date_range(start='2020-01-01', end='2020-12-31')
            np.random.seed(42)  # 固定随机种子以确保可重复性
            
            # 生成价格数据
            close = 100 + np.cumsum(np.random.normal(0, 1, len(dates)))
            high = close + np.random.uniform(0, 2, len(dates))
            low = close - np.random.uniform(0, 2, len(dates))
            open_price = close - np.random.uniform(-1, 1, len(dates))
            volume = np.random.randint(1000, 10000, len(dates))
            
            # 创建DataFrame
            df = pd.DataFrame({
                'Date': dates,
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close,
                'Volume': volume,
                'Adj Close': close  # 添加调整后收盘价
            })
            
            # 保存到CSV
            df.to_csv(sample_data_path, index=False)
            logger.info(f"测试数据创建完成: {sample_data_path}")
        else:
            logger.info(f"测试数据已存在: {sample_data_path}")
            
        return sample_data_path
        
    def test_basic_functionality(self):
        """测试框架的基本功能"""
        # 准备测试数据
        data_path = os.path.join(TEST_DATA_DIR, 'FNGU_sample.csv')
        symbol = 'FNGU'
        
        # 创建测试实例
        test = SignalComparisonTest(data_path, symbol, self.output_dir)
        
        # 准备原始策略
        original_params = {
            'n_atr': 20,  # 设置较短的窗口期以便在测试数据上生成足够的信号
            'atr_multiplier': 3.0,
            'n_ma': 50
        }
        original_strategy = ChandelierExitMAStrategy(original_params)
        
        # 准备原语策略配置
        primitive_config = {
            "indicators": [
                { 
                    "id": "ma_trend", 
                    "type": "SMA", 
                    "params": { 
                        "period": 50,
                        "column": "Close"
                    } 
                },
                {
                    "id": "atr", 
                    "type": "ATR", 
                    "params": { 
                        "period": 20
                    }
                },
                {
                    "id": "highest_high",
                    "type": "HighestValue",
                    "params": {
                        "period": 20,
                        "column": "High"
                    }
                }
            ],
            "signals": [
                {
                    "id": "chandelier_exit",
                    "type": "Subtract",
                    "inputs": [
                        { "ref": "highest_high" },
                        { 
                            "type": "Multiply",
                            "inputs": [
                                { "ref": "atr" },
                                { "value": 3.0 }
                            ]
                        }
                    ]
                },
                {
                    "id": "close_above_ma",
                    "type": "GreaterThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "ma_trend" }
                    ]
                },
                {
                    "id": "close_above_ce",
                    "type": "GreaterThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "chandelier_exit" }
                    ]
                },
                {
                    "id": "close_below_ma",
                    "type": "LessThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "ma_trend" }
                    ]
                },
                {
                    "id": "close_below_ce",
                    "type": "LessThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "chandelier_exit" }
                    ]
                },
                {
                    "id": "buy_signal",
                    "type": "And",
                    "inputs": [
                        { "ref": "close_above_ma" },
                        { "ref": "close_above_ce" }
                    ]
                },
                {
                    "id": "sell_signal",
                    "type": "And",
                    "inputs": [
                        { "ref": "close_below_ma" },
                        { "ref": "close_below_ce" }
                    ]
                }
            ],
            "outputs": {
                "buy_signal": "buy_signal",
                "sell_signal": "sell_signal"
            }
        }
        
        # 运行比较
        results = test.run_comparison(original_strategy, primitive_config)
        
        # 验证结果
        self.assertIsNotNone(results)
        self.assertIn('total_signals', results)
        self.assertIn('different_signals', results)
        self.assertIn('difference_percent', results)
        self.assertIn('is_consistent', results)
        
        # 检查输出文件
        csv_file = os.path.join(self.output_dir, f'comparison_results_{symbol}.csv')
        html_file = os.path.join(self.output_dir, f'comparison_report_{symbol}.html')
        png_file = os.path.join(self.output_dir, f'signal_comparison_{symbol}.png')
        
        self.assertTrue(os.path.exists(csv_file), f"CSV文件不存在: {csv_file}")
        self.assertTrue(os.path.exists(html_file), f"HTML报告不存在: {html_file}")
        self.assertTrue(os.path.exists(png_file), f"对比图不存在: {png_file}")
        
        # 打印结果
        logger.info(f"测试结果: {results}")
        
    def test_helper_function(self):
        """测试辅助函数"""
        # 准备测试数据
        data_path = os.path.join(TEST_DATA_DIR, 'FNGU_sample.csv')
        symbol = 'FNGU'
        
        # 准备原始策略
        original_params = {
            'n_atr': 20,
            'atr_multiplier': 3.0,
            'n_ma': 50
        }
        original_strategy = ChandelierExitMAStrategy(original_params)
        
        # 准备一个故意有差异的原语策略配置
        primitive_config = {
            "indicators": [
                { 
                    "id": "ma_trend", 
                    "type": "SMA", 
                    "params": { 
                        "period": 50,
                        "column": "Close"
                    } 
                },
                {
                    "id": "atr", 
                    "type": "ATR", 
                    "params": { 
                        "period": 20
                    }
                },
                {
                    "id": "highest_high",
                    "type": "HighestValue",
                    "params": {
                        "period": 20,
                        "column": "High"
                    }
                }
            ],
            "signals": [
                {
                    "id": "chandelier_exit",
                    "type": "Subtract",
                    "inputs": [
                        { "ref": "highest_high" },
                        { 
                            "type": "Multiply",
                            "inputs": [
                                { "ref": "atr" },
                                { "value": 2.0 }  # 故意使用不同的乘数
                            ]
                        }
                    ]
                },
                {
                    "id": "close_above_ma",
                    "type": "GreaterThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "ma_trend" }
                    ]
                },
                {
                    "id": "close_above_ce",
                    "type": "GreaterThan",
                    "inputs": [
                        { "column": "Close" },
                        { "ref": "chandelier_exit" }
                    ]
                },
                {
                    "id": "buy_signal",
                    "type": "And",
                    "inputs": [
                        { "ref": "close_above_ma" },
                        { "ref": "close_above_ce" }
                    ]
                },
                {
                    "id": "sell_signal",
                    "type": "ref",
                    "inputs": [{ "value": False }]  # 修正为Python大写False
                }
            ],
            "outputs": {
                "buy_signal": "buy_signal",
                "sell_signal": "sell_signal"
            }
        }
        
        # 使用辅助函数运行测试
        results = run_signal_test(
            data_path=data_path,
            symbol=symbol,
            original_strategy=original_strategy,
            primitive_config=primitive_config,
            output_dir=self.output_dir,
            ignore_first_n=50  # 忽略初始化阶段
        )
        
        # 验证结果 - 我们期望有差异
        self.assertIsNotNone(results)
        
        # 打印结果
        logger.info(f"辅助函数测试结果: {results}")


# 独立运行示例
def run_example():
    """运行独立示例"""
    # 这个函数可以在命令行中直接调用，展示框架的使用方法
    
    # 准备测试数据
    test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
    data_path = os.path.join(test_dir, 'FNGU_sample.csv')
    symbol = 'FNGU'
    output_dir = os.path.join(test_dir, 'signal_test_output')
    
    # 确保目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备数据
    test_case = TestSignalComparisonFramework()
    test_case.prepare_test_data()
    
    # 创建原始策略
    original_params = {
        'n_atr': 20,
        'atr_multiplier': 3.0,
        'n_ma': 50
    }
    original_strategy = ChandelierExitMAStrategy(original_params)
    
    # 创建原语策略配置 - 与原始策略参数完全匹配
    primitive_config = {
        "indicators": [
            { 
                "id": "ma_trend", 
                "type": "SMA", 
                "params": { 
                    "period": 50,
                    "column": "Close"
                } 
            },
            {
                "id": "atr", 
                "type": "ATR", 
                "params": { 
                    "period": 20
                }
            },
            {
                "id": "highest_high",
                "type": "HighestValue",
                "params": {
                    "period": 20,
                    "column": "High"
                }
            }
        ],
        "signals": [
            {
                "id": "chandelier_exit",
                "type": "Subtract",
                "inputs": [
                    { "ref": "highest_high" },
                    { 
                        "type": "Multiply",
                        "inputs": [
                            { "ref": "atr" },
                            { "value": 3.0 }
                        ]
                    }
                ]
            },
            {
                "id": "close_above_ma",
                "type": "GreaterThan",
                "inputs": [
                    { "column": "Close" },
                    { "ref": "ma_trend" }
                ]
            },
            {
                "id": "close_above_ce",
                "type": "GreaterThan",
                "inputs": [
                    { "column": "Close" },
                    { "ref": "chandelier_exit" }
                ]
            },
            {
                "id": "close_below_ma",
                "type": "LessThan",
                "inputs": [
                    { "column": "Close" },
                    { "ref": "ma_trend" }
                ]
            },
            {
                "id": "close_below_ce",
                "type": "LessThan",
                "inputs": [
                    { "column": "Close" },
                    { "ref": "chandelier_exit" }
                ]
            },
            {
                "id": "buy_signal",
                "type": "And",
                "inputs": [
                    { "ref": "close_above_ma" },
                    { "ref": "close_above_ce" }
                ]
            },
            {
                "id": "sell_signal",
                "type": "And",
                "inputs": [
                    { "ref": "close_below_ma" },
                    { "ref": "close_below_ce" }
                ]
            }
        ],
        "outputs": {
            "buy_signal": "buy_signal",
            "sell_signal": "sell_signal"
        }
    }
    
    # 运行测试
    results = run_signal_test(
        data_path=data_path,
        symbol=symbol,
        original_strategy=original_strategy,
        primitive_config=primitive_config,
        output_dir=output_dir,
        ignore_first_n=50,  # 忽略初始化阶段
        show_plot=False
    )
    
    print("\n=== 测试结果 ===")
    print(f"总信号数: {results['total_signals']}")
    print(f"不同信号数: {results['different_signals']}")
    print(f"差异百分比: {results['difference_percent']:.2f}%")
    print(f"一致性: {'✅ 通过' if results['is_consistent'] else '❌ 失败'}")
    print("\n结果已保存至:", output_dir)


if __name__ == "__main__":
    # 如果直接运行此脚本，则执行示例
    if len(sys.argv) > 1 and sys.argv[1] == "--example":
        run_example()
    else:
        # 否则运行单元测试
        unittest.main()
