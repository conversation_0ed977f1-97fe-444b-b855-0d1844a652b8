import pytest
import pandas as pd
from tests.utils.test_helpers import MockMinimalDataLoader
from portfolio_factory_v2 import PortfolioFactory

def test_max_drawdown_calculation():
    """测试最大回撤计算
    
    通过运行一个完整的回测来验证最大回撤计算的正确性。
    使用QQQ数据（下降趋势）来确保有明显的回撤。
    """
    # 准备测试数据
    data_loader = MockMinimalDataLoader()
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")
    portfolio = factory.get_portfolio("myinvestpilot_us_1", data_loader)
    
    # 执行回测
    collector = portfolio.execute_trades()
    
    # 验证最大回撤
    risk_metrics = collector.portfolio_data.get('risk_metrics', {})
    max_drawdown = risk_metrics.get('max_drawdown', 0)
    
    # 对于下降趋势的数据，最大回撤应为非正值
    assert isinstance(max_drawdown, float), "最大回撤应该是浮点数"
    assert max_drawdown <= 0, "下降趋势数据的最大回撤应该小于或等于0"

def test_sharpe_ratio_calculation():
    """测试夏普比率计算
    
    通过运行一个完整的回测来验证夏普比率的计算。
    使用SPY数据（上升趋势）来生成正的夏普比率。
    """
    # 准备测试数据
    data_loader = MockMinimalDataLoader()
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")
    portfolio = factory.get_portfolio("myinvestpilot_us_1", data_loader)
    
    # 执行回测
    collector = portfolio.execute_trades()
    
    # 验证夏普比率
    risk_metrics = collector.portfolio_data.get('risk_metrics', {})
    sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
    
    # 对于上升趋势的数据，夏普比率应为非负值或None（如果无法计算）
    assert sharpe_ratio is None or sharpe_ratio >= 0, "上升趋势数据的夏普比率应该为非负或None"
