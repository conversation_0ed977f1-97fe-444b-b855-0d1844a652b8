"""
股债切换信号原语测试。

该模块测试以下信号：
- StockBondSwitch
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from components.signals.switching import StockBondSwitch
from components.signal_evaluator import SignalEvaluator


def create_test_condition_signal():
    """创建测试用的条件信号序列。"""
    dates = pd.date_range('2020-01-01', periods=10)
    # 前5天为True（市场向上），后5天为False（市场向下）
    condition_signal = pd.Series([True, True, True, True, True, False, False, False, False, False], index=dates)
    return dates, condition_signal


class TestStockBondSwitch:
    """StockBondSwitch 原语测试类。"""
    
    def test_default_params(self):
        """测试默认参数。"""
        switch = StockBondSwitch()
        defaults = switch.get_default_params()
        
        assert defaults['default_to_stock'] is True
    
    def test_param_validation(self):
        """测试参数验证。"""
        # 测试有效参数
        switch = StockBondSwitch(params={'default_to_stock': True})
        switch.validate_params()
        
        switch = StockBondSwitch(params={'default_to_stock': False})
        switch.validate_params()
        
        # 测试无效的default_to_stock类型
        with pytest.raises(ValueError, match="default_to_stock must be a boolean"):
            switch = StockBondSwitch(params={'default_to_stock': 'invalid'})
            switch.validate_params()
    
    def test_requires_global_context(self):
        """测试全局上下文标志。"""
        switch = StockBondSwitch()
        assert switch.REQUIRES_GLOBAL_CONTEXT is True
    
    @patch('components.signals.switching.inspect')
    def test_stock_symbol_buy_signal_when_market_up(self, mock_inspect):
        """测试股票ETF在市场上涨时的买入信号。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 创建mock frame链
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '510300'},
                all_symbols=['510300', '511260']
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 验证返回的是布尔Series
        assert isinstance(result, pd.Series)
        assert result.dtype == bool
        assert len(result) == len(condition_signal)
        
        # 对于股票ETF：市场向上时买入（True），市场向下时不买入（False）
        expected = pd.Series([True, True, True, True, True, False, False, False, False, False], index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_bond_symbol_buy_signal_when_market_down(self, mock_inspect):
        """测试债券ETF在市场下跌时的买入信号。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 创建mock frame链
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '511260'},
                all_symbols=['510300', '511260']
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 对于债券ETF：市场向下时买入（~condition），市场向上时不买入
        expected = pd.Series([False, False, False, False, False, True, True, True, True, True], index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_unknown_symbol_returns_false(self, mock_inspect):
        """测试未知符号返回False。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 模拟调用栈，返回未知符号的上下文
        mock_frame = Mock()
        mock_frame.f_back = None
        mock_frame.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': 'UNKNOWN'},
                all_symbols=['510300', '511260']
            )
        }
        mock_inspect.currentframe.return_value = mock_frame
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 未知符号应该返回全False
        expected = pd.Series([False] * 10, index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_insufficient_symbols_returns_false(self, mock_inspect):
        """测试符号池不足时返回False。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 模拟调用栈，只有一个符号
        mock_frame = Mock()
        mock_frame.f_back = None
        mock_frame.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '510300'},
                all_symbols=['510300']  # 只有一个符号
            )
        }
        mock_inspect.currentframe.return_value = mock_frame
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 符号不足应该返回全False
        expected = pd.Series([False] * 10, index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_no_context_returns_false(self, mock_inspect):
        """测试无法获取上下文时返回False。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 模拟调用栈检查失败
        mock_inspect.currentframe.return_value = None
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 无法获取上下文应该返回全False
        expected = pd.Series([False] * 10, index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_condition_signal_with_nan_values(self, mock_inspect):
        """测试包含NaN值的条件信号处理。"""
        dates = pd.date_range('2020-01-01', periods=6)
        # 包含NaN值的信号
        condition_signal = pd.Series([np.nan, True, np.nan, False, True, np.nan], index=dates)
        
        # 创建mock frame链
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '510300'},
                all_symbols=['510300', '511260']
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        # 测试default_to_stock=True
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # NaN值应该被default_to_stock填充
        expected = pd.Series([True, True, True, False, True, True], index=dates)
        pd.testing.assert_series_equal(result, expected)
        
        # 测试default_to_stock=False
        switch = StockBondSwitch(params={'default_to_stock': False})
        result = switch.evaluate(condition_signal)
        
        expected = pd.Series([False, True, False, False, True, False], index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_complex_context_detection(self, mock_inspect):
        """测试复杂的上下文检测（多层调用栈）。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 创建mock frame链 - 债券ETF的测试
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '511260'},
                all_symbols=['510300', '511260']
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 债券ETF的期望结果（condition信号的反向）
        expected = pd.Series([False, False, False, False, False, True, True, True, True, True], index=dates)
        pd.testing.assert_series_equal(result, expected)
    
    @patch('components.signals.switching.inspect')
    def test_empty_condition_signal(self, mock_inspect):
        """测试空的条件信号。"""
        empty_signal = pd.Series([], dtype=bool)
        
        # 创建mock frame链
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '510300'},
                all_symbols=['510300', '511260']
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(empty_signal)
        
        # 应该返回空的Series
        assert isinstance(result, pd.Series)
        assert len(result) == 0
        assert result.dtype == bool
    
    @patch('components.signals.switching.inspect')
    def test_three_symbol_pool(self, mock_inspect):
        """测试三个符号的池子（只使用前两个）。"""
        dates, condition_signal = create_test_condition_signal()
        
        # 模拟三个符号的池子
        frame_2 = Mock()
        frame_2.f_back = None
        frame_2.f_locals = {}
        
        frame_1 = Mock()
        frame_1.f_back = frame_2
        frame_1.f_locals = {
            'self': Mock(
                _evaluated_components={'_symbol': '510300'},
                all_symbols=['510300', '511260', '510500']  # 三个符号
            )
        }
        
        frame_0 = Mock()
        frame_0.f_back = frame_1
        frame_0.f_locals = {}
        
        mock_inspect.currentframe.return_value = frame_0
        
        switch = StockBondSwitch(params={'default_to_stock': True})
        result = switch.evaluate(condition_signal)
        
        # 应该按照第一个是股票的约定处理
        expected = pd.Series([True, True, True, True, True, False, False, False, False, False], index=dates)
        pd.testing.assert_series_equal(result, expected) 