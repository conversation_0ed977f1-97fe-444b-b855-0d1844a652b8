"""
Tests for momentum signal primitives.

This module tests the functionality of momentum-based signal primitives.
"""

import pytest
import pandas as pd
import numpy as np
from components.signals.momentum import Percent<PERSON>hange


def create_test_series():
    """Create test series with known percent changes."""
    dates = pd.date_range('2020-01-01', periods=5)
    # Series with different percent changes:
    # 1->2 (100%), 2->3 (50%), 3->2 (-33.33%), 2->1 (-50%)
    series = pd.Series([1, 2, 3, 2, 1], index=dates)
    return dates, series


def test_percent_change_basic():
    """Test basic PercentChange functionality."""
    dates, series = create_test_series()
    
    # Test with default params (1-period change, 0% threshold, greater comparison)
    signal = PercentChange()
    result = signal.evaluate(series)
    
    # First point has no previous data to compare with (NaN gets converted to False)
    # Remaining points: +100%, +50%, -33.33%, -50%
    # With 'greater' comparison to 0%, only points with positive change should be True
    expected = pd.Series([False, True, True, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific date
    assert signal.evaluate(series, date=dates[1]) == True
    assert signal.evaluate(series, date=dates[3]) == False


def test_percent_change_periods():
    """Test PercentChange with different period settings."""
    dates, series = create_test_series()
    
    # Test with 2-period change
    signal = PercentChange(params={'periods': 2, 'threshold': 0})
    result = signal.evaluate(series)
    
    # 2-period changes: NaN, NaN, 200%, 0%, -66.67%
    # With 'greater' comparison to 0%, only the 3rd point (index 2) should be True
    expected = pd.Series([False, False, True, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_percent_change_threshold():
    """Test PercentChange with different threshold values."""
    dates, series = create_test_series()
    
    # Test with higher threshold that only the largest change meets
    signal = PercentChange(params={'threshold': 75})
    result = signal.evaluate(series)
    
    # First point has no previous data to compare with
    # Remaining points: +100%, +50%, -33.33%, -50%
    # Only the +100% change exceeds the 75% threshold
    expected = pd.Series([False, True, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_percent_change_comparison():
    """Test PercentChange with different comparison types."""
    dates, series = create_test_series()
    
    # Test 'less' comparison (looking for negative changes)
    signal = PercentChange(params={'comparison': 'less', 'threshold': 0})
    result = signal.evaluate(series)
    
    # First point has no previous data to compare with
    # Remaining points: +100%, +50%, -33.33%, -50%
    # With 'less' comparison to 0%, only the negative changes should be True
    expected = pd.Series([False, False, False, True, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test 'equal' comparison
    # Create a series with a known exact change
    exact_dates = pd.date_range('2020-01-01', periods=3)
    exact_series = pd.Series([100, 110, 121], index=exact_dates)  # 10% changes
    
    signal = PercentChange(params={'comparison': 'equal', 'threshold': 10})
    result = signal.evaluate(exact_series)
    
    # First point has no previous data to compare with
    # Remaining points: +10%, +10%
    expected = pd.Series([False, True, True], index=exact_dates)
    pd.testing.assert_series_equal(result, expected)


def test_percent_change_absolute():
    """Test PercentChange with absolute value mode."""
    dates, series = create_test_series()
    
    # Test with absolute value and threshold
    signal = PercentChange(params={'is_absolute': True, 'threshold': 40})
    result = signal.evaluate(series)
    
    # First point has no previous data to compare with
    # Remaining points: +100%, +50%, -33.33%, -50%
    # With absolute values: 100%, 50%, 33.33%, 50%
    # With threshold 40%, points 1, 2, and 4 should be True
    expected = pd.Series([False, True, True, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_percent_change_validation():
    """Test parameter validation for PercentChange."""
    # Test invalid periods
    with pytest.raises(ValueError):
        PercentChange(params={'periods': 0})
    
    with pytest.raises(ValueError):
        PercentChange(params={'periods': 'invalid'})
    
    # Test invalid threshold
    with pytest.raises(ValueError):
        PercentChange(params={'threshold': 'invalid'})
    
    # Test invalid comparison
    with pytest.raises(ValueError):
        PercentChange(params={'comparison': 'invalid'})
    
    # Test invalid is_absolute
    with pytest.raises(ValueError):
        PercentChange(params={'is_absolute': 'invalid'})
    
    # Test with incorrect number of inputs
    dates = pd.date_range('2020-01-01', periods=3)
    series = pd.Series([1, 2, 3], index=dates)
    
    signal = PercentChange()
    
    with pytest.raises(ValueError):
        signal.evaluate()
    
    with pytest.raises(ValueError):
        signal.evaluate(series, series)


def test_percent_change_nan_handling():
    """Test NaN handling in PercentChange."""
    dates = pd.date_range('2020-01-01', periods=5)
    series = pd.Series([1, np.nan, 3, np.nan, 5], index=dates)
    
    signal = PercentChange()
    result = signal.evaluate(series)
    
    # Verify result is a valid Series
    assert isinstance(result, pd.Series)
    
    # NaN values should result in False (by default) for that position and the next
    # [0]: No previous value, should be False
    # [1]: NaN, should be False
    # [2]: Previous is NaN, should be False
    # [3]: NaN, should be False
    # [4]: Previous is NaN, should be False
    expected = pd.Series([False, False, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_percent_change_date_out_of_range():
    """Test behavior when evaluating out-of-range dates."""
    dates, series = create_test_series()
    out_of_range_date = pd.Timestamp('2019-12-31')
    
    signal = PercentChange()
    # Should return False for out-of-range dates
    assert signal.evaluate(series, date=out_of_range_date) == False
