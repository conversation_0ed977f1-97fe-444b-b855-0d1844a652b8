"""
Tests for extreme value indicators.

This module contains test cases for the extremes indicators, including
HighestV<PERSON>ue, LowestValue, PercentFromHighest, and PercentFromLowest.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from app.components.indicators.extremes import (
    HighestValue, LowestValue, PercentFromHighest, PercentFromLowest
)


def create_test_data():
    """Create test data with various price patterns.
    
    Returns:
        pd.DataFrame: DataFrame with OHLCV data
    """
    # Create date range
    dates = pd.date_range(start='2020-01-01', periods=20, freq='D')
    
    # Create price data with a pattern (up, down, up)
    highs = [110, 120, 130, 140, 150, 140, 130, 120, 110, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200]
    lows = [90, 100, 110, 120, 130, 120, 110, 100, 90, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180]
    closes = [100, 110, 120, 130, 140, 130, 120, 110, 100, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190]
    opens = [95, 105, 115, 125, 135, 125, 115, 105, 95, 85, 95, 105, 115, 125, 135, 145, 155, 165, 175, 185]
    volumes = [1000] * 20
    
    # Create DataFrame
    data = pd.DataFrame({
        'Open': opens,
        'High': highs,
        'Low': lows,
        'Close': closes,
        'Volume': volumes
    }, index=dates)
    
    return data


def test_highest_value_calculation():
    """Test HighestValue indicator calculation."""
    data = create_test_data()
    
    # Test with default parameters (Close column, period 14)
    indicator = HighestValue()
    result = indicator.calculate(data)
    
    # Check type and length
    assert isinstance(result, pd.Series)
    assert len(result) == len(data)
    
    # Manual calculation for verification
    period = indicator.params['period']
    column = indicator.params['column']
    for i in range(period - 1, len(data)):
        window = data[column].iloc[i-(period-1):i+1]
        expected = window.max()
        assert result.iloc[i] == expected
    
    # Check that NaN values are present for the first period-1 points
    assert np.all(np.isnan(result.iloc[:period-1]))
    
    # Test with custom parameters
    indicator = HighestValue(params={'column': 'High', 'period': 5})
    result = indicator.calculate(data)
    
    # Check specific values
    # At index 4 (5th day), the highest High in the last 5 days (0-4) should be 150
    assert result.iloc[4] == 150
    # At index 9 (10th day), the highest High in the last 5 days (5-9) should be 140
    assert result.iloc[9] == 140


def test_highest_value_dataframe_output():
    """Test HighestValue with DataFrame output format."""
    data = create_test_data()
    
    # Test with dataframe output
    indicator = HighestValue(params={'output_format': 'dataframe'})
    result = indicator.calculate(data)
    
    # Check type and structure
    assert isinstance(result, pd.DataFrame)
    assert 'highest' in result.columns
    assert len(result) == len(data)
    
    # Compare values with series output
    indicator_series = HighestValue(params={'output_format': 'series'})
    result_series = indicator_series.calculate(data)
    
    # 由于索引和名称可能不完全相同，我们只比较数值
    np.testing.assert_array_almost_equal(result['highest'].values, result_series.values)


def test_lowest_value_calculation():
    """Test LowestValue indicator calculation."""
    data = create_test_data()
    
    # Test with default parameters (Close column, period 14)
    indicator = LowestValue()
    result = indicator.calculate(data)
    
    # Check type and length
    assert isinstance(result, pd.Series)
    assert len(result) == len(data)
    
    # Manual calculation for verification
    period = indicator.params['period']
    column = indicator.params['column']
    for i in range(period - 1, len(data)):
        window = data[column].iloc[i-(period-1):i+1]
        expected = window.min()
        assert result.iloc[i] == expected
    
    # Check that NaN values are present for the first period-1 points
    assert np.all(np.isnan(result.iloc[:period-1]))
    
    # Test with custom parameters
    indicator = LowestValue(params={'column': 'Low', 'period': 5})
    result = indicator.calculate(data)
    
    # Check specific values
    # At index 4 (5th day), the lowest Low in the last 5 days (0-4) should be 90
    assert result.iloc[4] == 90
    # At index 9 (10th day), the lowest Low in the last 5 days (5-9) should be 80
    assert result.iloc[9] == 80


def test_lowest_value_dataframe_output():
    """Test LowestValue with DataFrame output format."""
    data = create_test_data()
    
    # Test with dataframe output
    indicator = LowestValue(params={'output_format': 'dataframe'})
    result = indicator.calculate(data)
    
    # Check type and structure
    assert isinstance(result, pd.DataFrame)
    assert 'lowest' in result.columns
    assert len(result) == len(data)
    
    # Compare values with series output
    indicator_series = LowestValue(params={'output_format': 'series'})
    result_series = indicator_series.calculate(data)
    
    # 由于索引和名称可能不完全相同，我们只比较数值
    np.testing.assert_array_almost_equal(result['lowest'].values, result_series.values)


def test_percent_from_highest_calculation():
    """Test PercentFromHighest indicator calculation."""
    data = create_test_data()
    
    # Test with default parameters
    indicator = PercentFromHighest()
    result = indicator.calculate(data)
    
    # Check type and length
    assert isinstance(result, pd.Series)
    assert len(result) == len(data)
    
    # Manual calculation for verification
    period = indicator.params['period']
    column = indicator.params['column']
    
    # Test with smaller period for easier verification
    indicator = PercentFromHighest(params={'period': 5})
    result = indicator.calculate(data)
    
    # At index 5, the highest Close in the last 5 days (1-5) is 140, current is 130
    # Percent from highest = 100 * (130 - 140) / 140 = -7.14...%
    expected_pct = 100 * (data['Close'].iloc[5] - 140) / 140
    assert np.isclose(result.iloc[5], expected_pct)
    
    # Test with dataframe output
    indicator = PercentFromHighest(params={'output_format': 'dataframe'})
    result_df = indicator.calculate(data)
    assert isinstance(result_df, pd.DataFrame)
    assert 'percent_from_highest' in result_df.columns


def test_percent_from_lowest_calculation():
    """Test PercentFromLowest indicator calculation."""
    data = create_test_data()
    
    # Test with default parameters
    indicator = PercentFromLowest()
    result = indicator.calculate(data)
    
    # Check type and length
    assert isinstance(result, pd.Series)
    assert len(result) == len(data)
    
    # Manual calculation for verification
    period = indicator.params['period']
    column = indicator.params['column']
    
    # Test with smaller period for easier verification
    indicator = PercentFromLowest(params={'period': 5})
    result = indicator.calculate(data)
    
    # At index 15, the lowest Close in the last 5 days (11-15) is 110, current is 150
    # Percent from lowest = 100 * (150 - 110) / 110 = 36.36...%
    expected_pct = 100 * (data['Close'].iloc[15] - 110) / 110
    assert np.isclose(result.iloc[15], expected_pct)
    
    # Test with dataframe output
    indicator = PercentFromLowest(params={'output_format': 'dataframe'})
    result_df = indicator.calculate(data)
    assert isinstance(result_df, pd.DataFrame)
    assert 'percent_from_lowest' in result_df.columns


def test_parameter_validation():
    """Test parameter validation for extreme value indicators."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        indicator = HighestValue(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        indicator = LowestValue(params={'period': -5})
    
    # Test invalid output format
    with pytest.raises(ValueError, match="Output format must be one of"):
        indicator = HighestValue(params={'output_format': 'invalid'})
    
    # Test invalid column
    indicator = HighestValue(params={'column': 'NonExistent'})
    data = create_test_data()
    with pytest.raises(ValueError, match="Column 'NonExistent' not found in input data"):
        indicator.calculate(data)


def test_empty_data_handling():
    """Test handling of empty or small data."""
    # Create empty DataFrame
    empty_data = pd.DataFrame(columns=['Open', 'High', 'Low', 'Close', 'Volume'])
    
    # Test with empty data
    indicator = HighestValue()
    result = indicator.calculate(empty_data)
    assert len(result) == 0
    
    # Create small data (less than period)
    small_data = create_test_data().iloc[:5]
    period = 10
    
    indicator = HighestValue(params={'period': period})
    result = indicator.calculate(small_data)
    
    # Should have all NaN values since window size > data size
    assert np.all(np.isnan(result))


def test_different_columns():
    """Test with different input columns."""
    data = create_test_data()
    
    # Test with High column
    high_indicator = HighestValue(params={'column': 'High', 'period': 5})
    high_result = high_indicator.calculate(data)
    
    # Test with Low column
    low_indicator = HighestValue(params={'column': 'Low', 'period': 5})
    low_result = low_indicator.calculate(data)
    
    # Results should be different
    assert not high_result.equals(low_result)
    
    # Values from High should be consistently higher
    valid_indices = ~np.isnan(high_result) & ~np.isnan(low_result)
    assert all(high_result[valid_indices] >= low_result[valid_indices])
