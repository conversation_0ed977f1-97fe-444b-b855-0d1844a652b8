"""
Tests for composite signal primitives.

This module tests the following signals:
- CrossAbove: detects when one signal crosses above another
- CrossBelow: detects when one signal crosses below another
"""

import pandas as pd
import numpy as np
import pytest

from components.signals.composite import CrossAbove, CrossBelow


def create_test_data():
    """Create test price data with crossovers."""
    dates = pd.date_range(start='2020-01-01', periods=10)
    # Create two series that cross each other
    series_a = pd.Series([10, 12, 13, 15, 14, 13, 12, 14, 16, 15], index=dates)
    series_b = pd.Series([11, 13, 12, 13, 15, 14, 13, 13, 14, 16], index=dates)
    return dates, series_a, series_b


def test_cross_above_basic():
    """Test basic CrossAbove functionality."""
    dates, series_a, series_b = create_test_data()
    
    signal = CrossAbove()
    result = signal.evaluate(series_a, series_b)
    
    # series_a crosses above series_b at indices 2 and 7
    expected = pd.Series([False, <PERSON>alse, <PERSON>, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>, <PERSON>alse, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific date evaluation
    assert signal.evaluate(series_a, series_b, date=dates[2]) == True
    assert signal.evaluate(series_a, series_b, date=dates[3]) == False


def test_cross_below_basic():
    """Test basic CrossBelow functionality."""
    dates, series_a, series_b = create_test_data()
    
    signal = CrossBelow()
    result = signal.evaluate(series_a, series_b)
    
    # series_a crosses below series_b at indices 4 and 9
    expected = pd.Series([False, False, False, False, True, False, False, False, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific date evaluation
    assert signal.evaluate(series_a, series_b, date=dates[4]) == True
    assert signal.evaluate(series_a, series_b, date=dates[5]) == False


def test_cross_with_threshold():
    """Test cross signals with threshold parameter."""
    dates, series_a, series_b = create_test_data()
    
    # CrossAbove with threshold=2
    # series_a must be > series_b + 2 to consider it crossed above
    signal = CrossAbove(params={'threshold': 2})
    result = signal.evaluate(series_a, series_b)
    
    # series_a crosses above series_b+2 only at index 7 (14 > 13+2)
    expected = pd.Series([False, False, False, False, False, False, False, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # CrossBelow with threshold=2
    # series_a must be < series_b - 2 to consider it crossed below
    signal = CrossBelow(params={'threshold': 2})
    result = signal.evaluate(series_a, series_b)
    
    # series_a crosses below series_b-2 only at index 9 (15 < 16-2)
    expected = pd.Series([False, False, False, False, False, False, False, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_cross_strict_mode():
    """Test cross signals with strict parameter."""
    # Create data with exact equality in previous period
    dates = pd.date_range(start='2020-01-01', periods=5)
    series_a = pd.Series([10, 12, 13, 14, 15], index=dates)
    series_b = pd.Series([10, 12, 12, 15, 14], index=dates)
    
    # Default strict=False
    cross_above = CrossAbove()
    result = cross_above.evaluate(series_a, series_b)
    
    # series_a crosses above series_b at index 2 (prev values equal, now a > b)
    expected = pd.Series([False, False, True, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # With strict=True
    cross_above = CrossAbove(params={'strict': True})
    result = cross_above.evaluate(series_a, series_b)
    
    # No crossing at index 2 because previous values are equal, not strictly a < b
    expected = pd.Series([False, False, False, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test CrossBelow with strict=True
    cross_below = CrossBelow(params={'strict': True})
    result = cross_below.evaluate(series_a, series_b)
    
    # series_a crosses below series_b at index 3 (prev a > b, now a < b)
    expected = pd.Series([False, False, False, True, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_cross_parameter_validation():
    """Test parameter validation for cross signals."""
    # Test invalid threshold
    with pytest.raises(ValueError, match="threshold must be a number"):
        CrossAbove(params={'threshold': 'invalid'})
    
    with pytest.raises(ValueError, match="threshold must be a number"):
        CrossBelow(params={'threshold': 'invalid'})
    
    # Test invalid strict
    with pytest.raises(ValueError, match="strict must be a boolean"):
        CrossAbove(params={'strict': 'invalid'})
    
    with pytest.raises(ValueError, match="strict must be a boolean"):
        CrossBelow(params={'strict': 'invalid'})


def test_cross_input_validation():
    """Test input validation for cross signals."""
    dates, series_a, series_b = create_test_data()
    
    cross_above = CrossAbove()
    cross_below = CrossBelow()
    
    # Test with no inputs
    with pytest.raises(ValueError, match="CrossAbove requires exactly two input"):
        cross_above.evaluate()
    
    with pytest.raises(ValueError, match="CrossBelow requires exactly two input"):
        cross_below.evaluate()
    
    # Test with one input
    with pytest.raises(ValueError, match="CrossAbove requires exactly two input"):
        cross_above.evaluate(series_a)
    
    with pytest.raises(ValueError, match="CrossBelow requires exactly two input"):
        cross_below.evaluate(series_a)
    
    # Test with three inputs
    with pytest.raises(ValueError, match="CrossAbove requires exactly two input"):
        cross_above.evaluate(series_a, series_b, series_a)
    
    with pytest.raises(ValueError, match="CrossBelow requires exactly two input"):
        cross_below.evaluate(series_a, series_b, series_a)


def test_cross_type_conversion():
    """Test type conversion in cross signals."""
    dates = pd.date_range(start='2020-01-01', periods=5)
    
    # String series that can be converted to numeric
    series_a = pd.Series(['10', '12', '13', '14', '15'], index=dates)
    series_b = pd.Series(['11', '13', '12', '15', '14'], index=dates)
    
    cross_above = CrossAbove()
    result = cross_above.evaluate(series_a, series_b)
    
    # After conversion to numeric, crosses at index 2
    expected = pd.Series([False, False, True, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Non-convertible series
    series_c = pd.Series(['a', 'b', 'c', 'd', 'e'], index=dates)
    
    result = cross_above.evaluate(series_c, series_b)
    # All values in series_c become NaN, so no crossings
    expected = pd.Series([False, False, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_out_of_range_date():
    """Test behavior with out-of-range dates."""
    dates, series_a, series_b = create_test_data()
    out_of_range_date = pd.Timestamp('2021-01-01')
    
    cross_above = CrossAbove()
    cross_below = CrossBelow()
    
    # Should return False for out-of-range dates
    assert cross_above.evaluate(series_a, series_b, date=out_of_range_date) == False
    assert cross_below.evaluate(series_a, series_b, date=out_of_range_date) == False
