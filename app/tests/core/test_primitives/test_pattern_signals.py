"""
Tests for pattern signal primitives.

This module tests the following signals:
- Streak: consecutive pattern detection
"""

import pandas as pd
import numpy as np
import pytest

from components.signals.pattern import Streak


def create_test_series():
    """Create test series for testing pattern signals."""
    dates = pd.date_range(start='2020-01-01', periods=10)
    # Create pattern: [T, T, F, F, T, T, T, F, T, F]
    series = pd.Series([True, True, False, False, True, True, True, False, True, False], index=dates)
    return dates, series


def test_streak_basic():
    """Test basic Streak functionality."""
    dates, series = create_test_series()
    
    # Test default parameters (min_length=2, match_type='true')
    streak = Streak()
    result = streak.evaluate(series)
    
    # Expected: Streaks of True with length >= 2
    # Positions 0-1: Streak of 2 True (True at both positions)
    # Positions 4-6: Streak of 3 True (True at all three positions)
    expected = pd.Series([True, True, False, False, True, True, True, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with min_length=3
    streak = Streak(params={'min_length': 3})
    result = streak.evaluate(series)
    
    # Expected: Streaks of True with length >= 3
    # Only positions 4-6 have a streak of 3 True
    expected = pd.Series([False, False, False, False, True, True, True, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_streak_match_types():
    """Test different match_types for Streak."""
    dates, series = create_test_series()
    
    # Test match_type='false'
    streak = Streak(params={'match_type': 'false', 'min_length': 2})
    result = streak.evaluate(series)
    
    # Expected: Streaks of False with length >= 2
    # Positions 2-3: Streak of 2 False
    expected = pd.Series([False, False, True, True, False, False, False, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test match_type='any'
    streak = Streak(params={'match_type': 'any', 'min_length': 3})
    result = streak.evaluate(series)
    
    # Expected: Streaks of same value (True or False) with length >= 3
    # Positions 4-6: Streak of 3 True
    expected = pd.Series([False, False, False, False, True, True, True, False, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_streak_max_length():
    """Test max_length parameter for Streak."""
    # Create a longer series with varying streak lengths
    dates = pd.date_range(start='2020-01-01', periods=15)
    series = pd.Series([
        True, True, True, True, True,  # Streak of 5 True
        False, False, False,           # Streak of 3 False
        True, True,                    # Streak of 2 True
        False, False, False, False,    # Streak of 4 False
        True                           # Streak of 1 True
    ], index=dates)
    
    # Test with min_length=2, max_length=3
    streak = Streak(params={'min_length': 2, 'max_length': 3})
    result = streak.evaluate(series)
    
    # Expected: Streaks of True with length >= 2 and <= 3
    # Positions 0-2: Part of a longer streak, but each position has a valid streak of 3 looking back
    # Positions 8-9: Streak of 2 True
    expected = pd.Series([
        True, True, True, False, False,  # First 3 positions have valid streaks
        False, False, False,             # False streaks are not counted with default match_type
        True, True,                      # Valid streak of 2 True
        False, False, False, False,      # False streaks not counted
        False                            # No streak
    ], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with match_type='false', min_length=3, max_length=4
    streak = Streak(params={'match_type': 'false', 'min_length': 3, 'max_length': 4})
    result = streak.evaluate(series)
    
    # Expected: Streaks of False with length >= 3 and <= 4
    # Positions 5-7: Streak of 3 False
    # Positions 10-13: Streak of 4 False
    expected = pd.Series([
        False, False, False, False, False,  # True values, not counted
        True, True, True,                   # Valid streak of 3 False
        False, False,                       # True values, not counted
        True, True, True, True,             # Valid streak of 4 False
        False                               # No streak
    ], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_streak_parameter_validation():
    """Test parameter validation for Streak."""
    # Test invalid min_length
    with pytest.raises(ValueError, match="min_length must be a positive integer"):
        Streak(params={'min_length': 0})
    
    with pytest.raises(ValueError, match="min_length must be a positive integer"):
        Streak(params={'min_length': -1})
    
    with pytest.raises(ValueError, match="min_length must be a positive integer"):
        Streak(params={'min_length': 'invalid'})
    
    # Test invalid max_length
    with pytest.raises(ValueError, match="max_length must be an integer"):
        Streak(params={'min_length': 2, 'max_length': 'invalid'})
    
    with pytest.raises(ValueError, match="max_length must be greater than or equal to min_length"):
        Streak(params={'min_length': 3, 'max_length': 2})
    
    # Test invalid match_type
    with pytest.raises(ValueError, match="match_type must be one of"):
        Streak(params={'match_type': 'invalid'})


def test_streak_input_validation():
    """Test input validation for Streak."""
    dates, series = create_test_series()
    
    streak = Streak()
    
    # Test with no inputs
    with pytest.raises(ValueError, match="Streak requires exactly one input series"):
        streak.evaluate()
    
    # Test with multiple inputs
    with pytest.raises(ValueError, match="Streak requires exactly one input series"):
        streak.evaluate(series, series)


def test_streak_boolean_conversion():
    """Test boolean conversion in Streak."""
    dates = pd.date_range(start='2020-01-01', periods=5)
    # Non-boolean series
    series = pd.Series([0, 1, 2, 0, 1], index=dates)
    
    streak = Streak(params={'min_length': 2})
    result = streak.evaluate(series)
    
    # Expected: After boolean conversion, series becomes [F, T, T, F, T]
    # Only positions 1-2 have a streak of 2 True
    expected = pd.Series([False, True, True, False, False], index=dates)
    pd.testing.assert_series_equal(result, expected)


def test_streak_specific_date():
    """Test streak evaluation for a specific date."""
    dates, series = create_test_series()
    
    streak = Streak()
    
    # Date at index 1 - Part of a streak of 2 True
    assert streak.evaluate(series, date=dates[1]) == True
    
    # Date at index 2 - Not part of a True streak
    assert streak.evaluate(series, date=dates[2]) == False
    
    # Date at index 6 - Part of a streak of 3 True
    assert streak.evaluate(series, date=dates[6]) == True
    
    # Test with match_type='false'
    streak = Streak(params={'match_type': 'false'})
    
    # Date at index 3 - Part of a streak of 2 False
    assert streak.evaluate(series, date=dates[3]) == True
    
    # Out of range date
    out_of_range_date = pd.Timestamp('2021-01-01')
    assert streak.evaluate(series, date=out_of_range_date) == False
