"""
Tests for logical signal primitives.

This module tests the following signals:
- And
- Or
- Not
"""

import pytest
import pandas as pd
import numpy as np
from components.signals.logical import And, Or, Not


def create_test_series():
    """Create test series with known logical patterns."""
    dates = pd.date_range('2020-01-01', periods=5)
    # True at indices 1, 3
    series_a = pd.Series([False, True, False, True, False], index=dates)
    # True at indices 2, 3
    series_b = pd.Series([False, False, True, True, False], index=dates)
    return dates, series_a, series_b


def test_and_basic():
    """Test basic And functionality."""
    dates, series_a, series_b = create_test_series()
    
    signal = And()
    result = signal.evaluate(series_a, series_b)
    
    # True at index 3 where both series are True
    expected = pd.Series([False, False, False, True, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific dates
    assert signal.evaluate(series_a, series_b, date=dates[3]) == True
    assert signal.evaluate(series_a, series_b, date=dates[0]) == False


def test_or_basic():
    """Test basic Or functionality."""
    dates, series_a, series_b = create_test_series()
    
    signal = Or()
    result = signal.evaluate(series_a, series_b)
    
    # True at indices 1, 2, 3 where either series is True
    expected = pd.Series([False, True, True, True, False], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific dates
    assert signal.evaluate(series_a, series_b, date=dates[1]) == True
    assert signal.evaluate(series_a, series_b, date=dates[0]) == False


def test_not_basic():
    """Test basic Not functionality."""
    dates, series_a, _ = create_test_series()
    
    signal = Not()
    result = signal.evaluate(series_a)
    
    # True at indices 0, 2, 4 where series is False
    expected = pd.Series([True, False, True, False, True], index=dates)
    pd.testing.assert_series_equal(result, expected)
    
    # Test specific dates
    assert signal.evaluate(series_a, date=dates[0]) == True
    assert signal.evaluate(series_a, date=dates[1]) == False


def test_logical_param_validation():
    """Test parameter validation for logical operators."""
    # Test invalid fill_method for And
    with pytest.raises(ValueError, match="fill_method must be one of"):
        And(params={'fill_method': 'invalid'})
        
    # Test invalid fill_method for Or
    with pytest.raises(ValueError, match="fill_method must be one of"):
        Or(params={'fill_method': 'invalid'})
        
    # Test invalid fill_method for Not
    with pytest.raises(ValueError, match="fill_method must be one of"):
        Not(params={'fill_method': 'invalid'})
        
    # Test invalid preserve_na for Not
    with pytest.raises(ValueError, match="preserve_na must be a boolean"):
        Not(params={'preserve_na': 'invalid'})


def test_logical_input_validation():
    """Test input validation for logical operators."""
    data = pd.Series([True, False, True])
    
    # Test And with wrong number of inputs
    and_op = And()
    with pytest.raises(ValueError, match="And requires exactly two input"):
        and_op.evaluate(data)
    with pytest.raises(ValueError, match="And requires exactly two input"):
        and_op.evaluate(data, data, data)
        
    # Test Or with wrong number of inputs
    or_op = Or()
    with pytest.raises(ValueError, match="Or requires exactly two input"):
        or_op.evaluate(data)
    with pytest.raises(ValueError, match="Or requires exactly two input"):
        or_op.evaluate(data, data, data)
        
    # Test Not with wrong number of inputs
    not_op = Not()
    with pytest.raises(ValueError, match="Not requires exactly one input"):
        not_op.evaluate()
    with pytest.raises(ValueError, match="Not requires exactly one input"):
        not_op.evaluate(data, data)


def test_date_out_of_range():
    """Test evaluation for specific date that's out of range."""
    # Create test data with dates
    dates = pd.date_range(start='2020-01-01', periods=5)
    data1 = pd.Series([True, False, True, False, True], index=dates)
    data2 = pd.Series([False, True, True, False, False], index=dates)
    
    # Initialize operators
    and_op = And()
    or_op = Or()
    not_op = Not()
    
    # Test date in range
    assert and_op.evaluate(data1, data2, date=dates[2]) == True
    assert or_op.evaluate(data1, data2, date=dates[1]) == True
    assert not_op.evaluate(data1, date=dates[1]) == True
    
    # Test date out of range
    out_of_range_date = pd.Timestamp('2020-02-01')
    assert and_op.evaluate(data1, data2, date=out_of_range_date) == False
    assert or_op.evaluate(data1, data2, date=out_of_range_date) == False
    assert not_op.evaluate(data1, date=out_of_range_date) == False


def test_fill_methods():
    """Test fill methods for handling NaN values."""
    # Create test data with NaN values
    data1 = pd.Series([True, False, np.nan, True, False])
    data2 = pd.Series([True, np.nan, False, False, np.nan])
    
    # Test ffill (forward fill)
    and_ffill = And(params={'fill_method': 'ffill'})
    result = and_ffill.evaluate(data1, data2)
    # Expected: NaN values forward filled before AND operation
    # data1 after ffill: [True, False, False, True, False]
    # data2 after ffill: [True, True, False, False, False]
    expected = pd.Series([True, False, False, False, False])
    pd.testing.assert_series_equal(result, expected, check_names=False)
    
    # Test bfill (backward fill)
    or_bfill = Or(params={'fill_method': 'bfill'})
    result = or_bfill.evaluate(data1, data2)
    # Expected: NaN values backward filled before OR operation
    # data1 after bfill: [True, False, True, True, False]
    # data2 after bfill: [True, False, False, False, False] (After subsequent fillna(False))
    # We expect True wherever either series has True AFTER NaN handling
    expected = pd.Series([True, False, True, True, False])
    pd.testing.assert_series_equal(result, expected, check_names=False)
    
    # Test Not with ffill
    not_ffill = Not(params={'fill_method': 'ffill'})
    result = not_ffill.evaluate(data1)
    # Expected: NaN values forward filled before NOT operation
    # data1 after ffill: [True, False, False, True, False]
    expected = pd.Series([False, True, True, False, True])
    pd.testing.assert_series_equal(result, expected, check_names=False)
    
    # Test Not with preserve_na=True
    not_preserve = Not(params={'preserve_na': True})
    result = not_preserve.evaluate(data1)
    # Expected: NaN values remain NaN after NOT operation
    expected = pd.Series([False, True, np.nan, False, True])
    pd.testing.assert_series_equal(result, expected, check_names=False)


def test_boolean_conversion():
    """Test conversion of non-boolean values to boolean."""
    dates = pd.date_range('2020-01-01', periods=3)
    # These will convert to: [True, True, False]
    series_a = pd.Series([1, "value", 0], index=dates)
    # These will convert to: [False, True, True]
    series_b = pd.Series([0, 1, "value"], index=dates)
    
    # Test AND operation
    and_signal = And()
    and_result = and_signal.evaluate(series_a, series_b)
    and_expected = pd.Series([False, True, False], index=dates)
    pd.testing.assert_series_equal(and_result, and_expected)
    
    # Test OR operation
    or_signal = Or()
    or_result = or_signal.evaluate(series_a, series_b)
    or_expected = pd.Series([True, True, True], index=dates)
    pd.testing.assert_series_equal(or_result, or_expected)
    
    # Test NOT operation
    not_signal = Not()
    not_result = not_signal.evaluate(series_a)
    not_expected = pd.Series([False, False, True], index=dates)
    pd.testing.assert_series_equal(not_result, not_expected)
