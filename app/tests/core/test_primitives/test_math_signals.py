"""
Tests for mathematical operation signals.

This module contains test cases for the various mathematical operation signals,
including Add, Subtract, Multiply, and Divide.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from app.components.signals.math import Add, Subtract, Multiply, Divide


def create_test_series():
    """Create test series with sample data.
    
    Returns:
        Tuple[pd.Series, pd.Series, pd.Series]: Three test Series with different patterns
    """
    dates = pd.date_range(start='2020-01-01', periods=10, freq='D')
    
    # First series: simple sequential values
    series1 = pd.Series([10, 20, 30, 40, 50, 60, 70, 80, 90, 100], index=dates)
    
    # Second series: different pattern
    series2 = pd.Series([5, 10, 15, 20, 25, 30, 35, 40, 45, 50], index=dates)
    
    # Third series: static threshold
    series3 = pd.Series([30, 30, 30, 30, 30, 100, 100, 100, 100, 100], index=dates)
    
    return series1, series2, series3


def test_add_signal():
    """Test the Add signal."""
    series1, series2, series3 = create_test_series()
    
    # Test with threshold parameter
    add_signal = Add(params={'threshold': 50})
    result = add_signal.evaluate(series1, series2)
    
    # First 5 values should be False (sum < 50), last 5 should be True (sum >= 50)
    expected = pd.Series([False, False, False, True, True, True, True, True, True, True], index=series1.index)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with a third series as threshold
    add_signal = Add(params={'threshold': None})
    result = add_signal.evaluate(series1, series2, series3)
    
    # Compare (series1 + series2) > series3
    expected = pd.Series([(s1 + s2) > s3 for s1, s2, s3 in zip(series1, series2, series3)], index=series1.index)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with single date
    test_date = series1.index[5]  # 2020-01-06
    single_result = add_signal.evaluate(series1, series2, series3, date=test_date)
    assert single_result == expected[test_date]


def test_subtract_signal():
    """Test the Subtract signal."""
    series1, series2, series3 = create_test_series()
    
    # Test with threshold parameter
    subtract_signal = Subtract(params={'threshold': 20, 'comparison': 'greater'})
    result = subtract_signal.evaluate(series1, series2)
    
    # Calculate expected results: (series1 - series2) > 20
    expected = pd.Series([(s1 - s2) > 20 for s1, s2 in zip(series1, series2)], index=series1.index)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with absolute value
    subtract_signal = Subtract(params={'threshold': 20, 'absolute': True, 'comparison': 'greater'})
    
    # Test with different comparison
    subtract_signal = Subtract(params={'threshold': 0, 'comparison': 'less'})
    result = subtract_signal.evaluate(series2, series1)  # Reversing operands to get negative result
    
    # Should all be true since series2 < series1 for all points
    assert all(result)


def test_multiply_signal():
    """Test the Multiply signal."""
    series1, series2, series3 = create_test_series()
    
    # Test with threshold parameter
    multiply_signal = Multiply(params={'threshold': 1000, 'comparison': 'greater'})
    result = multiply_signal.evaluate(series1, series2)
    
    # Calculate expected results: (series1 * series2) > 1000
    expected = pd.Series([(s1 * s2) > 1000 for s1, s2 in zip(series1, series2)], index=series1.index)
    pd.testing.assert_series_equal(result, expected)
    
    # Test with equal comparison
    multiply_signal = Multiply(params={'comparison': 'equal'})
    
    # Test multiplying by a constant series
    const_series = pd.Series([2] * len(series1), index=series1.index)
    result = multiply_signal.evaluate(series1, const_series, 2 * series1)  # s1 * 2 == 2 * s1
    assert all(result)  # All should be True


def test_divide_signal():
    """Test the Divide signal."""
    # 创建简单测试数据
    dates = pd.date_range(start='2020-01-01', periods=3)
    numerator = pd.Series([10, 20, 30], index=dates)
    denominator = pd.Series([0, 4, 0], index=dates)
    
    # 使用简单的大于零比较
    divide_signal = Divide(params={'threshold': 0, 'comparison': 'greater'})
    
    # 直接评估并打印结果，以便调试
    result = divide_signal.evaluate(numerator, denominator)
    print(f"Division result: {result}")
    
    # 检查除零位置 - 在pandas的BooleanDtype中，空值是pd.NA
    assert pd.isna(result.iloc[0]), f"Expected NA at index 0, got {result.iloc[0]}"
    assert pd.isna(result.iloc[2]), f"Expected NA at index 2, got {result.iloc[2]}"
    
    # 检查有效除法 - 使用==而不是is比较布尔值
    assert result.iloc[1] == True, f"Expected True at index 1, got {result.iloc[1]}"


def test_parameter_validation():
    """Test parameter validation for math operation signals."""
    # Test invalid operation
    with pytest.raises(ValueError, match="Operation must be one of"):
        MathOperation = Add  # Using Add as a stand-in for MathOperation
        signal = MathOperation(params={'operation': 'invalid_op'})
    
    # Test invalid comparison
    with pytest.raises(ValueError, match="Comparison must be one of"):
        signal = Add(params={'comparison': 'invalid_comp'})
    
    # Test invalid threshold type
    with pytest.raises(ValueError, match="Threshold must be a number or None"):
        signal = Add(params={'threshold': 'not_a_number'})
    
    # Test invalid absolute value
    with pytest.raises(ValueError, match="Absolute must be a boolean"):
        signal = Add(params={'absolute': 'not_a_bool'})


def test_input_validation():
    """Test input validation in evaluate method."""
    series1, series2, _ = create_test_series()
    
    # Test with threshold but not enough inputs
    add_signal = Add(params={'threshold': None})
    with pytest.raises(ValueError, match="At least 3 inputs required"):
        add_signal.evaluate(series1, series2)
    
    # Test with not enough inputs
    add_signal = Add(params={'threshold': 50})
    with pytest.raises(ValueError, match="At least 2 inputs required"):
        add_signal.evaluate(series1)


def test_different_comparisons():
    """Test different comparison types."""
    # 创建简单的测试数据
    dates = pd.date_range(start='2020-01-01', periods=5, freq='D')
    a = pd.Series([10, 20, 30, 40, 50], index=dates)
    b = pd.Series([5, 15, 25, 35, 45], index=dates)
    
    # 测试大于等于比较
    signal_ge = Add(params={'threshold': 40, 'comparison': 'greater_equal'})
    result_ge = signal_ge.evaluate(a, b)
    
    # 结果应该是 Series 类型
    assert isinstance(result_ge, pd.Series), "Expected result to be a pandas Series"
    
    # 验证结果 - 前两个应该是 False (15, 35 < 40)，后三个应该是 True (55, 75, 95 >= 40)
    assert not result_ge.iloc[0], "Expected False for 10+5 >= 40"
    assert not result_ge.iloc[1], "Expected False for 20+15 >= 40"
    assert result_ge.iloc[2], "Expected True for 30+25 >= 40"
    assert result_ge.iloc[3], "Expected True for 40+35 >= 40"
    assert result_ge.iloc[4], "Expected True for 50+45 >= 40"
    
    # 测试等于比较
    signal_eq = Add(params={'threshold': 40, 'comparison': 'equal'})
    result_eq = signal_eq.evaluate(a, b)
    
    # 验证结果 - 只有 a.iloc[1] + b.iloc[1] = 20+15 = 35 与 40 接近
    for i in range(len(a)):
        expected = np.isclose(a.iloc[i] + b.iloc[i], 40)
        assert result_eq.iloc[i] == expected, f"Expected {'True' if expected else 'False'} at index {i} for {a.iloc[i]}+{b.iloc[i]} == 40"


def test_absolute_value():
    """Test absolute value setting."""
    # Create series with negative values
    dates = pd.date_range(start='2020-01-01', periods=5, freq='D')
    series1 = pd.Series([10, -20, 30, -40, 50], index=dates)
    series2 = pd.Series([5, 10, -15, 20, -25], index=dates)
    
    # Test without absolute value
    subtract_signal = Subtract(params={'threshold': 20, 'comparison': 'greater', 'absolute': False})
    result_no_abs = subtract_signal.evaluate(series1, series2)
    
    # Test with absolute value
    subtract_signal = Subtract(params={'threshold': 20, 'comparison': 'greater', 'absolute': True})
    result_abs = subtract_signal.evaluate(series1, series2)
    
    # Check that results are different (absolute value matters)
    assert not np.array_equal(result_no_abs.values, result_abs.values)
    
    # Check specific cases
    # For s1=-20, s2=10, result=-30, abs(-30)=30 which is > 20
    assert result_abs.iloc[1] == True
    # For s1=-20, s2=10, result=-30 which is not > 20
    assert result_no_abs.iloc[1] == False
