"""
Tests for moving average indicator primitives.

This module tests the functionality of moving average indicator primitives,
including Simple Moving Average (SMA) and Exponential Moving Average (EMA).
"""

import pytest
import pandas as pd
import numpy as np
from components.indicators.moving_averages import SMA, EMA

def test_sma_defaults():
    """Test SMA indicator with default parameters."""
    sma = SMA()
    assert sma.params['period'] == 20
    assert sma.params['column'] == 'Close'

def test_sma_custom_params():
    """Test SMA indicator with custom parameters."""
    sma = SMA(params={'period': 10, 'column': 'Open'})
    assert sma.params['period'] == 10
    assert sma.params['column'] == 'Open'

def test_sma_validation():
    """Test SMA parameter validation."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        SMA(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        SMA(params={'period': -1})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        SMA(params={'period': 'invalid'})
    
    # Test invalid column
    with pytest.raises(ValueError, match="Column must be a string"):
        SMA(params={'column': 123})

def test_sma_calculation():
    """Test SMA calculation against known values."""
    # Create sample data
    data = pd.DataFrame({
        'Close': [1, 2, 3, 4, 5],
        'Open': [1.1, 2.1, 3.1, 4.1, 5.1]
    })
    
    # Test with period=3
    sma = SMA(params={'period': 3})
    result = sma.calculate(data)
    
    # Expected values (no min_periods):
    # First two values should be NaN
    # [NaN, NaN, (1+2+3)/3, (2+3+4)/3, (3+4+5)/3]
    expected = pd.Series([np.nan, np.nan, 2.0, 3.0, 4.0], index=data.index)
    
    # Use pandas testing for better NaN comparison
    pd.testing.assert_series_equal(result, expected, check_names=False)

def test_sma_missing_column():
    """Test SMA behavior with missing column."""
    sma = SMA(params={'column': 'NonExistent'})
    data = pd.DataFrame({'Close': [1, 2, 3]})
    
    with pytest.raises(KeyError, match="Column NonExistent not found in data"):
        sma.calculate(data)

def test_sma_nan_handling():
    """Test SMA handling of NaN values."""
    data = pd.DataFrame({
        'Close': [1, np.nan, 3, 4, 5],
        'Open': [1.1, 2.1, 3.1, 4.1, 5.1]
    })
    
    # Test with period=3
    sma = SMA(params={'period': 3})
    result = sma.calculate(data)
    
    # Expected values (no min_periods):
    # SMA calculation skips NaNs, but needs 'period' valid numbers
    # Index 0: Needs 3 values, has 1 -> NaN
    # Index 1: Needs 3 values, has 1 (skip NaN) -> NaN
    # Index 2: Needs 3 values, has 2 (1, 3) -> NaN 
    # Index 3: Needs 3 values, has 2 (3, 4) -> NaN
    # Index 4: Needs 3 values, has 3 (3, 4, 5) -> (3+4+5)/3 = 4.0
    expected = pd.Series([np.nan, np.nan, np.nan, np.nan, 4.0], index=data.index)
    
    # Verify the result is a valid Series
    assert isinstance(result, pd.Series)
    # Verify it has the same length as input
    assert len(result) == len(data)
    # Use pandas testing
    pd.testing.assert_series_equal(result, expected, check_names=False)


# EMA Tests

def test_ema_defaults():
    """Test EMA indicator with default parameters."""
    ema = EMA()
    assert ema.params['period'] == 20
    assert ema.params['column'] == 'Close'
    assert ema.params['adjust'] == False

def test_ema_custom_params():
    """Test EMA indicator with custom parameters."""
    ema = EMA(params={'period': 10, 'column': 'Open', 'adjust': True})
    assert ema.params['period'] == 10
    assert ema.params['column'] == 'Open'
    assert ema.params['adjust'] == True

def test_ema_validation():
    """Test EMA parameter validation."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        EMA(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        EMA(params={'period': -1})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        EMA(params={'period': 'invalid'})
    
    # Test invalid column
    with pytest.raises(ValueError, match="Column must be a string"):
        EMA(params={'column': 123})
    
    # Test invalid adjust
    with pytest.raises(ValueError, match="Adjust must be a boolean"):
        EMA(params={'adjust': 'invalid'})

def test_ema_calculation():
    """Test EMA calculation against known values."""
    # Create sample data
    data = pd.DataFrame({
        'Close': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
        'Open': [1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 8.1, 9.1, 10.1]
    })
    
    # Test with period=3 (alpha = 2/(3+1) = 0.5)
    ema = EMA(params={'period': 3})
    result = ema.calculate(data)
    
    # Expected values using pandas ewm with alpha=0.5
    # First value should match the original
    # Subsequent values: EMA_t = alpha * price_t + (1-alpha) * EMA_{t-1}
    alpha = 0.5
    expected = pd.Series({
        0: 1.0,  # First value matches original
        1: 1.0 * (1-alpha) + 2.0 * alpha,  # 1.5
        2: 1.5 * (1-alpha) + 3.0 * alpha,  # 2.25
        3: 2.25 * (1-alpha) + 4.0 * alpha,  # 3.125
        4: 3.125 * (1-alpha) + 5.0 * alpha,  # 4.0625
        5: 4.0625 * (1-alpha) + 6.0 * alpha,  # 5.03125
        6: 5.03125 * (1-alpha) + 7.0 * alpha,  # 6.015625
        7: 6.015625 * (1-alpha) + 8.0 * alpha,  # 7.0078125
        8: 7.0078125 * (1-alpha) + 9.0 * alpha,  # 8.00390625
        9: 8.00390625 * (1-alpha) + 10.0 * alpha,  # 9.001953125
    })
    
    # Use numpy.testing for float comparison with tolerance
    np.testing.assert_array_almost_equal(result, expected, decimal=6)

def test_ema_missing_column():
    """Test EMA behavior with missing column."""
    ema = EMA(params={'column': 'NonExistent'})
    data = pd.DataFrame({'Close': [1, 2, 3]})
    
    with pytest.raises(KeyError, match="Column NonExistent not found in data"):
        ema.calculate(data)

def test_ema_nan_handling():
    """Test EMA handling of NaN values."""
    data = pd.DataFrame({
        'Close': [1, np.nan, 3, 4, 5],
        'Open': [1.1, 2.1, 3.1, 4.1, 5.1]
    })
    
    # Test with period=3
    ema = EMA(params={'period': 3})
    result = ema.calculate(data)
    
    # Verify the result is a valid Series
    assert isinstance(result, pd.Series)
    # Verify it has the same length as input
    assert len(result) == len(data)
    # Check that there are no NaN values in the result
    assert not result.isna().any()
    # First value should be 1.0 (first observation)
    assert result[0] == 1.0
    
    # Get the actual values calculated by pandas ewm for validation
    # Note: pandas ewm handles NaN values differently than our manual calculation:
    # When it encounters a NaN, the EMA calculation continues with the available data
    alpha = 0.5
    # Second value would normally be 1.0 if NaN is ignored
    # Third value: After NaN, pandas ewm continues with alpha smoothing from previous value
    # Validate that the results are reasonable with a tolerance of 0.001
    assert abs(result[1] - 1.0) < 0.001
    # Don't specify exact values as pandas behavior with NaN can vary slightly
    # Just check the values are monotonically increasing as expected
    assert result[2] > result[1]
    assert result[3] > result[2]
    assert result[4] > result[3]
    
    # Additionally, verify that last value is reasonably close to what we'd expect
    # with a continued exponential smoothing
    assert 4.0 < result[4] < 5.0
