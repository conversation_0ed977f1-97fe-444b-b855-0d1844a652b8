"""
Tests for volatility indicator primitives.

This module tests the following indicators:
- ATR (Average True Range)
- Bollinger Bands
"""

import pandas as pd
import numpy as np
import pytest
from pandas.testing import assert_series_equal, assert_frame_equal

from components.indicators.volatility import ATR, <PERSON>llingerBand<PERSON>


def create_test_data():
    """Create test price data."""
    # Create sample price data
    dates = pd.date_range(start='2020-01-01', periods=20, freq='D')
    data = pd.DataFrame({
        'Open': [100, 102, 104, 103, 105, 107, 108, 109, 110, 111, 
                 112, 111, 110, 109, 108, 107, 106, 105, 104, 103],
        'High': [105, 106, 107, 105, 108, 110, 111, 112, 113, 114, 
                 115, 114, 113, 112, 111, 110, 109, 108, 107, 106],
        'Low': [98, 100, 101, 100, 102, 105, 106, 107, 108, 109, 
                110, 108, 107, 106, 105, 104, 103, 102, 101, 100],
        'Close': [102, 104, 103, 105, 107, 108, 109, 110, 111, 112, 
                 111, 110, 109, 108, 107, 106, 105, 104, 103, 102],
        'Volume': [1000, 1200, 1300, 1200, 1400, 1500, 1600, 1700, 1800, 1900,
                  2000, 1900, 1800, 1700, 1600, 1500, 1400, 1300, 1200, 1100]
    }, index=dates)
    return data


def test_atr_basic():
    """Test basic ATR calculation."""
    # Create test data
    data = create_test_data()
    
    # Create ATR with default parameters
    atr = ATR()
    result = atr.calculate(data)
    
    # Verify result shape
    assert len(result) == len(data)
    assert isinstance(result, pd.Series)
    
    # Ensure non-NaN values are positive
    assert (result.dropna() >= 0).all()
    
    # Test with custom period
    atr_custom = ATR(params={'period': 5})
    result_custom = atr_custom.calculate(data)
    
    # Results should be different with different periods
    assert not result.equals(result_custom)


def test_atr_methods():
    """Test ATR calculation methods (SMA vs EMA)."""
    data = create_test_data()
    
    # Calculate ATR with SMA method
    atr_sma = ATR(params={'period': 10, 'method': 'sma'})
    result_sma = atr_sma.calculate(data)
    
    # Calculate ATR with EMA method
    atr_ema = ATR(params={'period': 10, 'method': 'ema'})
    result_ema = atr_ema.calculate(data)
    
    # Results should be different for different methods
    assert not result_sma.equals(result_ema)
    
    # Check if both methods produce NaN initially (due to lookback period)
    assert pd.isna(result_sma.iloc[0]) and pd.isna(result_ema.iloc[0])


def test_atr_param_validation():
    """Test ATR parameter validation."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        ATR(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        ATR(params={'period': -5})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        ATR(params={'period': 'invalid'})
    
    # Test invalid method
    with pytest.raises(ValueError, match="Method must be one of"):
        ATR(params={'method': 'invalid'})


def test_atr_input_validation():
    """Test ATR input data validation."""
    # Create incomplete data
    data = pd.DataFrame({
        'Close': [100, 101, 102, 103, 104]
    })
    
    # ATR should raise error when required columns are missing
    atr = ATR()
    with pytest.raises(KeyError, match="Required column 'High' not found"):
        atr.calculate(data)


def test_bollinger_basic():
    """Test basic Bollinger Bands calculation."""
    # Create test data
    data = create_test_data()
    
    # Create Bollinger Bands with default parameters
    bb = BollingerBands()
    result = bb.calculate(data)
    
    # Verify result structure
    assert isinstance(result, dict)
    assert set(result.keys()) == {'upper', 'middle', 'lower'}
    
    # Verify each band is a Series with the same length as data
    for band in result.values():
        assert isinstance(band, pd.Series)
        assert len(band) == len(data)
    
    # Upper band should always be higher than middle band
    assert (result['upper'] >= result['middle']).all()
    
    # Lower band should always be lower than middle band
    assert (result['lower'] <= result['middle']).all()
    
    # Test distance between bands
    # For the default 2 std_dev, the distance should be consistent
    upper_diff = result['upper'] - result['middle']
    lower_diff = result['middle'] - result['lower']
    assert_series_equal(upper_diff, lower_diff, check_names=False)


def test_bollinger_parameters():
    """Test Bollinger Bands with different parameters."""
    data = create_test_data()
    
    # Default parameters (period=20, std_dev=2.0)
    bb_default = BollingerBands()
    result_default = bb_default.calculate(data)
    
    # Custom parameters (period=10, std_dev=1.5)
    bb_custom = BollingerBands(params={'period': 10, 'std_dev': 1.5})
    result_custom = bb_custom.calculate(data)
    
    # Bands should be different with different parameters
    for band in ['upper', 'middle', 'lower']:
        assert not result_default[band].equals(result_custom[band])
    
    # With lower std_dev, bands should be narrower
    default_width = result_default['upper'] - result_default['lower']
    custom_width = result_custom['upper'] - result_custom['lower']
    # This only applies if we compare the same period, but we're using
    # different periods here, so we just verify they're different
    assert not default_width.equals(custom_width)
    
    # Test with different column
    bb_ohlc = BollingerBands(params={'column': 'Open'})
    result_ohlc = bb_ohlc.calculate(data)
    
    # Should be different from default (using 'Close')
    assert not result_default['middle'].equals(result_ohlc['middle'])


def test_bollinger_methods():
    """Test Bollinger Bands with different calculation methods."""
    data = create_test_data()
    
    # Calculate with SMA method
    bb_sma = BollingerBands(params={'method': 'sma'})
    result_sma = bb_sma.calculate(data)
    
    # Calculate with EMA method
    bb_ema = BollingerBands(params={'method': 'ema'})
    result_ema = bb_ema.calculate(data)
    
    # Results should be different for different methods
    for band in ['upper', 'middle', 'lower']:
        assert not result_sma[band].equals(result_ema[band])


def test_bollinger_param_validation():
    """Test Bollinger Bands parameter validation."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        BollingerBands(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        BollingerBands(params={'period': -5})
    
    # Test invalid std_dev
    with pytest.raises(ValueError, match="std_dev must be a positive number"):
        BollingerBands(params={'std_dev': 0})
    
    with pytest.raises(ValueError, match="std_dev must be a positive number"):
        BollingerBands(params={'std_dev': -1.5})
    
    # Test invalid column
    with pytest.raises(ValueError, match="Column must be a string"):
        BollingerBands(params={'column': 123})
    
    # Test invalid method
    with pytest.raises(ValueError, match="Method must be one of"):
        BollingerBands(params={'method': 'invalid'})


def test_bollinger_input_validation():
    """Test Bollinger Bands input data validation."""
    # Create data missing the required column
    data = pd.DataFrame({
        'Open': [100, 101, 102, 103, 104],
        'High': [105, 106, 107, 108, 109],
        'Low': [95, 96, 97, 98, 99]
        # Missing 'Close' column
    })
    
    # Default Bollinger Bands uses 'Close', should raise error
    bb = BollingerBands()
    with pytest.raises(KeyError, match="Column Close not found"):
        bb.calculate(data)
    
    # But should work when we specify a column that exists
    bb_custom = BollingerBands(params={'column': 'Open'})
    result = bb_custom.calculate(data)
    assert isinstance(result, dict)
    assert set(result.keys()) == {'upper', 'middle', 'lower'}
