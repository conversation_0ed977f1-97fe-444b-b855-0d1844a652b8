"""
Tests for base primitive components.

This module tests the functionality of the base primitive classes.
"""

import pytest
import pandas as pd
from components.base.primitives import (
    BaseComponentPrimitive,
    BaseIndicatorPrimitive,
    BaseSignalPrimitive
)

# Test implementation classes
class TestComponent(BaseComponentPrimitive):
    def get_default_params(self):
        return {'param1': 'default', 'param2': 42}
    
    def validate_params(self):
        if 'param1' not in self.params:
            raise ValueError("param1 is required")
        if not isinstance(self.params['param2'], int):
            raise ValueError("param2 must be an integer")

class TestIndicator(BaseIndicatorPrimitive):
    def calculate(self, data):
        return data['Close']

class TestSignal(BaseSignalPrimitive):
    def evaluate(self, *inputs, date=None):
        if not inputs:
            return False if date else pd.Series([], dtype=bool)
        series = inputs[0] > 0
        if date is not None:
            return bool(series.loc[date]) if date in series.index else False
        return series

# Test cases
def test_base_component_init():
    """Test BaseComponentPrimitive initialization and parameter handling."""
    component = TestComponent()
    assert component.params['param1'] == 'default'
    assert component.params['param2'] == 42
    assert component.name == 'TestComponent'

def test_base_component_param_override():
    """Test parameter override functionality."""
    component = TestComponent(params={'param1': 'custom', 'param2': 100})
    assert component.params['param1'] == 'custom'
    assert component.params['param2'] == 100

def test_base_component_validation():
    """Test parameter validation."""
    with pytest.raises(ValueError, match="param2 must be an integer"):
        TestComponent(params={'param1': 'test', 'param2': 'invalid'})

def test_base_indicator():
    """Test BaseIndicatorPrimitive functionality."""
    indicator = TestIndicator()
    data = pd.DataFrame({
        'Close': [1, 2, 3],
        'Open': [1.1, 2.1, 3.1]
    })
    result = indicator.calculate(data)
    assert isinstance(result, pd.Series)
    assert all(result == data['Close'])

def test_base_signal():
    """Test BaseSignalPrimitive functionality."""
    signal = TestSignal()
    series = pd.Series([1, -1, 2], index=pd.date_range('2020-01-01', periods=3))
    
    # Test full series evaluation
    result = signal.evaluate(series)
    assert isinstance(result, pd.Series)
    assert all(result == [True, False, True])
    
    # Test single date evaluation
    date = pd.Timestamp('2020-01-02')
    result = signal.evaluate(series, date=date)
    assert isinstance(result, bool)
    assert result == False

def test_abstract_methods():
    """Test that abstract methods cannot be instantiated without implementation."""
    class EmptyIndicator(BaseIndicatorPrimitive):
        pass
    
    class EmptySignal(BaseSignalPrimitive):
        pass
    
    with pytest.raises(TypeError):
        EmptyIndicator()
    
    with pytest.raises(TypeError):
        EmptySignal()
