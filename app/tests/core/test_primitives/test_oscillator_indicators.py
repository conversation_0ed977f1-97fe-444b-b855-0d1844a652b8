"""
Tests for oscillator indicator primitives.

This module tests the functionality of oscillator-based indicator primitives,
including Stochastic Oscillator.
"""

import pytest
import pandas as pd
import numpy as np
from components.indicators.oscillators import Stochastic


def test_stochastic_defaults():
    """Test Stochastic Oscillator with default parameters."""
    stoch = Stochastic()
    assert stoch.params['k_period'] == 14
    assert stoch.params['d_period'] == 3
    assert stoch.params['d_method'] == 'simple'
    assert stoch.params['output_format'] == 'dict'


def test_stochastic_custom_params():
    """Test Stochastic Oscillator with custom parameters."""
    stoch = Stochastic(params={
        'k_period': 5,
        'd_period': 2,
        'd_method': 'exponential',
        'output_format': 'dataframe'
    })
    assert stoch.params['k_period'] == 5
    assert stoch.params['d_period'] == 2
    assert stoch.params['d_method'] == 'exponential'
    assert stoch.params['output_format'] == 'dataframe'


def test_stochastic_validation():
    """Test Stochastic Oscillator parameter validation."""
    # Test invalid periods
    for param in ['k_period', 'd_period']:
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            Stochastic(params={param: 0})
        
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            Stochastic(params={param: -1})
        
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            Stochastic(params={param: 'invalid'})
    
    # Test invalid d_method
    with pytest.raises(ValueError, match="d_method must be either 'simple' or 'exponential'"):
        Stochastic(params={'d_method': 'invalid'})
    
    # Test invalid output_format
    with pytest.raises(ValueError, match="output_format must be either 'dict' or 'dataframe'"):
        Stochastic(params={'output_format': 'invalid'})


def create_test_data():
    """Create test data for stochastic oscillator tests."""
    # Create sample data with varying high, low, close values
    dates = pd.date_range('2020-01-01', periods=30)
    
    # Generate values with a pattern that will produce meaningful stochastic values
    highs = [10, 12, 15, 14, 16, 15, 14, 13, 12, 11, 12, 13, 14, 15, 16, 
             15, 14, 13, 12, 11, 10, 9, 10, 11, 12, 13, 14, 15, 16, 17]
    lows = [8, 9, 10, 10, 12, 10, 9, 8, 7, 6, 7, 8, 9, 10, 12, 
            10, 9, 8, 7, 6, 5, 5, 6, 7, 8, 9, 10, 11, 12, 13]
    closes = [9, 10, 11, 12, 14, 13, 11, 10, 9, 8, 10, 11, 12, 13, 14, 
              13, 11, 10, 9, 8, 7, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    
    data = pd.DataFrame({
        'High': highs,
        'Low': lows,
        'Close': closes
    }, index=dates)
    
    return data


def test_stochastic_calculation():
    """Test Stochastic Oscillator calculation against known behavior."""
    data = create_test_data()
    
    # Create Stochastic with default parameters
    stoch = Stochastic()
    result = stoch.calculate(data)
    
    # Test output structure
    assert isinstance(result, dict)
    assert 'k' in result
    assert 'd' in result
    
    # Test each component
    for component in ['k', 'd']:
        assert isinstance(result[component], pd.Series)
        assert len(result[component]) == len(data)
    
    # Check ranges - Stochastic always between 0 and 100
    assert np.all(result['k'] >= 0)
    assert np.all(result['k'] <= 100)
    assert np.all(result['d'] >= 0)
    assert np.all(result['d'] <= 100)
    
    # In our test data:
    # First the price goes up then down then up again
    # Let's verify some key properties of the stochastic oscillator
    
    # Check that k values are within the correct range [0, 100]
    assert np.all(result['k'] >= 0) and np.all(result['k'] <= 100)
    
    # Based on our data pattern, we should see some variation in the oscillator
    # Check for variance in the values rather than specific ranges
    assert result['k'].std() > 0
    
    # Check that early k values are different from later k values
    # (capturing the changing trend pattern without hardcoding specific values)
    assert not np.isclose(result['k'][5:10].mean(), result['k'][15:20].mean())
    
    # Check that %D (signal line) is derived from %K
    # We know that D is calculated based on K, so they should be correlated
    # but not identical (the smoothing creates a difference)
    correlation = np.corrcoef(result['k'].dropna(), result['d'].dropna())[0, 1]
    assert correlation > 0.5  # Strong positive correlation expected
    assert not np.array_equal(result['k'], result['d'])  # Not identical


def test_stochastic_dataframe_output():
    """Test Stochastic Oscillator with DataFrame output format."""
    data = create_test_data()
    
    stoch = Stochastic(params={'output_format': 'dataframe'})
    result = stoch.calculate(data)
    
    # Verify result is DataFrame
    assert isinstance(result, pd.DataFrame)
    
    # Check columns
    assert all(col in result.columns for col in ['k', 'd'])
    
    # Check shape
    assert result.shape == (len(data), 2)


def test_stochastic_missing_column():
    """Test Stochastic Oscillator behavior with missing column."""
    stoch = Stochastic()
    
    # Test with missing High column
    data = pd.DataFrame({
        'Low': [1, 2, 3],
        'Close': [2, 3, 4]
    })
    with pytest.raises(KeyError, match="Column High not found in data"):
        stoch.calculate(data)
    
    # Test with missing Low column
    data = pd.DataFrame({
        'High': [3, 4, 5],
        'Close': [2, 3, 4]
    })
    with pytest.raises(KeyError, match="Column Low not found in data"):
        stoch.calculate(data)
    
    # Test with missing Close column
    data = pd.DataFrame({
        'High': [3, 4, 5],
        'Low': [1, 2, 3]
    })
    with pytest.raises(KeyError, match="Column Close not found in data"):
        stoch.calculate(data)


def test_stochastic_extreme_values():
    """Test Stochastic Oscillator behavior with extreme price patterns."""
    # Create data with same high and low (should result in stochastic of 100 or 0)
    dates = pd.date_range('2020-01-01', periods=20)
    
    # Case 1: Price always at high - should result in stochastic of 100
    high_data = pd.DataFrame({
        'High': [10] * 20,
        'Low': [5] * 20,
        'Close': [10] * 20  # Close always equals high
    }, index=dates)
    
    stoch = Stochastic()
    high_result = stoch.calculate(high_data)
    
    # With close always at high, %K should be 100 after initial lookback period
    assert np.isclose(high_result['k'].iloc[-1], 100.0)
    
    # Case 2: Price always at low - should result in stochastic of 0
    low_data = pd.DataFrame({
        'High': [10] * 20,
        'Low': [5] * 20,
        'Close': [5] * 20  # Close always equals low
    }, index=dates)
    
    low_result = stoch.calculate(low_data)
    
    # With close always at low, %K should be 0 after initial lookback period
    assert np.isclose(low_result['k'].iloc[-1], 0.0)


def test_stochastic_zero_range():
    """Test Stochastic Oscillator when price range is zero."""
    # Create data with identical high, low, close (no range)
    dates = pd.date_range('2020-01-01', periods=20)
    flat_data = pd.DataFrame({
        'High': [10] * 20,
        'Low': [10] * 20,
        'Close': [10] * 20
    }, index=dates)
    
    stoch = Stochastic()
    result = stoch.calculate(flat_data)
    
    # When high = low, stochastic is traditionally defined as 50 (neutral)
    # Check if implementation handles this edge case
    assert np.all(np.isclose(result['k'][stoch.params['k_period']:], 50.0))
    assert np.all(np.isclose(result['d'][stoch.params['k_period']+stoch.params['d_period']-1:], 50.0))


def test_stochastic_different_methods():
    """Test Stochastic Oscillator with different %D calculation methods."""
    data = create_test_data()
    
    # Test with simple MA (default)
    stoch_simple = Stochastic(params={'d_method': 'simple'})
    result_simple = stoch_simple.calculate(data)
    
    # Test with exponential MA
    stoch_exp = Stochastic(params={'d_method': 'exponential'})
    result_exp = stoch_exp.calculate(data)
    
    # Both should be valid
    assert len(result_simple['d']) == len(data)
    assert len(result_exp['d']) == len(data)
    
    # They should be different after sufficient data points
    # (EMA reacts faster to recent data than SMA)
    assert not np.isclose(result_simple['d'].iloc[-1], result_exp['d'].iloc[-1])


def test_stochastic_nan_handling():
    """Test Stochastic Oscillator handling of NaN values."""
    # Add NaN values in different columns
    data = create_test_data().copy()
    data.loc[data.index[5], 'High'] = np.nan
    data.loc[data.index[10], 'Low'] = np.nan
    data.loc[data.index[15], 'Close'] = np.nan
    
    stoch = Stochastic()
    result = stoch.calculate(data)
    
    # Verify the result contains both components
    assert 'k' in result
    assert 'd' in result
    
    # Verify each component has the same length as input
    assert len(result['k']) == len(data)
    assert len(result['d']) == len(data)
    
    # Check that calculation doesn't fail entirely due to NaN values
    # Some NaN values might propagate, but not all results should be NaN
    assert not result['k'].isna().all()
    assert not result['d'].isna().all()
