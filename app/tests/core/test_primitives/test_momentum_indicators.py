"""
Tests for momentum indicator primitives.

This module tests the functionality of momentum-based indicator primitives,
including Relative Strength Index (RSI).
"""

import pytest
import pandas as pd
import numpy as np
from components.indicators.momentum import RSI, MACD


def test_rsi_defaults():
    """Test RSI indicator with default parameters."""
    rsi = RSI()
    assert rsi.params['period'] == 14
    assert rsi.params['column'] == 'Close'


def test_rsi_custom_params():
    """Test RSI indicator with custom parameters."""
    rsi = RSI(params={'period': 10, 'column': 'Open'})
    assert rsi.params['period'] == 10
    assert rsi.params['column'] == 'Open'


def test_rsi_validation():
    """Test RSI parameter validation."""
    # Test invalid period
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        RSI(params={'period': 0})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        RSI(params={'period': -1})
    
    with pytest.raises(ValueError, match="Period must be a positive integer"):
        RSI(params={'period': 'invalid'})
    
    # Test invalid column
    with pytest.raises(ValueError, match="Column must be a string"):
        RSI(params={'column': 123})


def test_rsi_calculation():
    """Test RSI calculation against known values."""
    # Create sample data with a consistent up-trend for predictable RSI values
    close_prices = [10, 11, 12, 11, 12, 13, 14, 13, 14, 15, 16, 15, 16, 17, 18, 19, 20]
    data = pd.DataFrame({
        'Close': close_prices
    })
    
    # Use RSI with period=14 (default)
    rsi = RSI()
    result = rsi.calculate(data)
    
    # Verify the result is a valid Series
    assert isinstance(result, pd.Series)
    # Verify it has the same length as input
    assert len(result) == len(data)
    
    # Check range - RSI should always be between 0 and 100
    assert np.all(result >= 0)
    assert np.all(result <= 100)
    
    # Check values 
    # Since we have a mostly upward trend, RSI should be high (> 50) for later values
    # Early values might vary based on initialization, but later ones should stabilize
    assert result.iloc[-1] > 50  # Last value should be above 50 (bullish)
    
    # Verify known value from a standard calculation
    # With an uptrend, the final values should be above 70 (indicating overbought)
    assert result.iloc[-1] > 70


def test_rsi_all_same_price():
    """Test RSI calculation when all prices are the same."""
    # This should result in a RSI of 50 (neutral)
    data = pd.DataFrame({
        'Close': [10, 10, 10, 10, 10, 10, 10]
    })
    
    rsi = RSI()
    result = rsi.calculate(data)
    
    # With no price changes, RSI should be 50 (neutral)
    # Check all values are close to 50
    assert np.all(np.isclose(result, 50.0))


def test_rsi_all_increases():
    """Test RSI calculation when all prices increase."""
    # This should result in a RSI approaching 100 (strong overbought)
    data = pd.DataFrame({
        'Close': [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
    })
    
    rsi = RSI()
    result = rsi.calculate(data)
    
    # With all price increases, RSI should approach 100 (completely overbought)
    # First value is undefined, but last value should be high
    assert result.iloc[-1] > 90


def test_rsi_all_decreases():
    """Test RSI calculation when all prices decrease."""
    # This should result in a RSI approaching 0 (strong oversold)
    data = pd.DataFrame({
        'Close': [25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10]
    })
    
    rsi = RSI()
    result = rsi.calculate(data)
    
    # With all price decreases, RSI should approach 0 (completely oversold)
    # First value is undefined, but last value should be low
    assert result.iloc[-1] < 10


def test_rsi_missing_column():
    """Test RSI behavior with missing column."""
    rsi = RSI(params={'column': 'NonExistent'})
    data = pd.DataFrame({'Close': [1, 2, 3]})
    
    with pytest.raises(KeyError, match="Column NonExistent not found in data"):
        rsi.calculate(data)


def test_rsi_nan_handling():
    """Test RSI handling of NaN values."""
    data = pd.DataFrame({
        'Close': [10, np.nan, 12, 13, 14, 15]
    })
    
    rsi = RSI()
    result = rsi.calculate(data)
    
    # Verify the result is a valid Series
    assert isinstance(result, pd.Series)
    # Verify it has the same length as input
    assert len(result) == len(data)
    # RSI should handle NaN values and not propagate them
    assert not result.isna().any()


# MACD Tests
def test_macd_defaults():
    """Test MACD indicator with default parameters."""
    macd = MACD()
    assert macd.params['fast_period'] == 12
    assert macd.params['slow_period'] == 26
    assert macd.params['signal_period'] == 9
    assert macd.params['column'] == 'Close'
    assert macd.params['output_format'] == 'dict'


def test_macd_custom_params():
    """Test MACD indicator with custom parameters."""
    macd = MACD(params={
        'fast_period': 8,
        'slow_period': 21,
        'signal_period': 5,
        'column': 'Open',
        'output_format': 'dataframe'
    })
    assert macd.params['fast_period'] == 8
    assert macd.params['slow_period'] == 21
    assert macd.params['signal_period'] == 5
    assert macd.params['column'] == 'Open'
    assert macd.params['output_format'] == 'dataframe'


def test_macd_validation():
    """Test MACD parameter validation."""
    # Test invalid periods
    for param in ['fast_period', 'slow_period', 'signal_period']:
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            MACD(params={param: 0})
        
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            MACD(params={param: -1})
        
        with pytest.raises(ValueError, match=f"{param} must be a positive integer"):
            MACD(params={param: 'invalid'})
    
    # Test fast_period >= slow_period (invalid)
    with pytest.raises(ValueError, match="fast_period must be less than slow_period"):
        MACD(params={'fast_period': 26, 'slow_period': 12})
    
    with pytest.raises(ValueError, match="fast_period must be less than slow_period"):
        MACD(params={'fast_period': 26, 'slow_period': 26})
    
    # Test invalid column
    with pytest.raises(ValueError, match="column must be a string"):
        MACD(params={'column': 123})
    
    # Test invalid output_format
    with pytest.raises(ValueError, match="output_format must be either 'dict' or 'dataframe'"):
        MACD(params={'output_format': 'invalid'})


def test_macd_calculation():
    """Test MACD calculation against known values."""
    # Create sample data with a consistent up-trend for predictable MACD values
    dates = pd.date_range('2020-01-01', periods=50)
    close_prices = [10 + i for i in range(50)]  # Linear uptrend
    data = pd.DataFrame({
        'Close': close_prices
    }, index=dates)
    
    # Use MACD with default parameters
    macd = MACD()
    result = macd.calculate(data)
    
    # Test output structure
    assert isinstance(result, dict)
    assert 'macd' in result
    assert 'signal' in result
    assert 'histogram' in result
    
    # Test each component
    for component in ['macd', 'signal', 'histogram']:
        assert isinstance(result[component], pd.Series)
        assert len(result[component]) == len(data)
    
    # Check values with uptrend
    # In an uptrend, MACD line should eventually be positive
    assert result['macd'].iloc[-1] > 0
    
    # MACD line should be above signal line in a strong uptrend
    assert result['macd'].iloc[-1] > result['signal'].iloc[-1]
    
    # Histogram should be positive when MACD is above signal
    assert result['histogram'].iloc[-1] > 0
    
    # Test consistency: histogram = macd - signal
    pd.testing.assert_series_equal(
        result['histogram'],
        result['macd'] - result['signal']
    )


def test_macd_dataframe_output():
    """Test MACD with DataFrame output format."""
    data = pd.DataFrame({
        'Close': [10 + i for i in range(30)]
    })
    
    macd = MACD(params={'output_format': 'dataframe'})
    result = macd.calculate(data)
    
    # Verify result is DataFrame
    assert isinstance(result, pd.DataFrame)
    
    # Check columns
    assert all(col in result.columns for col in ['macd', 'signal', 'histogram'])
    
    # Check shape
    assert result.shape == (len(data), 3)


def test_macd_missing_column():
    """Test MACD behavior with missing column."""
    macd = MACD(params={'column': 'NonExistent'})
    data = pd.DataFrame({'Close': [1, 2, 3]})
    
    with pytest.raises(KeyError, match="Column NonExistent not found in data"):
        macd.calculate(data)


def test_macd_downtrend():
    """Test MACD behavior in a downtrend."""
    # Create sample data with a consistent down-trend
    dates = pd.date_range('2020-01-01', periods=50)
    close_prices = [100 - i for i in range(50)]  # Linear downtrend
    data = pd.DataFrame({
        'Close': close_prices
    }, index=dates)
    
    macd = MACD()
    result = macd.calculate(data)
    
    # In a downtrend, MACD line should eventually be negative
    assert result['macd'].iloc[-1] < 0
    
    # MACD line should be below signal line in a strong downtrend
    assert result['macd'].iloc[-1] < result['signal'].iloc[-1]
    
    # Histogram should be negative when MACD is below signal
    assert result['histogram'].iloc[-1] < 0


def test_macd_nan_handling():
    """Test MACD handling of NaN values."""
    data = pd.DataFrame({
        'Close': [10, np.nan, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
    })
    
    macd = MACD()
    result = macd.calculate(data)
    
    # Verify the result contains all components
    assert all(component in result for component in ['macd', 'signal', 'histogram'])
    
    # Verify each component has the same length as input
    for component in ['macd', 'signal', 'histogram']:
        assert len(result[component]) == len(data)
    
    # Check that NaN doesn't cause calculation to fail
    assert not result['macd'].isna().all()
    assert not result['signal'].isna().all()
    assert not result['histogram'].isna().all()
