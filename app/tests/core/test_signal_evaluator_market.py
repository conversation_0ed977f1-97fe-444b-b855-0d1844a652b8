"""
Unit tests for the Signal Evaluator with market indicator support.

Tests the SignalEvaluator's ability to handle market indicator references
and properly integrate market data into signal evaluation.
"""

import unittest
import pandas as pd
import numpy as np
from unittest.mock import MagicMock

from components.market_indicators import MarketIndicatorManager
from components.signal_evaluator import SignalEvaluator

class MockPortfolioDataLoader:
    """Mock data loader for testing."""
    
    def __init__(self):
        """Initialize the mock loader."""
        pass
        
    def get_market_index(self, code, start_date=None, end_date=None):
        """Mock method to get market index data."""
        dates = pd.date_range(start='2023-01-01', end='2023-01-10')
        data = pd.DataFrame({
            'open': np.random.uniform(15, 20, size=len(dates)),
            'high': np.random.uniform(20, 25, size=len(dates)),
            'low': np.random.uniform(10, 15, size=len(dates)),
            'close': np.linspace(20, 10, len(dates)),
            'volume': np.random.uniform(1000, 2000, size=len(dates))
        }, index=dates)
        return data

class TestSignalEvaluatorWithMarketIndicators(unittest.TestCase):
    """Test cases for SignalEvaluator with market indicator support."""
    
    def setUp(self):
        """Set up test fixtures."""
        # 创建模拟注册表
        self.mock_registry = MagicMock()
        
        # 创建市场指标管理器
        self.market_indicator_manager = MarketIndicatorManager(MockPortfolioDataLoader())
        
        # 准备测试数据
        dates = pd.date_range(start='2023-01-01', end='2023-01-10')
        # 创建价格数据
        self.price_data = pd.DataFrame({
            'Open': np.random.uniform(100, 110, size=len(dates)),
            'High': np.random.uniform(110, 120, size=len(dates)),
            'Low': np.random.uniform(90, 100, size=len(dates)),
            'Close': np.random.uniform(100, 110, size=len(dates)),
            'Volume': np.random.uniform(10000, 20000, size=len(dates)),
            'Adj Close': np.random.uniform(100, 110, size=len(dates))
        }, index=dates)
        
        # 创建市场指标数据
        vix_data = pd.DataFrame({
            'open': np.random.uniform(15, 20, size=len(dates)),
            'high': np.random.uniform(20, 25, size=len(dates)),
            'low': np.random.uniform(10, 15, size=len(dates)),
            'close': np.linspace(20, 10, len(dates)),  # 线性下降，便于测试
            'volume': np.random.uniform(1000, 2000, size=len(dates))
        }, index=dates)
        
        # 模拟加载市场指标
        self.market_indicator_manager.indicators = {'vix': vix_data}
        
        # 创建 SignalEvaluator 实例
        self.evaluator = SignalEvaluator(self.mock_registry, self.market_indicator_manager)
        
        # 模拟 ComponentRegistry 和信号计算
        # 为了简化测试，我们将直接模拟 _evaluate_node 的结果，而不是完整实现组件创建和评估
        self.evaluator._create_component = MagicMock()
        self.evaluator._evaluated_components = {}
    
    def test_evaluate_market_indicator_reference(self):
        """Test evaluation of a market indicator reference."""
        # 初始化 _evaluated_components 缓存
        self.evaluator._evaluated_components = {}
        
        # 创建市场指标引用节点
        node = {'market': 'vix', 'field': 'close'}
        
        # 调用 _evaluate_node 方法
        result = self.evaluator._evaluate_node(node, self.price_data)
        
        # 验证结果
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.price_data))
        # 确保数据被正确对齐
        self.assertTrue(all(result.index == self.price_data.index))
        
    def test_evaluate_market_transformer_reference(self):
        """Test evaluation of a market transformer reference."""
        # 注册一个转换器
        self.market_indicator_manager.register_transformer = MagicMock()
        
        # 模拟注册转换器
        mock_transformer = MagicMock()
        mock_transformer.transform.return_value = pd.Series(
            np.linspace(0, 1, len(self.price_data)), 
            index=self.price_data.index
        )
        self.market_indicator_manager.transformers = {
            'vix_ma': ('vix', {'window': 5}, mock_transformer)
        }
        
        # 模拟获取转换后的指标
        self.market_indicator_manager.get_transformed_indicator = MagicMock(
            return_value=pd.Series(
                np.linspace(0, 1, len(self.price_data)),
                index=self.price_data.index
            )
        )
        
        # 初始化 _evaluated_components 缓存
        self.evaluator._evaluated_components = {}
        
        # 创建市场指标转换器引用节点
        node = {'market': 'vix', 'transformer': 'vix_ma'}
        
        # 调用 _evaluate_node 方法
        result = self.evaluator._evaluate_node(node, self.price_data)
        
        # 验证结果
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.price_data))
        # 确保数据被正确对齐
        self.assertTrue(all(result.index == self.price_data.index))
        # 验证转换器被调用
        self.market_indicator_manager.get_transformed_indicator.assert_called_once_with('vix_ma')
        
    def test_evaluate_market_indicator_missing(self):
        """Test evaluation with a missing market indicator."""
        # 初始化 _evaluated_components 缓存
        self.evaluator._evaluated_components = {}
        
        # 创建不存在的市场指标引用节点
        node = {'market': 'nonexistent', 'field': 'close'}
        
        # 测试异常是否被正确抛出
        with self.assertRaises(ValueError) as context:
            self.evaluator._evaluate_node(node, self.price_data)
            
        self.assertIn("Market indicator 'nonexistent' not found", str(context.exception))
        
    def test_evaluate_signals_with_market_conditions(self):
        """Test evaluation of signals that reference market indicators."""
        # 为了这个测试，我们需要模拟一个更复杂的场景
        # 我们将创建一个简单的信号树，使用市场指标作为条件
        
        # 假设我们有一个 GreaterThan 信号，比较价格与 VIX
        price_node = {'column': 'Close'}
        vix_node = {'market': 'vix', 'field': 'close'}
        
        # 模拟信号实例
        mock_signal = MagicMock()
        mock_signal.evaluate.return_value = pd.Series(
            [False, False, True, True, True, True, True, True, True, True], 
            index=self.price_data.index
        )
        
        # 模拟创建组件返回我们的模拟信号
        self.evaluator._create_component.return_value = mock_signal
        
        # 配置信号节点
        signal_node = {
            'type': 'GreaterThan',
            'inputs': [price_node, vix_node]
        }
        
        # 调用 _evaluate_node 方法
        result = self.evaluator._evaluate_node(signal_node, self.price_data, 'signals')
        
        # 验证结果
        self.assertIsInstance(result, pd.Series)
        self.assertEqual(len(result), len(self.price_data))
        # 验证信号评估被调用
        mock_signal.evaluate.assert_called_once()
        # 验证输入参数数量 (price 和 vix)
        self.assertEqual(len(mock_signal.evaluate.call_args[0]), 2)


if __name__ == '__main__':
    unittest.main()
