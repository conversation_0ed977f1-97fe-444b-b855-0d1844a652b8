import pytest
from portfolio_factory_v2 import PortfolioFactory
from tests.utils.test_helpers import MockMinimalDataLoader

def test_parameters_passing():
    """测试参数能否正确传递给投资组合"""
    factory = PortfolioFactory()
    factory.load_portfolio_config_from_file("portfolio_config.json")

    # 创建自定义参数
    custom_params = {
        "start_date": "2022-01-15",
        "end_date": "2022-02-15",
        "test_param": "test_value"
    }

    # 使用自定义参数创建投资组合
    portfolio = factory.get_portfolio(
        "myinvestpilot_us_1",
        MockMinimalDataLoader(),
        params=custom_params  # Pass params here
    )

    # 验证参数是否被正确传递
    # Access parameters through the portfolio's internal config or attributes
    # Note: The original example accessed portfolio.config, but PortfolioManager stores config differently.
    # We'll check the attributes set during initialization.
    assert portfolio.start_date == "2022-01-15", "开始日期参数未正确传递"
    assert portfolio.end_date == "2022-02-15", "结束日期参数未正确传递"
    
    # Note: Verifying arbitrary custom parameters like 'test_param' requires 
    # specific knowledge of how they are used within the strategy or manager.
    # Since 'test_param' isn't standard, we cannot reliably check its effect here.
    # The test confirms standard parameters (start/end date) are passed correctly.
