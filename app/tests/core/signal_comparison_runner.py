#!/usr/bin/env python
"""
信号比较工具运行器

该脚本提供了命令行界面，用于比较原始策略与原语化策略的信号生成。
可用于验证原语配置是否正确复制了原始策略的行为。

用法:
    python -m tests.core.signal_comparison_runner --help
    python -m tests.core.signal_comparison_runner --data tests/data/SPY.csv --symbol SPY --strategy ChandelierExitMAStrategy --config config/portfolio_config_complex_primitive.json
"""

import os
import sys
import json
import argparse
import importlib
import logging
import pandas as pd
from typing import Dict, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录路径
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
# 确保项目根目录在Python路径中
sys.path.insert(0, ROOT_DIR)
# 确保app目录在Python路径中
sys.path.insert(0, os.path.join(ROOT_DIR, "app"))

from tests.core.signal_comparison_framework import run_signal_test


def parse_strategy_params(params_str: str) -> Dict[str, Any]:
    """
    解析策略参数字符串，格式为key1=value1,key2=value2
    
    Args:
        params_str: 参数字符串，如 'n_atr=60,atr_multiplier=4.0,n_ma=250'
        
    Returns:
        Dict: 参数字典
    """
    if not params_str:
        return {}
        
    params = {}
    for item in params_str.split(','):
        if '=' not in item:
            logger.warning(f"忽略无效参数: {item}")
            continue
            
        key, value = item.split('=', 1)
        key = key.strip()
        value = value.strip()
        
        # 尝试转换值为适当的类型
        try:
            # 尝试转换为int
            params[key] = int(value)
        except ValueError:
            try:
                # 尝试转换为float
                params[key] = float(value)
            except ValueError:
                # 否则保持为字符串
                if value.lower() == 'true':
                    params[key] = True
                elif value.lower() == 'false':
                    params[key] = False
                else:
                    params[key] = value
    
    return params


def load_strategy(strategy_name: str, params: Dict[str, Any]) -> Any:
    """
    动态加载策略类并实例化
    
    Args:
        strategy_name: 策略类名
        params: 策略参数
        
    Returns:
        Any: 策略实例
    """
    # 先尝试显式导入常见的策略类
    if strategy_name == "ChandelierExitMAStrategy":
        from trade_strategies.chandelier_exit_ma_strategy import ChandelierExitMAStrategy
        return ChandelierExitMAStrategy(params)
    elif strategy_name == "DualMovingAverageStrategy":
        from trade_strategies.dual_moving_average_strategy import DualMovingAverageStrategy
        return DualMovingAverageStrategy(params)
    elif strategy_name == "RSIStrategy":
        from trade_strategies.rsi_strategy import RSIStrategy
        return RSIStrategy(params)
    
    # 尝试从trade_strategies模块加载策略
    module_name = f"trade_strategies.{strategy_name.lower()}_strategy"
    try:
        module = importlib.import_module(module_name)
        strategy_class = getattr(module, strategy_name)
    except (ImportError, AttributeError):
        # 如果找不到，尝试直接从trade_strategies模块获取
        try:
            module = importlib.import_module("trade_strategies")
            strategy_class = getattr(module, strategy_name)
        except (ImportError, AttributeError) as e:
            raise ImportError(f"无法找到策略类: {strategy_name}. 错误: {e}")
    
    # 实例化策略
    return strategy_class(params)


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载原语配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict: 原语配置字典
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 如果配置是完整的portfolio_config格式，提取strategy_definition部分
    if 'portfolios' in config:
        for portfolio in config['portfolios']:
            if 'strategy_definition' in portfolio and 'trade_strategy' in portfolio['strategy_definition']:
                return portfolio['strategy_definition']['trade_strategy']
    
    # 如果配置是直接的trade_strategy格式
    if 'indicators' in config and 'signals' in config:
        return config
    
    # 如果配置是strategy_definition格式
    if 'trade_strategy' in config:
        return config['trade_strategy']
    
    raise ValueError(f"无法识别的配置格式: {config_path}")


def load_data(data_path: str) -> pd.DataFrame:
    """
    加载OHLCV数据
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        pd.DataFrame: 数据框
    """
    # 检查文件扩展名
    if data_path.endswith('.csv'):
        df = pd.read_csv(data_path)
    elif data_path.endswith('.parquet'):
        df = pd.read_parquet(data_path)
    elif data_path.endswith('.h5') or data_path.endswith('.hdf5'):
        df = pd.read_hdf(data_path)
    else:
        raise ValueError(f"不支持的数据文件格式: {data_path}")
    
    # 确保日期列为datetime类型
    if 'Date' in df.columns:
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)
    
    # 确保关键列存在
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing = [col for col in required_columns if col not in df.columns]
    if missing:
        raise ValueError(f"数据文件中缺少所需列: {missing}")
    
    return df


def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='信号比较工具 - 比较原始策略与原语策略的信号生成')
    
    # 必选参数
    parser.add_argument('--data', required=True, help='OHLCV数据文件路径')
    parser.add_argument('--symbol', required=True, help='测试标的代码')
    parser.add_argument('--strategy', required=True, help='原始策略类名')
    parser.add_argument('--config', required=True, help='原语策略配置文件路径')
    
    # 可选参数
    parser.add_argument('--strategy-params', default='', help='策略参数，格式为key1=value1,key2=value2')
    parser.add_argument('--output-dir', default='', help='输出目录')
    parser.add_argument('--ignore-first-n', type=int, default=0, help='忽略前n个数据点')
    parser.add_argument('--show-plot', action='store_true', help='显示图表')
    parser.add_argument('--verbose', action='store_true', help='启用详细日志')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 加载数据
        logger.info(f"加载数据: {args.data}")
        
        # 解析策略参数
        strategy_params = parse_strategy_params(args.strategy_params)
        logger.info(f"策略参数: {strategy_params}")
        
        # 加载策略
        logger.info(f"加载策略: {args.strategy}")
        original_strategy = load_strategy(args.strategy, strategy_params)
        
        # 加载原语配置
        logger.info(f"加载原语配置: {args.config}")
        primitive_config = load_config(args.config)
        
        # 设置输出目录
        output_dir = args.output_dir or os.path.join(os.getcwd(), "signal_comparison_output")
        os.makedirs(output_dir, exist_ok=True)
        
        # 运行比较
        logger.info("开始比较信号...")
        results = run_signal_test(
            data_path=args.data,
            symbol=args.symbol,
            original_strategy=original_strategy,
            primitive_config=primitive_config,
            output_dir=output_dir,
            ignore_first_n=args.ignore_first_n,
            show_plot=args.show_plot
        )
        
        # 显示结果
        print("\n=== 信号比较结果 ===")
        print(f"总信号数: {results['total_signals']}")
        print(f"不同信号数: {results['different_signals']}")
        print(f"差异百分比: {results['difference_percent']:.2f}%")
        print(f"一致性: {'✅ 通过' if results['is_consistent'] else '❌ 失败'}")
        
        # 如果有差异，显示更多信息
        if not results['is_consistent']:
            print("\n⚠️ 检测到信号差异")
            print("请查看生成的报告和图表以获取详细信息")
        
        print("\n结果已保存至:", output_dir)
        print(f"- CSV: comparison_results_{args.symbol}.csv")
        print(f"- HTML报告: comparison_report_{args.symbol}.html")
        print(f"- 图表: signal_comparison_{args.symbol}.png")
        
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
