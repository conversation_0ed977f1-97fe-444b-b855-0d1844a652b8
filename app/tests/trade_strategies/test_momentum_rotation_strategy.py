import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from trade_strategies import TradeSignalState
from trade_strategies.momentum_rotation_strategy import MomentumRotationStrategy

class TestMomentumRotationStrategy(unittest.TestCase):
    def setUp(self):
        """Set up test data and strategy instance"""
        # Test ETF symbols
        self.etf_symbols = ['SPY', 'QQQ', 'IWM']
        
        # Create strategy instance with just the momentum period parameter
        self.strategy = MomentumRotationStrategy({
            'momentum_period': 10  # Shorter period for testing
        })
        
        # Generate test data
        self.create_test_data()
        
    def create_test_data(self):
        """Create synthetic price data for testing"""
        # Create date range for 50 days
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=60)
        dates = pd.date_range(start=start_date, end=end_date, freq='B')  # Business days
        
        # Create test data for each ETF with different momentum patterns
        test_data = {}
        
        # SPY: Steady uptrend
        spy_data = pd.DataFrame(index=dates)
        spy_data['Open'] = np.linspace(100, 120, len(dates))
        spy_data['High'] = spy_data['Open'] * 1.01
        spy_data['Low'] = spy_data['Open'] * 0.99
        spy_data['Close'] = spy_data['Open'] * 1.005
        spy_data['Volume'] = 1000000
        spy_data['Adj Close'] = spy_data['Close']
        spy_data['Adj Factor'] = 1.0
        spy_data['symbol'] = 'SPY'
        
        # QQQ: Initial uptrend then strong momentum
        qqq_data = pd.DataFrame(index=dates)
        base_prices = np.linspace(200, 205, len(dates) // 2)
        accel_prices = np.linspace(205, 230, len(dates) - len(dates) // 2)
        qqq_data['Open'] = np.concatenate([base_prices, accel_prices])
        qqq_data['High'] = qqq_data['Open'] * 1.01
        qqq_data['Low'] = qqq_data['Open'] * 0.99
        qqq_data['Close'] = qqq_data['Open'] * 1.003
        qqq_data['Volume'] = 800000
        qqq_data['Adj Close'] = qqq_data['Close']
        qqq_data['Adj Factor'] = 1.0
        qqq_data['symbol'] = 'QQQ'
        
        # IWM: Downtrend
        iwm_data = pd.DataFrame(index=dates)
        iwm_data['Open'] = np.linspace(180, 160, len(dates))
        iwm_data['High'] = iwm_data['Open'] * 1.01
        iwm_data['Low'] = iwm_data['Open'] * 0.99
        iwm_data['Close'] = iwm_data['Open'] * 0.997
        iwm_data['Volume'] = 600000
        iwm_data['Adj Close'] = iwm_data['Close']
        iwm_data['Adj Factor'] = 1.0
        iwm_data['symbol'] = 'IWM'
        
        # Store data in dictionary
        test_data['SPY'] = spy_data
        test_data['QQQ'] = qqq_data
        test_data['IWM'] = iwm_data
        
        # Set global data
        self.strategy.set_global_data(test_data)
        
        # Store the symbols for testing
        self.etf_symbols = list(test_data.keys())
    
    def test_calculate_momentum(self):
        """Test the momentum calculation logic"""
        # Create a simple price series
        prices = pd.Series([10.0, 11.0, 12.0, 13.0, 14.0, 15.0])
        
        # Calculate momentum with period=4
        momentum = self.strategy._calculate_momentum(prices, 4)
        
        # Expected: (15.0 / 11.0 - 1) * 100 = 36.36...%
        expected_momentum = (15.0 / 11.0 - 1) * 100
        self.assertAlmostEqual(momentum, expected_momentum, places=2)
    
    def test_generate_all_signals(self):
        """Test the generation of signals for all symbols"""
        # Generate signals
        all_signals = self.strategy.generate_all_signals()
        
        # Check if signals were generated for all ETFs
        self.assertEqual(len(all_signals), len(self.etf_symbols))
        
        # Verify each ETF has signals
        for symbol in self.etf_symbols:
            self.assertIn(symbol, all_signals)
            self.assertGreater(len(all_signals[symbol]), 0)
            
            # Ensure symbol column exists in each DataFrame
            self.assertIn('symbol', all_signals[symbol].columns, 
                          f"Symbol column missing in signals for {symbol}")
            
            # Verify symbol values are correct
            self.assertTrue((all_signals[symbol]['symbol'] == symbol).all(), 
                            f"Incorrect symbol values in signals for {symbol}")
    
    def test_signal_validity(self):
        """Test that signals follow the rotation strategy rules"""
        # Generate signals
        all_signals = self.strategy.generate_all_signals()
        
        # Check that signals follow the rotation rules
        # 1. At any given date, only one ETF can have a BUY or HOLD signal
        
        # Get all dates from any ETF
        dates = all_signals[self.etf_symbols[0]].index
        
        for date in dates:
            # Count BUY and HOLD signals on this date
            buy_hold_count = 0
            for symbol in self.etf_symbols:
                if date in all_signals[symbol].index:
                    signal = all_signals[symbol].loc[date, 'signal']
                    if signal in [TradeSignalState.BUY.value, TradeSignalState.HOLD.value]:
                        buy_hold_count += 1
            
            # Ensure at most one ETF has BUY or HOLD signal
            self.assertLessEqual(buy_hold_count, 1, f"Multiple BUY/HOLD signals on {date}")
    
    def test_momentum_based_selection(self):
        """Test that the ETF with highest momentum gets selected"""
        # Given our test data, QQQ should have the highest momentum in the later part
        all_signals = self.strategy.generate_all_signals()
        
        # Check signals in the last 10 days (when QQQ has highest momentum)
        last_days = all_signals['QQQ'].index[-10:]
        
        # QQQ should have at least one BUY signal in this period
        qqq_signals = all_signals['QQQ'].loc[last_days, 'signal']
        self.assertTrue(
            (qqq_signals == TradeSignalState.BUY.value).any() or 
            (qqq_signals == TradeSignalState.HOLD.value).any(), 
            "QQQ should have BUY or HOLD signals when it has highest momentum"
        )
        
    def test_symbol_column_preservation(self):
        """专门测试symbol列是否正确保留在信号数据中"""
        # 生成信号
        all_signals = self.strategy.generate_all_signals()
        
        # 合并所有ETF的信号以模拟persist_to_db前的处理
        combined_signals = pd.concat(list(all_signals.values()))
        
        # 验证symbol列存在且不为空
        self.assertIn('symbol', combined_signals.columns)
        self.assertEqual(0, combined_signals['symbol'].isna().sum(), 
                        "Symbol column contains NaN values")
        
        # 验证symbol列只包含预期的ETF符号
        unique_symbols = set(combined_signals['symbol'].unique())
        self.assertEqual(set(self.etf_symbols), unique_symbols)

if __name__ == '__main__':
    unittest.main() 