"""Integration tests for portfolio backtesting."""

import os
import json
import pandas as pd
import pytest
import sqlite3
import numpy as np
from typing import Dict, <PERSON><PERSON>

def load_csv_data(file_path):
    """Load data from a CSV file.

    Args:
        file_path (str): Path to the CSV file

    Returns:
        pd.DataFrame or None: DataFrame containing file data, None if file doesn't exist
    """
    if not os.path.exists(file_path):
        return None
    return pd.read_csv(file_path)

def load_db_table(db_path, table_name):
    """Load a table from SQLite database.

    Args:
        db_path (str): Path to the database file
        table_name (str): Name of the table to load

    Returns:
        pd.DataFrame or None: DataFrame containing table data, None if database/table doesn't exist
    """
    if not os.path.exists(db_path):
        return None

    conn = sqlite3.connect(db_path)
    query = f"SELECT * FROM {table_name}"
    df = pd.read_sql_query(query, conn)
    conn.close()

    return df

def compare_float_dicts(dict1: Dict, dict2: Dict) -> <PERSON>ple[bool, str]:
    """Compare two dictionaries containing float values truncated to 3 decimal places.

    Args:
        dict1: First dictionary
        dict2: Second dictionary

    Returns:
        Tuple of (bool, str) indicating match status and explanation
    """
    if dict1.keys() != dict2.keys():
        return False, f"Keys don't match: {dict1.keys()} vs {dict2.keys()}"

    for key in dict1:
        # Format as strings with exactly 3 decimal places
        val1 = f"{float(dict1[key]):.3f}"
        val2 = f"{float(dict2[key]):.3f}"
        if val1 != val2:
            return False, f"Values don't match at key {key}: {val1} vs {val2}"
    return True, "Dictionaries match to 3 decimal places"

def format_diff_message(baseline_val, current_val, col_name):
    """Format comparison difference message.

    Args:
        baseline_val: Baseline value
        current_val: Current value
        col_name (str): Column name

    Returns:
        str: Formatted message
    """
    if isinstance(baseline_val, (int, float)):
        return (f"Column '{col_name}' mismatch: "
                f"baseline={baseline_val:.6f} vs "
                f"current={current_val:.6f}")
    return f"Column '{col_name}' mismatch: baseline={baseline_val} vs current={current_val}"


def compare_dataframes(baseline_df, current_df, table_name=None, float_tolerance=1e-3):
    """Compare two DataFrames with tolerance for floating point differences.

    Args:
        baseline_df (pd.DataFrame): Baseline data
        current_df (pd.DataFrame): Current data to compare
        table_name (str): Name of the table being compared
        float_tolerance (float): Tolerance for floating point comparisons

    Returns:
        tuple: (bool, str) indicating match status and explanation message
    """
    if baseline_df is None or current_df is None:
        return False, "One or both DataFrames are empty"

    # Check column matching
    if set(baseline_df.columns) != set(current_df.columns):
        missing_in_current = set(baseline_df.columns) - set(current_df.columns)
        missing_in_baseline = set(current_df.columns) - set(baseline_df.columns)

        message = "Columns do not match. "
        if missing_in_current:
            message += f"Missing in current: {missing_in_current}. "
        if missing_in_baseline:
            message += f"Missing in baseline: {missing_in_baseline}."

        return False, message

    # Convert dates to datetime
    if 'date' in baseline_df.columns:
        baseline_df['date'] = pd.to_datetime(baseline_df['date'])
        current_df['date'] = pd.to_datetime(current_df['date'])

    # Ensure column order matches for comparison
    baseline_df = baseline_df.reindex(sorted(baseline_df.columns), axis=1)
    current_df = current_df.reindex(sorted(current_df.columns), axis=1)

    # Process numeric columns with specific rounding rules
    rounding_rules = {
        'net_value': 3,
        'annual_return': 3,
        'close': 3,
        'amount': 3,
        'price': 3,
        'value': 3,
        'available_cash': 3,
        'total_assets': 3,
        'atr': 3,
        'vwr': 3,
        'profit_loss_ratio': 4
    }

    for col in baseline_df.columns:
        if col == 'annual_returns':
            # Pre-process all annual_returns values to 3 decimal places
            for idx in baseline_df.index:
                try:
                    # Parse and normalize baseline
                    baseline_dict = json.loads(baseline_df.loc[idx, col].replace("'", '"'))
                    baseline_dict = {k: f"{float(v):.3f}" for k, v in baseline_dict.items()}
                    baseline_df.loc[idx, col] = json.dumps(baseline_dict)

                    # Parse and normalize current
                    current_dict = json.loads(current_df.loc[idx, col].replace("'", '"'))
                    current_dict = {k: f"{float(v):.3f}" for k, v in current_dict.items()}
                    current_df.loc[idx, col] = json.dumps(current_dict)
                except (json.JSONDecodeError, ValueError) as e:
                    continue  # Keep original values if parsing fails

            # Now we can do direct string comparison since values are normalized
            if not (baseline_df[col] == current_df[col]).all():
                mismatched_idx = (baseline_df[col] != current_df[col])
                first_mismatch = mismatched_idx[mismatched_idx].index[0]
                return False, format_diff_message(baseline_df.loc[first_mismatch, col],
                                               current_df.loc[first_mismatch, col], col)
        elif baseline_df[col].dtype.kind in 'fc':  # float or complex
            decimals = rounding_rules.get(col, 3)
            baseline_df[col] = pd.to_numeric(baseline_df[col], errors='coerce').round(decimals)
            current_df[col] = pd.to_numeric(current_df[col], errors='coerce').round(decimals)

    # Standardize date formats
    if 'date' in baseline_df.columns:
        baseline_df['date'] = baseline_df['date'].dt.strftime('%Y-%m-%d')
        current_df['date'] = current_df['date'].dt.strftime('%Y-%m-%d')

    # Sort by date if available
    if 'date' in baseline_df.columns:
        baseline_df = baseline_df.sort_values('date').reset_index(drop=True)
        current_df = current_df.sort_values('date').reset_index(drop=True)

    # Compare row counts
    if len(baseline_df) != len(current_df):
        sample_diff = ""
        if len(baseline_df) > 0 and len(current_df) > 0:
            sample_diff = "\nFirst few rows comparison:\nBaseline:\n{}\nCurrent:\n{}".format(
                baseline_df.head().to_string(),
                current_df.head().to_string()
            )
        return False, f"Row counts do not match. Baseline: {len(baseline_df)}, Current: {len(current_df)}{sample_diff}"

    # Compare each column
    for col in baseline_df.columns:
        baseline_values = baseline_df[col]
        current_values = current_df[col]

        # For numeric columns, use approximate comparison
        if np.issubdtype(baseline_values.dtype, np.number):
            # Handle NaN values separately
            nan_mask_baseline = np.isnan(baseline_values)
            nan_mask_current = np.isnan(current_values)

            # Check if NaN values match
            if not (nan_mask_baseline == nan_mask_current).all():
                mismatched_idx = (nan_mask_baseline != nan_mask_current)
                first_mismatch = mismatched_idx[mismatched_idx].index[0]
                return False, format_diff_message(baseline_values[first_mismatch],
                                               current_values[first_mismatch], col)

            # Compare non-NaN values with tolerance
            non_nan_mask = ~nan_mask_baseline  # same as ~nan_mask_current since we checked they match
            if non_nan_mask.any():  # Only compare if there are non-NaN values
                value_mismatch = abs(baseline_values[non_nan_mask] - current_values[non_nan_mask]) > float_tolerance
                if value_mismatch.any():
                    mismatched_idx = abs(baseline_values - current_values) > float_tolerance
                    first_mismatch = mismatched_idx[mismatched_idx & non_nan_mask].index[0]
                    return False, format_diff_message(baseline_values[first_mismatch],
                                                   current_values[first_mismatch], col)
        else:
            # For non-numeric columns, exact comparison
            if not (baseline_values.fillna('') == current_values.fillna('')).all():
                mismatched_idx = (baseline_values != current_values)
                first_mismatch = mismatched_idx[mismatched_idx].index[0]
                return False, format_diff_message(baseline_values[first_mismatch],
                                               current_values[first_mismatch], col)

    return True, "DataFrames match"

@pytest.mark.parametrize("portfolio_id", [
    "myinvestpilot_us_1",
    "myinvestpilot_cn_1",
    "myinvestpilot_cc_1",
    "myinvestpilot_us_dip_1",
    "myinvestpilot_us_dip_2",
    "myinvestpilot_cn_1_primitive",
    "myinvestpilot_us_1_primitive",
    "myinvestpilot_market_filtered",
    "myinvestpilot_market_trend",
    "us_etf_momentum_rotation"
])
def test_portfolio_against_baseline(portfolio_id):
    """
    Compare generated portfolio status against baseline data.
    Only compares the data, does not generate or clean any data.

    Args:
        portfolio_id (str): Portfolio identifier to test
    """
    print(f"Testing portfolio: {portfolio_id}")

    # 1. Load baseline data
    baseline_path = os.path.join("tests", "baseline", portfolio_id, "portfolio_status.csv")
    baseline_status = load_csv_data(baseline_path)
    if baseline_status is None:
        pytest.fail(f"Baseline data not found: {baseline_path}")

    # 2. Load the backtest generated data
    portfolio_db = os.path.join("data", portfolio_id, f"{portfolio_id}_portfolio.db")
    current_status = load_db_table(portfolio_db, "portfolio_status")
    if current_status is None:
        pytest.fail(f"Generated data not found: {portfolio_db}")

    # 3. Compare the data
    matched, message = compare_dataframes(baseline_status, current_status, "portfolio_status", float_tolerance=1e-3)
    if not matched:
        print(f"❌ {message}")
        pytest.fail(message)
    print("✓ Portfolio status matched")
