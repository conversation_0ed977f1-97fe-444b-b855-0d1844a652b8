# 投资策略服务测试架构

本目录包含了投资策略服务的完整测试架构，包括单元测试、集成测试和基线测试。测试架构设计遵循以下原则：

- 确保核心功能正常工作
- 在改动代码后快速发现问题
- 保持测试维护成本在合理范围
- 确保测试执行高效

## 测试类型

### 1. 单元测试 (Unit Tests)
快速验证独立组件的功能正确性，包含以下测试套件：

#### 1.1 金丝雀测试 (Canary Tests)
位置：`tests/canary/`
- 验证投资组合创建功能
- 确保回测流程的基本执行
- 检查核心数据结构的完整性

#### 1.2 核心测试 (Core Tests)
位置：`tests/core/`

##### 分析器测试 (`test_analyzers.py`)
- 最大回撤计算验证
- 夏普比率计算验证
- 预期结果的准确性验证

##### 资金策略测试 (`test_capital_strategies.py`)
- 百分比资金分配策略验证
- 持仓限制功能测试
- 资金分配计算准确性验证

##### 参数传递测试 (`test_parameters.py`)
- 验证参数正确传递到投资组合
- 确保配置覆盖机制正常工作

#### 1.3 健壮性测试 (Robustness Tests)
位置：`tests/robustness/`
- 无效股票代码处理
- 未来日期数据请求处理
- 空数据处理
- 异常输入处理

### 2. 集成测试 (Integration Tests)
位置：`tests/integration/`

#### 2.0 生成或更新基线数据

运行`generate_baseline_data.sh`脚本以生成或更新基线数据。该脚本会从外部API获取数据并存储在`tests/baseline/`目录下。

#### 2.1 投资组合回测测试
- 完整的端到端回测流程验证
- 与外部服务（Redis、OHLC代理）的集成测试
- 数据持久化和检索测试

#### 2.2 基线测试 (Baseline Tests)
位置：`tests/baseline/`
- 验证不同投资组合的回测结果
- 确保策略性能符合预期
- 检测策略行为的回归问题

## 测试环境要求

### 单元测试环境
- Python 3.10+
- pytest
- 不需要外部服务

### 集成测试环境
- Redis 服务
- OHLC数据代理
- 完整的环境变量配置
- 外部API访问权限

## 测试执行

### 运行单元测试
```bash
./run_unit_tests.sh
```
这将执行所有金丝雀测试、核心测试和健壮性测试。这些测试快速且不依赖外部服务。

### 运行集成测试
```bash
./run_integration_tests.sh
```
这将：
1. 设置必要的测试环境（Redis、OHLC代理等）
2. 执行端到端回测测试
3. 验证与基线数据的一致性
4. 清理测试环境

## CI/CD 集成

本项目使用 GitHub Actions 进行持续集成和部署。工作流程如下：

1. 代码提交触发测试
2. 首先运行单元测试套件
3. 然后设置集成测试环境并运行集成测试
4. 所有测试通过后，main 分支的更改会触发部署

详细配置请参见 `.github/workflows/deploy.yml`。

## 测试辅助工具

### 数据加载器
位置：`tests/utils/test_helpers.py`

1. MockMinimalDataLoader
- 提供最小化测试数据集
- 支持不同的价格模式（上升、下降、震荡）
- 生成符合实际格式的OHLCV数据

2. EmptyDataLoader
- 用于测试系统对空数据的处理
- 返回结构正确但数据为空的DataFrame

## 信号比较测试框架

### 简介

信号比较测试框架是一个专门设计用于比较原始策略代码与原语化策略配置生成的交易信号的工具。该框架可以精确验证原语化策略是否正确复制了原始策略的行为，从而确保在将现有策略迁移到原语框架时保持一致性。

### 功能特点

- **信号级别比较**：直接在信号生成级别进行比较，而不是仅依赖最终的回测结果
- **可视化差异**：生成图表显示价格走势和信号差异点，帮助直观理解差异
- **详细报告**：生成包含差异信息的HTML报告和CSV文件
- **灵活配置**：支持多种策略和配置格式，易于与现有策略集成
- **命令行界面**：提供简单的命令行工具，无需编写代码即可进行比较

### 使用方法

#### Python API使用

```python
from tests.core.signal_comparison_framework import run_signal_test

# 使用便捷函数运行完整比较流程
results = run_signal_test(
    data_path="tests/data/AAPL_sample.csv",
    symbol="AAPL",
    original_strategy=original_strategy,
    primitive_config=primitive_config,
    output_dir="tests/output/DualMovingAverageStrategy",
    ignore_first_n=50  # 跳过前50个数据点的比较
)
```

#### 命令行使用

```bash
# Python模块方式
python -m tests.core.signal_comparison_runner \
    --data tests/data/AAPL_sample.csv \
    --symbol AAPL \
    --strategy DualMovingAverageStrategy \
    --config tests/data/dual_ma.json \
    --output-dir tests/output/DualMovingAverageStrategy

# 测试双均线策略（Shell脚本方式）
./run_signal_comparison.sh \
    -d tests/data/AAPL_sample.csv \
    -s AAPL \
    -t DualMovingAverageStrategy \
    -p short_window=11,long_window=22 \
    -c tests/data/dual_ma_config.json \
    -o tests/output/DualMovingAverageStrategy

# 测试吊灯止损策略
./run_signal_comparison.sh \
    -d tests/data/AAPL_sample.csv \
    -s AAPL \
    -t ChandelierExitMAStrategy \
    -c tests/data/chandelier_exit_ma.json \
    -o tests/output/ChandelierExitMAStrategy
```

### 输出结果

框架会生成以下输出文件:

1. **CSV文件** (`comparison_results_{symbol}.csv`): 包含原始和原语策略信号的完整对比数据
2. **HTML报告** (`comparison_report_{symbol}.html`): 包含比较结果摘要、差异点位和上下文数据
3. **图表** (`signal_comparison_{symbol}.png`): 显示价格走势和信号对比的可视化图表

### 调试技巧

1. **使用 `ignore_first_n` 参数**: 许多策略在初始化阶段会有NaN值或不完整的信号，使用此参数可跳过这些初始数据点
2. **检查首个差异点**: 报告会高亮显示第一个出现差异的点，这通常是找出根本原因的关键
3. **比较周围上下文**: 报告中会显示差异点周围的数据，有助于理解导致差异的条件

## 编写新测试

### 测试命名约定
- 测试文件名应以 `test_` 开头
- 测试函数名应描述被测试的功能
- 使用清晰的注释说明测试目的

### 测试结构建议
1. 准备测试数据
2. 执行被测试的功能
3. 验证结果
4. 清理测试环境（如果需要）

### 示例
```python
def test_portfolio_creation():
    """Test if portfolio objects can be created"""
    try:
        factory = PortfolioFactory()
        portfolios = factory.get_portfolio_configs()
        assert len(portfolios) > 0, "Portfolio configuration is empty"
    except Exception as e:
        pytest.fail(f"Failed to create portfolio factory: {str(e)}")
```

## 最佳实践

1. 测试隔离
- 使用 MockMinimalDataLoader 而不是真实数据源
- 确保测试数据的可预测性和一致性
- 避免测试间的相互依赖

2. 错误处理
- 测试应该验证错误处理机制
- 使用适当的异常捕获和断言
- 确保清晰的错误信息

3. 性能考虑
- 单元测试应该快速执行
- 较重的测试放在集成测试套件中
- 合理使用测试夹具(fixtures)重用设置代码

4. 持续维护
- 定期审查和更新基线测试数据
- 删除过时或冗余的测试
- 确保测试文档及时更新
