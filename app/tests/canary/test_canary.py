import pytest
import pandas as pd
import logging
from portfolios.portfolio_manager import PortfolioManager
from portfolio_factory_v2 import PortfolioFactory
from tests.utils.test_helpers import MockMinimalDataLoader

logger = logging.getLogger(__name__)

def test_portfolio_creation():
    """测试能否创建投资组合对象"""
    try:
        # 创建工厂实例
        factory = PortfolioFactory()

        # 加载配置
        factory.load_portfolio_config_from_file("portfolio_config.json")
        portfolios = factory.get_portfolio_configs()

        # 确保配置已加载
        assert len(portfolios) > 0, "投资组合配置为空"
        logger.info(f"发现 {len(portfolios)} 个投资组合配置")
    except Exception as e:
        pytest.fail(f"创建投资组合工厂失败: {str(e)}")

def test_minimal_portfolio_run():
    """测试能否执行最小化的回测过程"""
    try:
        # 使用美股1号作为测试对象
        portfolio_code = "myinvestpilot_us_1"
        factory = PortfolioFactory()
        factory.load_portfolio_config_from_file("portfolio_config.json")

        # 使用最小化数据加载器
        data_loader = MockMinimalDataLoader()
        portfolio = factory.get_portfolio(portfolio_code, data_loader)

        # 执行回测
        collector = portfolio.execute_trades()

        # 获取状态
        portfolio_status = collector.portfolio_status

        # 验证关键字段
        assert portfolio_status is not None, "无法获取投资组合状态"
        assert not portfolio_status.empty, "投资组合状态为空"
        assert 'net_value' in portfolio_status.columns, "状态中缺少净值信息"
        assert portfolio_status['net_value'].iloc[0] > 0, "净值异常"

        logger.info(f"投资组合 {portfolio_code} 金丝雀测试通过")

        # 输出主要指标作为参考
        metrics = portfolio_status.iloc[0]
        logger.info(f"净值: {metrics['net_value']:.4f}")
        logger.info(f"夏普率: {metrics.get('sharpe_ratio', 'N/A')}")
        logger.info(f"最大回撤: {metrics.get('max_drawdown', 'N/A')}")

    except Exception as e:
        pytest.fail(f"投资组合运行失败: {str(e)}")

def test_market_indicator_portfolio_run():
    """测试市场指标策略能否执行回测过程"""
    try:
        # 使用市场指标过滤策略作为测试对象
        portfolio_code = "myinvestpilot_market_filtered"
        factory = PortfolioFactory()
        factory.load_portfolio_config_from_file("portfolio_config_complex_primitive.json")

        # 使用最小化数据加载器
        data_loader = MockMinimalDataLoader()
        portfolio = factory.get_portfolio(portfolio_code, data_loader)

        # 执行回测
        collector = portfolio.execute_trades()

        # 获取状态
        portfolio_status = collector.portfolio_status

        # 验证关键字段
        assert portfolio_status is not None, "无法获取投资组合状态"
        assert not portfolio_status.empty, "投资组合状态为空"
        assert 'net_value' in portfolio_status.columns, "状态中缺少净值信息"
        assert portfolio_status['net_value'].iloc[0] > 0, "净值异常"

        logger.info(f"市场指标策略 {portfolio_code} 金丝雀测试通过")

        # 输出主要指标作为参考
        metrics = portfolio_status.iloc[0]
        logger.info(f"净值: {metrics['net_value']:.4f}")
        logger.info(f"夏普率: {metrics.get('sharpe_ratio', 'N/A')}")
        logger.info(f"最大回撤: {metrics.get('max_drawdown', 'N/A')}")

    except Exception as e:
        pytest.fail(f"市场指标策略运行失败: {str(e)}")
