# fly.toml app configuration file generated for invest-strategy-service on 2024-02-24T23:04:50+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "invest-strategy-service"
primary_region = "sin"

[env]
  PORT = "8080"

[[services]]
  protocol = "tcp"
  internal_port = 8080
  auto_stop_machines = true
  auto_start_machines = false
  min_machines_running = 0

  [[services.ports]]
    port = 80
    handlers = ["http"]

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]
  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    interval = "10s"
    timeout = "2s"
    grace_period = "10s"

[[vm]]
  size = 'shared-cpu-2x'
  memory = '512mb'
  cpu_kind = 'shared'
  cpus = 2
