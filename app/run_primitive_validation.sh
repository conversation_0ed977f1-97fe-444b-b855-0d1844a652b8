#!/bin/bash
set -e  # Exit on any error

# 颜色配置，用于标识输出类型
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to setup environment
setup_environment() {
    # If local .env file exists, use it
    if [ -f ".env" ]; then
        print_message "$BLUE" "使用已有的 .env 文件..."
        ENV_FILE=".env"
    else
        print_message "$BLUE" "未找到 .env 文件，创建 .env.test..."
        cat > .env.test << EOL
FLY_UPSTASH_REDIS_HOST=localhost
FLY_UPSTASH_REDIS_PORT=16379
FLY_UPSTASH_REDIS_PASSWORD=${FLY_UPSTASH_REDIS_PASSWORD}
INVEST_OHLC_PROXY_HOST=http://localhost:8000
EOL
        ENV_FILE=".env.test"
    fi
}

# Function to cleanup on script exit
cleanup() {
    print_message "$BLUE" "清理验证测试环境..."
    if [ ! -z "$REDIS_CONNECT_PID" ]; then
        kill $REDIS_CONNECT_PID 2>/dev/null || true
    fi
    if [ ! -z "$OHLC_PROXY_PID" ]; then
        kill $OHLC_PROXY_PID 2>/dev/null || true
    fi
    pkill flyctl 2>/dev/null || true
    # Only remove .env.test if we created it
    if [ "$ENV_FILE" = ".env.test" ]; then
        rm -f .env.test
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Setup environment
setup_environment

print_message "$BLUE" "设置原语验证测试环境..."

# Maximum number of retries
MAX_RETRIES=6
RETRY_INTERVAL=5

# Start Redis connection
print_message "$BLUE" "启动 Redis 连接..."
$(dirname $PWD)/connect_redis.exp &
REDIS_CONNECT_PID=$!

# Start OHLC proxy
print_message "$BLUE" "启动 OHLC 代理..."
flyctl proxy 8000 -a invest-ohlc-proxy &
OHLC_PROXY_PID=$!

# Function to check if a service is ready
wait_for_service() {
    local host=$1
    local port=$2
    local service=$3
    local retries=0
    
    print_message "$BLUE" "等待 $service 就绪..."
    while ! nc -z $host $port >/dev/null 2>&1; do
        retries=$((retries + 1))
        if [ $retries -ge $MAX_RETRIES ]; then
            print_message "$RED" "错误: $service 在 $MAX_RETRIES 次尝试后仍未就绪"
            return 1
        fi
        print_message "$YELLOW" "等待 $service... (尝试 $retries/$MAX_RETRIES)"
        sleep $RETRY_INTERVAL
    done
    print_message "$GREEN" "$service 已就绪"
    return 0
}

print_message "$BLUE" "检查验证测试服务..."

# Wait for services to be ready
if ! wait_for_service localhost 16379 "Redis 连接"; then
    print_message "$RED" "无法连接到 Redis"
    exit 1
fi

if ! wait_for_service localhost 8000 "OHLC 代理"; then
    print_message "$RED" "无法连接到 OHLC 代理"
    exit 1
fi

print_message "$GREEN" "验证测试环境就绪"

# Define primitive strategy portfolios to test
PORTFOLIOS=(
    "example_rsi_ma"
    "example_bollinger_bands"
    "example_macd_volume"
)

# 检查是否有传入参数指定特定的投资组合
if [ $# -gt 0 ]; then
    PORTFOLIOS=("$@")
    print_message "$BLUE" "将测试指定的投资组合: ${PORTFOLIOS[*]}"
fi

# Choose a suitable test date (recent past)
# 兼容Mac(BSD)和Linux(GNU)版本的date命令
if [[ "$(uname)" == "Darwin" ]]; then
    # Mac OS (BSD) 版本的date命令
    TEST_DATE=$(date -v-30d +%Y-%m-%d)
else
    # Linux (GNU) 版本的date命令
    TEST_DATE=$(date -d "30 days ago" +%Y-%m-%d)
fi
print_message "$BLUE" "使用测试日期: $TEST_DATE"

# Execute backtests
for portfolio in "${PORTFOLIOS[@]}"; do
    print_message "$BLUE" "----------------------------------------"
    print_message "$BLUE" "处理投资组合: $portfolio"
    print_message "$BLUE" "测试日期: $TEST_DATE"
    
    # Check data directory before backtest
    print_message "$BLUE" "回测前数据目录:"
    ls -la data/$portfolio/ 2>/dev/null || print_message "$YELLOW" "目录不存在"
    
    # Run backtest
    print_message "$BLUE" "运行回测..."
    env $(cat $ENV_FILE) python main_v2.py --mode local --code $portfolio \
        --end-date "$TEST_DATE" || {
        print_message "$RED" "错误: 运行 $portfolio 的回测失败"
        exit 1
    }
    
    # Check data directory after backtest
    print_message "$BLUE" "回测后数据目录:"
    ls -la data/$portfolio/ || print_message "$YELLOW" "目录不存在"
    
    # Verify portfolio database exists
    if [ ! -f "data/$portfolio/${portfolio}_portfolio.db" ]; then
        print_message "$RED" "警告: 未生成投资组合数据库!"
        exit 1
    else
        print_message "$GREEN" "成功生成投资组合数据库"
    fi
    
    # 检查交易信号
    print_message "$BLUE" "验证交易信号..."
    # 首先检查信号数据库是否存在
    if [ ! -f "data/$portfolio/${portfolio}_signals.db" ]; then
        print_message "$YELLOW" "警告: 信号数据库不存在，跳过信号验证"
    else
        env $(cat $ENV_FILE) python -c "
import sqlite3
import pandas as pd

# 连接到信号数据库
conn = sqlite3.connect('data/$portfolio/${portfolio}_signals.db')

# 查询交易信号数据
try:
    signals_df = pd.read_sql('SELECT * FROM trade_signals ORDER BY date DESC LIMIT 20', conn)
    signals_count = len(signals_df)

    # 检查是否有交易信号
    if signals_count > 0:
        print(f'找到 {signals_count} 条交易信号:')
        print(signals_df.head(5)[['date', 'symbol', 'signal']])
    else:
        print('警告: 没有找到交易信号')
    print(f'验证了 {signals_count} 个信号记录')

    # 关闭连接
    conn.close()
except Exception as e:
    print(f'警告: 无法查询信号表: {str(e)}')
    print('可能是新组合尚未生成信号')
    # 确保即使发生异常也关闭连接
    try:
        conn.close()
    except:
        pass
" || {
        print_message "$YELLOW" "警告: 验证 $portfolio 的交易信号过程中出现非致命问题"
    }
    fi
    
    print_message "$GREEN" "$portfolio 验证成功"
    print_message "$BLUE" "----------------------------------------"
done

print_message "$GREEN" "所有原语策略配置验证测试通过 ✓"
