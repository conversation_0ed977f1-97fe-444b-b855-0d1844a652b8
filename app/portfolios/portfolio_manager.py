from datetime import datetime
import json
import os
import logging

import pandas as pd
import sentry_sdk
from analyzers.xirr_analyzer import CashFlowReturnAnalyzer, XIRRAnalyzer
from analyzers.equal_weight_buy_hold_analyzer import EqualWeightBuyHoldAnalyzer
from analyzers.return_attribution_analyzer import ReturnAttributionAnalyzer
from capital_strategies.fixed_investment_strategy import FixedInvestmentStrategy
from trade_strategies import TradeSignalState

from utilities.db import execute_query_to_dict
from utilities.helpers import float_precision_handler, get_date_from_str, get_today_str, get_yesterday_str, render_html, round_floats, safe_format
from constants import PORTFOLIO_DB_BASE_DIR
from brokers.custom_broker import CustomBroker
from . import BacktraderStrategyAdapter, BasePortfolioManager, CapitalStrategySizerAdapter

from portfolios.portfolio_data_collector import PortfolioDataCollector
from portfolios.portfolio_trade_signal_collector import PortfolioTradeSignalCollector
from portfolios.cash_flow_adjusted_nav_observer import CashFlowAdjustedNAVObserver
import backtrader as bt
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from matplotlib import gridspec
from matplotlib.animation import FuncAnimation
import gc

module_logger = logging.getLogger(__name__)

matplotlib.use('Agg') # 避免在服务器上绘图时出现错误

class PortfolioManager(BasePortfolioManager):
    def __init__(self, config, data_loader):
        super().__init__(
            name=config['name'],
            code=config['code'],
            description=config['description'],
            strategy=config['strategy'],
            capital_strategy=config['capital_strategy'],
            commission=config['commission'],
            start_date=config['start_date'],
            end_date=config.get('end_date', get_today_str()),
            data_loader=data_loader,
        )
        self.symbols = [symbol['symbol'] for symbol in config['symbols']]
        self.symbol_names = {symbol['symbol']: (symbol['name'], symbol['name']) for symbol in config['symbols']}
        self.currency = config.get('currency', 'USD')
        self.market = config.get('market', 'US')
        self.cerebro = bt.Cerebro()
        self.cerebro.setbroker(CustomBroker())
        
        # 设置文件路径
        self._setup_file_paths()
        
        # 创建或清理数据目录
        self._prepare_data_directory()
    
    def _setup_file_paths(self):
        """设置所有文件路径"""
        today_str = get_today_str()
        today_str_underscored = today_str.replace("-", "_")
        
        self.base_data_dir = f'{PORTFOLIO_DB_BASE_DIR}/{self.code}'
        self.signals_db_name = f'{self.base_data_dir}/{self.code}_signals.db'
        self.portfolio_db_name = f'{self.base_data_dir}/{self.code}_portfolio.db'
        
        # 信号相关文件
        self.portfolio_today_signals_name = f'{self.base_data_dir}/{self.code}_signals_{today_str_underscored}.json'
        self.portfolio_today_signals_en_email = f'{self.base_data_dir}/{self.code}_signals_{today_str_underscored}_en.html'
        self.portfolio_today_signals_zh_email = f'{self.base_data_dir}/{self.code}_signals_{today_str_underscored}_zh.html'
        
        # 图表文件
        self.portfolio_image_fullsize_name = f'{self.base_data_dir}/{self.code}_portfolio_fullsize.png'
        self.portfolio_image_thumbnail_name = f'{self.base_data_dir}/{self.code}_portfolio_thumbnail.png'
        
        # 日志文件
        self.portfolio_log_file = f'{self.base_data_dir}/{self.code}_{today_str_underscored}.log'

    def _prepare_data_directory(self):
        """准备数据目录，如果不存在则创建，如果存在则清理"""
        if not os.path.exists(self.base_data_dir):
            os.makedirs(self.base_data_dir)
        else:
            for file in os.listdir(self.base_data_dir):
                os.remove(f'{self.base_data_dir}/{file}')
                
    def get_symbol_names(self, symbol):
        """给定股票代码，返回其中文和英文名称"""
        return self.symbol_names.get(symbol, ("Unknown", "Unknown"))

    def execute_trades(self):
        """执行交易并生成分析结果"""
        with sentry_sdk.start_transaction(op="execute_trades", name=f"Execute trades for {self.code}"):
            sentry_sdk.set_tag("portfolio_code", self.code)
            sentry_sdk.set_context("portfolio_config", self._get_portfolio_context())
            
            try:
                module_logger.info(f"Executing trades for portfolio {self.name}...")
                
                # 设置初始配置
                self._setup_initial_configuration()
                
                # 初始化收集器并收集数据
                data_collector, trade_signal_collector = self._initialize_and_collect_signals()
                
                # 添加策略和分析器
                self._add_strategy_and_analyzers()
                
                # 执行模拟并收集结果
                self._run_simulation_and_collect_data(data_collector, trade_signal_collector)
                
                # 生成报告和可视化
                self._generate_reports_and_visualizations(data_collector)
                
                return data_collector
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                module_logger.error(f"Failed to execute trades for portfolio {self.code}. Error: {str(e)}")
                sentry_sdk.capture_exception(e)
                raise

    def _get_portfolio_context(self):
        """获取投资组合上下文信息，用于日志和错误追踪"""
        return {
            "name": self.name,
            "symbols": self.symbols,
            "start_date": self.start_date,
            "end_date": self.end_date
        }

    def _setup_initial_configuration(self):
        """初始化Cerebro和资金设置"""
        module_logger.info(f"Setting initial capital to {self.capital_strategy.initial_capital}")
        self.cerebro.broker.set_cash(self.capital_strategy.initial_capital, get_date_from_str(self.start_date))
        self.cerebro.addsizer(CapitalStrategySizerAdapter, capital_strategy=self.capital_strategy)

    def _initialize_and_collect_signals(self):
        """初始化数据收集器并收集交易信号"""
        data_collector = PortfolioDataCollector(self)
        trade_signal_collector = PortfolioTradeSignalCollector(self)
        trade_signal_collector.collect_data()
        return data_collector, trade_signal_collector

    def _add_strategy_and_analyzers(self):
        """添加策略和所有分析器"""
        # 添加策略
        self.cerebro.addstrategy(BacktraderStrategyAdapter)
        
        # 添加基础分析器
        self._add_basic_analyzers()
        
        # 添加风险分析器
        self._add_risk_analyzers()
        
        # 添加策略特定分析器
        self._add_strategy_specific_analyzers()
        
        # 添加现金流调整的净值观察者
        self.cerebro.addobserver(CashFlowAdjustedNAVObserver)
        module_logger.info("Added CashFlowAdjustedNAVObserver to track performance with cash flow adjustments")
        
        # 设置佣金
        self.cerebro.broker.setcommission(commission=self.commission, margin=None)

    def _add_basic_analyzers(self):
        """添加基础分析器"""
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='SharpeRatio')
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name='DrawDown')
        self.cerebro.addanalyzer(bt.analyzers.TimeReturn, _name='TimeReturn')
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='TradeAnalyzer')
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name='Returns')
        self.cerebro.addanalyzer(bt.analyzers.SQN, _name='SQN')
        self.cerebro.addanalyzer(bt.analyzers.AnnualReturn, _name='AnnualReturn')
        self.cerebro.addanalyzer(bt.analyzers.VWR, _name='VWR')

    def _add_risk_analyzers(self):
        """添加风险相关分析器"""
        from analyzers.risk_metrics_analyzer import RiskMetricsAnalyzer
        self.cerebro.addanalyzer(RiskMetricsAnalyzer, _name='RiskMetrics')
    
    def _add_strategy_specific_analyzers(self):
        """添加根据策略类型决定的特定分析器"""
        if isinstance(self.capital_strategy, FixedInvestmentStrategy):
            module_logger.info("Adding CashFlowReturnAnalyzer for fixed investment strategy")
            # 使用新的CashFlowReturnAnalyzer，但保持相同的名称以保持兼容性
            self.cerebro.addanalyzer(CashFlowReturnAnalyzer, _name='XIRR')
        else:
            module_logger.info("Adding equal weight buy-hold analyzer")
            self.cerebro.addanalyzer(EqualWeightBuyHoldAnalyzer,
                                   _name='EqualWeightBuyHold',
                                   initial_capital=self.capital_strategy.initial_capital)

        self.cerebro.addanalyzer(ReturnAttributionAnalyzer, _name='ReturnAttribution')

    def _run_simulation_and_collect_data(self, data_collector, trade_signal_collector):
        """执行回测模拟并收集数据"""
        self.portfolio_data = self.transform_portfolio_data(self.cerebro.run(), trade_signal_collector)
        data_collector.collect_data(self.portfolio_data)
        self.post_process_results(data_collector.portfolio_status, data_collector)
        self.export_to_db(trade_signal_collector, data_collector)

    def _generate_reports_and_visualizations(self, data_collector):
        """生成报告和可视化"""
        self.generate_portfolio_today_notification()
        self.export_email_html()
        self.plot_portfolio_image(data_collector)

    def transform_portfolio_data(self, results, trade_signal_collector):
        strategy = results[0]
        
        # Get comprehensive risk metrics from RiskMetricsAnalyzer
        risk_metrics_analysis = strategy.analyzers.RiskMetrics.get_analysis()
        
        # Get supplementary metrics from other analyzers
        vwr_analysis = strategy.analyzers.VWR.get_analysis()
        vwr_value = vwr_analysis.get('vwr', 0) if isinstance(vwr_analysis, dict) else vwr_analysis
        
        # Combine all risk metrics into a single dictionary
        risk_metrics = {
            # Core metrics from RiskMetricsAnalyzer
            **risk_metrics_analysis,
            
            # Supplementary metrics from other analyzers
            'sharpe_ratio': round_floats(strategy.analyzers.SharpeRatio.get_analysis().get('sharperatio', 0)),
            'sqn': round_floats(strategy.analyzers.SQN.get_analysis().get('sqn', 0)),
            'vwr': round_floats(vwr_value),
            'xirr': round_floats(strategy.analyzers.XIRR.get_analysis().get('xirr', 0)) if hasattr(strategy.analyzers, 'XIRR') else None
        }
        
        # 获取现金流调整后的净值记录（如果可用）
        cash_flow_adjusted_nav = None
        try:
            # 查找CashFlowAdjustedNAVObserver
            for observer in strategy.observers:
                if isinstance(observer, CashFlowAdjustedNAVObserver):
                    if hasattr(observer, 'get_analysis'):
                        cash_flow_adjusted_nav = observer.get_analysis()
                        module_logger.info(f"Retrieved cash flow adjusted NAV data from observer: {len(cash_flow_adjusted_nav)} records")
                        break
        except Exception as e:
            module_logger.warning(f"Failed to retrieve cash flow adjusted NAV data: {e}")
            cash_flow_adjusted_nav = None
        
        portfolio_data = {
            # Portfolio values
            "final_value": self.cerebro.broker.getvalue(),
            "fund_value": self.cerebro.broker.get_fundvalue(),
            
            # Records
            "time_return_records": strategy.analyzers.TimeReturn.get_analysis(),
            "trade_analyzer": strategy.analyzers.TradeAnalyzer.get_analysis(),
            "annual_return": strategy.analyzers.AnnualReturn.get_analysis(),
            "positions_records": strategy.get_positions(),
            'trade_records': strategy.get_trades(),
            "capital_records": strategy.get_capital(),
            "cash_value_records": strategy.get_cash_value(),
            "trade_signals": trade_signal_collector.signals,
            
            # 现金流调整后的净值记录（如果可用）
            "cash_flow_adjusted_nav": cash_flow_adjusted_nav,
            
            # Combined risk metrics
            "risk_metrics": risk_metrics
        }
        
        # Add equal weight buy hold analysis (non-fixed investment portfolios)
        if not isinstance(self.capital_strategy, FixedInvestmentStrategy) and hasattr(strategy.analyzers, 'EqualWeightBuyHold'):
            portfolio_data["equal_weight_buy_hold"] = strategy.analyzers.EqualWeightBuyHold.get_analysis()
        
        # Add return attribution analysis for all portfolios
        portfolio_data["return_attribution"] = strategy.analyzers.ReturnAttribution.get_analysis()
        
        return portfolio_data

    def post_process_results(self, portfolio_status, data_collector):
        # 初始化组合状态字典
        portfolio_status_dict = {
            'code': self.code,
            'name': self.name,
            'currency': self.currency,
            'final_value': self.cerebro.broker.getvalue()
        }
        portfolio_status_dict.update(portfolio_status.to_dict(orient='records')[0])

        # 添加基础描述
        portfolio_status_dict['win_rate_description'] = f"胜率: {safe_format(portfolio_status_dict.get('win_rate'), '{:.2%}')}"
        portfolio_status_dict['profit_loss_ratio_description'] = f"盈亏比: {safe_format(portfolio_status_dict.get('profit_loss_ratio'), '{:.2f}')}"
        portfolio_status_dict['max_drawdown_duration_description'] = f"最大回撤持续时间: {safe_format(portfolio_status_dict.get('max_drawdown_duration_days'), '{}')} 天"

        # 添加收益率指标
        if isinstance(self.capital_strategy, FixedInvestmentStrategy):
            # 定投策略显示XIRR和净值法收益率
            xirr_value = data_collector.risk_metrics.get('xirr')
            portfolio_status_dict['xirr'] = xirr_value
            portfolio_status_dict['xirr_description'] = f"定投实际年化收益率(XIRR): {safe_format(xirr_value, '{:.2%}')}"
            portfolio_status_dict['cagr_description'] = f"净值法年化收益率: {safe_format(portfolio_status_dict.get('cagr'), '{:.2%}')}"
        else:
            # 其他策略只显示净值法收益率
            portfolio_status_dict['cagr_description'] = f"年化收益率: {safe_format(portfolio_status_dict.get('cagr'), '{:.2%}')}"

        # 添加风险指标描述
        portfolio_status_dict['sharpe_ratio_description'] = f"夏普比率: {safe_format(portfolio_status_dict.get('sharpe_ratio'), '{:.2f}')}"
        portfolio_status_dict['sqn_description'] = f"系统质量数: {safe_format(portfolio_status_dict.get('sqn'), '{:.2f}')}"
        
        # 添加波动率加权收益
        vwr_value = data_collector.risk_metrics.get('vwr', 0)
        if isinstance(vwr_value, dict):
            vwr_value = vwr_value.get('vwr', 0)
        portfolio_status_dict['vwr'] = vwr_value
        portfolio_status_dict['vwr_description'] = f"波动率加权收益: {safe_format(vwr_value, '{:.2f}')}"

        # 添加年度回报率描述
        annual_returns = data_collector.risk_metrics.get('annual_returns', {})
        annual_returns_desc = "年度回报率: "
        for year, return_value in annual_returns.items():
            annual_returns_desc += f"{year}: {safe_format(return_value, '{:.2%}')}, "
        portfolio_status_dict['annual_returns_description'] = annual_returns_desc.rstrip(', ')

        # 添加交易统计
        portfolio_status_dict['trade_summary_description'] = (
            f"总交易次数: {safe_format(portfolio_status_dict.get('total_trades'), '{}')}, "
            f"盈利交易: {safe_format(portfolio_status_dict.get('profit_trades'), '{}')}, "
            f"亏损交易: {safe_format(portfolio_status_dict.get('loss_trades'), '{}')}"
        )

        self.portfolio_status = portfolio_status_dict

    def export_to_db(self, trade_signal_collector, data_collector):
        trade_signal_collector.persist_to_db()
        data_collector.persist_to_db()

    def _execute_db_query(self, db_name, query, params=None):
        """执行数据库查询，处理文件不存在的情况"""
        if not os.path.exists(db_name):
            return None
        
        return execute_query_to_dict(db_name, query, params or ())

    def _create_empty_signals_dict(self):
        """创建空的信号字典结构"""
        return {
            TradeSignalState.HOLD.value: [],
            TradeSignalState.SELL.value: [],
            TradeSignalState.BUY.value: [],
            TradeSignalState.EMPTY.value: [],
            TradeSignalState.ERROR.value: [],
            'B_empty': True,
            'S_empty': True
        }

    def _process_signals(self, signals, symbol_order):
        """处理原始信号数据，添加名称和排序"""
        signals_dict = self._create_empty_signals_dict()
        
        for signal in signals:
            signal['cn_name'] = self.get_symbol_names(signal['symbol'])[0]
            signal['en_name'] = self.get_symbol_names(signal['symbol'])[1]
            signal['open'] = signal['signal_open_price']
            signal['original_order'] = symbol_order.get(signal['symbol'], float('inf'))
            
            signals_dict[signal['signal']].append(signal)
        
        # 对每个信号组进行排序
        for key in signals_dict:
            if isinstance(signals_dict[key], list):
                signals_dict[key] = sorted(signals_dict[key], key=lambda x: x['original_order'])
                # 移除临时的排序键
                for signal in signals_dict[key]:
                    signal.pop('original_order', None)
        
        # 更新空操作标记
        signals_dict['B_empty'] = len(signals_dict[TradeSignalState.BUY.value]) == 0
        signals_dict['S_empty'] = len(signals_dict[TradeSignalState.SELL.value]) == 0
        
        return signals_dict

    def get_today_signals(self):
        """获取今日的交易信号，包括买入、卖出、持有、空仓和错误信号"""
        yesterday = get_yesterday_str()
        symbol_order = {symbol: index for index, symbol in enumerate(self.symbols)}
        
        query = """
        WITH signal_changes AS (
            SELECT t1.*,
                (SELECT t2.date
                    FROM trade_signals t2
                    WHERE t2.symbol = t1.symbol
                    AND t2.date <= ?
                    AND t2.signal != t1.signal
                    ORDER BY t2.date DESC
                    LIMIT 1) as last_change_date
            FROM trade_signals t1
            WHERE t1.date = ?
        ),
        signal_info AS (
            SELECT sc.*,
                CASE
                    WHEN sc.last_change_date IS NULL THEN sc.date
                    ELSE (SELECT MIN(t3.date)
                            FROM trade_signals t3
                            WHERE t3.symbol = sc.symbol
                            AND t3.date > sc.last_change_date
                            AND t3.signal = sc.signal)
                END as signal_date
            FROM signal_changes sc
        )
        SELECT 
            si.symbol,
            si.signal,
            si.date as current_date,
            si.signal_date,
            (SELECT open
            FROM trade_signals
            WHERE symbol = si.symbol
            AND date = si.signal_date) as signal_open_price,
            si.close as latest_close,
            (si.close - (SELECT open FROM trade_signals WHERE symbol = si.symbol AND date = si.signal_date)) / (SELECT open FROM trade_signals WHERE symbol = si.symbol AND date = si.signal_date) * 100 as profit_loss_percent
        FROM signal_info si
        """
        
        signals = self._execute_db_query(self.signals_db_name, query, (yesterday, yesterday))
        if not signals:
            return self._create_empty_signals_dict()
        
        return self._process_signals(signals, symbol_order)

    def get_today_trades(self):
        """获取今日的交易记录"""
        query = "SELECT * FROM trade_records WHERE strftime('%Y-%m-%d', date) = ?"
        return self._execute_db_query(self.portfolio_db_name, query, (get_yesterday_str(),))

    def get_today_positions(self):
        """获取今日的持仓信息"""
        query = "SELECT * FROM position_records WHERE strftime('%Y-%m-%d', date) = ?"
        positions = self._execute_db_query(self.portfolio_db_name, query, (get_yesterday_str(),))
        if not positions:
            return []

        positions_dict = []
        for position in positions:
            date = position['date']
            symbol = position['symbol']
            
            # 获取最近一次交易记录
            query = """
                SELECT * FROM trade_records
                WHERE strftime('%Y-%m-%d', date) <= ?
                AND code = ?
                ORDER BY strftime('%Y-%m-%d', date) DESC
                LIMIT 1
            """
            last_trade = self._execute_db_query(
                self.portfolio_db_name,
                query,
                (datetime.strptime(date, '%Y-%m-%d'), symbol)
            )

            if last_trade:
                last_trade = last_trade[0]
                profit_loss = (position['close_price'] - last_trade['price']) * position['position_size']
                if last_trade['type'] != 'buy':
                    profit_loss = -profit_loss

                positions_dict.append({
                    "hold_target": position,
                    "last_trade": last_trade,
                    "profit_loss_percent": profit_loss / (last_trade['price'] * last_trade['size']) * 100
                })

        return positions_dict
    
    def get_net_value_daily_change_percent(self):
        """获取每日净值变化百分比"""
        query = """
            SELECT 
                date,
                (net_value - LAG(net_value) OVER (ORDER BY date)) / LAG(net_value) OVER (ORDER BY date) * 100 AS percentage_change
            FROM net_values
            ORDER BY date
        """
        result = self._execute_db_query(self.portfolio_db_name, query)
        if not result:
            return None

        df = pd.DataFrame(result, columns=['date', 'percentage_change'])
        df['date'] = pd.to_datetime(df['date'])
        return df

    def generate_portfolio_today_notification(self):
        today_signals = self.get_today_signals()        
        today_trades = self.get_today_trades()
        today_positions = self.get_today_positions()
        portfolio_status = self.get_portfolio_status()

        # 更新B/S无操作的信号
        today_signals['B_empty'] = len(today_signals['B']) == 0
        today_signals['S_empty'] = len(today_signals['S']) == 0

        # 将CAGR和当前回撤转换为百分比
        portfolio_status['cagr'] = portfolio_status['cagr'] * 100
        portfolio_status['current_drawdown'] = portfolio_status['current_drawdown'] * 100

        # 昨日涨跌百分比
        portfolio_status['daily_change_percent'] = self.get_net_value_daily_change_percent().iloc[-1]['percentage_change']

        # 计算持仓百分比
        final_value = portfolio_status['final_value']
        for p in today_positions:
            value = p['hold_target']['value']
            position_percent = value / final_value * 100
            p['hold_target']['position_percent'] = position_percent
        
        notification_data = {
            "date": get_today_str(),
            "signals": today_signals,
            "portfolio": {
                "meta": portfolio_status,
                "trades": today_trades,
                "positions": today_positions
            }
        }
        
        notification_json = json.dumps(notification_data, indent=4, ensure_ascii=False, default=str)
        
        with open(self.portfolio_today_signals_name, "w") as json_file:
            json_file.write(notification_json)
        
        module_logger.info("Portfolio today notification generated successfully.")

    def plot_portfolio_image(self, data_collector):
        """绘制投资组合图表"""
        # 获取绘图数据
        df = data_collector.benchmark_index_records
        capital_df = data_collector.cash_value_records
        percentage_change_df = self.get_net_value_daily_change_percent()

        # 创建主图和子图
        self._create_main_chart(df, capital_df, percentage_change_df)
        
        # 创建缩略图
        self._create_thumbnail(df)

    def _get_plot_colors(self):
        """获取绘图颜色列表"""
        return [
            '#1f77b4',  # 淡蓝色
            '#ff7f0e',  # 淡橙色
            '#2ca02c',  # 淡绿色
            '#d62728',  # 淡红色
            '#9467bd',  # 淡紫色
            '#8c564b',  # 棕色
            '#e377c2',  # 粉色
            '#7f7f7f',  # 灰色
            '#bcbd22',  # 橄榄绿
            '#17becf'   # 青色
        ]

    def _create_main_chart(self, df, capital_df, percentage_change_df):
        """创建主图和子图"""
        plt.figure(figsize=(10, 10))
        gs = gridspec.GridSpec(3, 1, height_ratios=[8, 2, 2])
        
        # 创建子图并共享x轴
        ax0 = plt.subplot(gs[0])
        ax1 = plt.subplot(gs[1], sharex=ax0)
        ax2 = plt.subplot(gs[2], sharex=ax0)
        
        # 绘制各个部分
        self._plot_net_value_chart(ax0, df)
        self._plot_position_percentage(ax1, capital_df)
        self._plot_value_change_percentage(ax2, percentage_change_df)
        
        # 保存图像并清理资源
        plt.savefig(self.portfolio_image_fullsize_name)
        self._cleanup_plot_resources()

    def _plot_net_value_chart(self, ax, df):
        """绘制净值对比图"""
        colors = self._get_plot_colors()
        color_index = 0
        
        # 绘制基准指数
        for column in df.columns:
            if column != "net_value":
                ax.plot(df.index, df[column], 
                       label=column, 
                       color=colors[color_index % len(colors)], 
                       linewidth=0.5)
                color_index += 1
        
        # 绘制净值曲线
        ax.plot(df.index, df["net_value"], 
                label="Net Value", 
                color='red', 
                linewidth=2)
        
        # 设置图表属性
        ax.legend(loc='best', ncol=5)
        ax.set_title("Portfolio Net Value vs. Benchmark Indices")
        ax.set_ylabel("Normalized Value")

    def _plot_position_percentage(self, ax, capital_df):
        """绘制持仓百分比图"""
        capital_df['position_percentage'] = 1 - capital_df['cash'] / capital_df['value']
        ax.fill_between(capital_df.index, 
                       0, 
                       capital_df['position_percentage'], 
                       color='skyblue', 
                       step='pre')
        ax.set_ylabel("Position %")

    def _plot_value_change_percentage(self, ax, percentage_change_df):
        """绘制净值变化百分比图"""
        significant_change = percentage_change_df['percentage_change'].abs() > 2
        
        # 绘制所有变化
        ax.bar(percentage_change_df['date'], 
               percentage_change_df['percentage_change'], 
               color='gray')
        
        # 高亮显著变化
        ax.bar(percentage_change_df['date'][significant_change], 
               percentage_change_df['percentage_change'][significant_change], 
               color='red')
        
        ax.set_ylabel("% Change")
        ax.set_xlabel("Date")

    def _create_thumbnail(self, df):
        """创建缩略图"""
        plt.figure(figsize=(4, 2), dpi=100)
        plt.plot(df.index, df["net_value"], color='red', linewidth=1)
        plt.axis('off')
        plt.savefig(self.portfolio_image_thumbnail_name, 
                   bbox_inches='tight', 
                   pad_inches=0)
        self._cleanup_plot_resources()

    def _cleanup_plot_resources(self):
        """清理绘图资源"""
        plt.clf()
        plt.close('all')
        gc.collect()

    def export_portfolio_video(self, data_collector):
        """导出投资组合动画视频"""
        df = data_collector.benchmark_index_records
        dates = df.index
        
        # 创建动画对象
        fig, ax, lines = self._setup_video_plot(df, dates)
        anim = self._create_animation(fig, ax, lines, df, dates)
        
        # 导出视频
        anim.save(self.portfolio_video_fullsize_name, writer='ffmpeg', fps=60)
        plt.close(fig)

    def _setup_video_plot(self, df, dates):
        """设置视频绘图对象"""
        net_value = df["net_value"]
        indices = [col for col in df.columns if col == 'HS300' or col == 'HSI' or col == 'SPX']
        
        fig, ax = plt.subplots()
        line, = ax.plot(dates, net_value, color='red', label='Net Value', linewidth=2)
        index_lines = [ax.plot(dates, df[index], label=index, linewidth=1)[0] 
                      for index in indices]
        
        ax.set_xlim(dates[0], dates[-1])
        ax.set_ylim(min(df.min()), max(df.max()))
        ax.legend(loc='upper left')
        
        return fig, ax, [line] + index_lines

    def _create_animation(self, fig, ax, lines, df, dates):
        """创建动画对象"""
        net_value = df["net_value"]
        indices = [col for col in df.columns if col == 'HS300' or col == 'HSI' or col == 'SPX']
        
        def init():
            return lines

        def update(frame):
            # 更新净值曲线
            lines[0].set_data(dates[:frame], net_value[:frame])
            # 更新指数曲线
            for i, index in enumerate(indices):
                lines[i+1].set_data(dates[:frame], df[index][:frame])
            return lines

        return FuncAnimation(fig, update, 
                           frames=np.arange(1, len(dates)), 
                           init_func=init, 
                           blit=True)

    def export_email_html(self):
        with open(self.portfolio_today_signals_name, 'r', encoding='utf-8') as f:
            signal_data = float_precision_handler(json.load(f))

        if not any(signal_data['signals'][key] for key in ['H', 'S', 'B', 'E', 'X']):
            module_logger.info("No trading signals for today.")
            return None
        
        template_path = 'templates/email'
        
        email_html_en = render_html(signal_data, template_path, "en")
        email_html_zh = render_html(signal_data, template_path, "zh")

        with open(self.portfolio_today_signals_en_email, "w", encoding='utf-8') as f:
            f.write(email_html_en)
        
        with open(self.portfolio_today_signals_zh_email, "w", encoding='utf-8') as f:
            f.write(email_html_zh)

        module_logger.info("Portfolio today email HTML generated successfully.")

    def sync_to_s3(self):
        module_logger.info("Syncing portfolio data to S3...")
        
        files_to_upload_private = [
            self.signals_db_name,
            self.portfolio_today_signals_name,
            self.portfolio_today_signals_en_email,
            self.portfolio_today_signals_zh_email
        ]
        files_to_upload_public = [
            self.portfolio_db_name,
            self.portfolio_image_fullsize_name,
            self.portfolio_image_thumbnail_name,
            self.portfolio_log_file
        ]

        for file_name in files_to_upload_private:
            if os.path.exists(file_name):
                self.data_loader.upload_to_private_bucket(self.code, file_name)
            else:
                module_logger.warning(f"File {file_name} does not exist. Skipping upload to private bucket.")

        for file_name in files_to_upload_public:
            if os.path.exists(file_name):
                self.data_loader.upload_to_public_bucket(self.code, file_name)
            else:
                module_logger.warning(f"File {file_name} does not exist. Skipping upload to public bucket.")

        module_logger.info("Portfolio data synced to S3 successfully.")
