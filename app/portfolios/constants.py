"""
Constants used across the portfolio management system.

This module contains database column definitions and other constants used by
various components of the portfolio management system.
"""

# Database column definitions for various portfolio records
PORTFOLIO_DB_COLUMN_DEFINITIONS = {
    'net_values': ['date', 'net_value'],
    'trade_records': ['date', 'code', 'name', 'type', 'size', 'price', 'amount'],
    'position_records': ['date', 'symbol', 'name', 'position_size', 'close_price', 'value'],
    'capital_records': ['date', 'trade_type', 'change_amount', 'available_cash', 'total_assets'],
    'benchmark_index': ['date', 'net_value', 'HS300', 'ZZ500', 'CYB', 'HSI', 'SPX', 'IXIC', 'GDAXI', 'N225', 'KS11', 'AS51', 'SENSEX', '15pct_annual_return'],
    'equal_weight_benchmark': ['date', 'initial_capital', 'final_value', 'overall_return', 'cagr', 'max_drawdown', 'sharpe_ratio'],
    'return_attribution_annual': ['year', 'return', 'contribution', 'contribution_percentage'],
    'return_attribution_assets': ['symbol', 'name', 'return', 'contribution', 'contribution_percentage'],
    'portfolio_status': [
        'date', 'net_value', 'fund_value', 'cagr', 'sharpe_ratio', 'current_drawdown',
        'max_drawdown', 'max_drawdown_duration_days', 'total_trades', 'profit_trades',
        'loss_trades', 'running_days', 'inception_date', 'win_rate', 'profit_loss_ratio',
        'sqn', 'annual_return', 'annual_returns', 'vwr', 'calmar', 'xirr'
    ]
}

# Columns for tracking portfolio cash and value
PORTFOLIO_CASH_VALUE_COLUMNS = ['date', 'cash', 'value']
