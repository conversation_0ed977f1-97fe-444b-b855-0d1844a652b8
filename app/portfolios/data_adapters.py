"""
Data adapters for portfolio management.

This module provides adapters for converting different data formats into
structures that can be used by the portfolio management system.
"""

from typing import Any, List
import backtrader as bt

class CustomPandasData(bt.feeds.PandasData):
    """
    Custom PandasData adapter for converting pandas DataFrame to Backtrader format.
    
    This adapter extends Backtrader's PandasData to handle custom data columns,
    particularly adding support for trading signals. Additional columns can be
    added by extending the lines and params attributes as needed.
    
    Attributes:
        lines (tuple): Defines additional data line for trading signals
        params (tuple): Parameter mapping for the additional data line
        
    Example:
        >>> df = pd.DataFrame(...)  # Your pandas DataFrame with OHLCV + signal data
        >>> data = CustomPandasData(
        ...     dataname=df,
        ...     datetime='date',
        ...     open='open',
        ...     high='high',
        ...     low='low',
        ...     close='close',
        ...     volume='volume',
        ...     signal='signal'
        ... )
    """
    
    # Define additional lines (data series) that will be available
    lines: tuple = ('signal',)
    
    # Define parameters mapping DataFrame columns to new lines
    # -1 is the default value if the column is not found
    params: tuple = (('signal', -1),)
    
    @classmethod
    def get_line_names(cls) -> List[str]:
        """
        Get all available data line names.
        
        Returns:
            List[str]: List of all data line names including default and custom ones
        """
        # Get default lines from parent class
        default_lines = super().lines
        # Combine with custom lines
        all_lines = default_lines + cls.lines
        return [line.__name__ for line in all_lines]
    
    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize the CustomPandasData adapter.
        
        Args:
            **kwargs: Keyword arguments passed to PandasData parent class.
                     Must include 'dataname' (pandas DataFrame) and column mappings.
        """
        super().__init__(**kwargs)
