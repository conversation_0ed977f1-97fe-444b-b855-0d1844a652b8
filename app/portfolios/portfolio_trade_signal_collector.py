import logging
import pandas as pd
import sentry_sdk
from utilities.db import persist_dataframe_to_sqlite
from portfolios import CustomPandasData
from trade_strategies import TradeSignalState, convert_numeric_signals_to_string, convert_signals_to_numeric
from utilities.helpers import is_china_stock, is_cryptocurrency

module_logger = logging.getLogger(__name__)

class PortfolioTradeSignalCollector:
    """
    1. Load historical price data for symbols into the Portfolio Manager Cerebro instance
    2. Collect historical trading signals from the portfolio trading strategy
    3. Reorder trading signals according to the portfolio symbols and persist to SQLite database
    """

    def __init__(self, portfolio_manager):
        self.portfolio_manager = portfolio_manager
        self.signals = None

    def align_data(self, symbol_strategy_signals, trading_days):
        # Check if symbol_strategy_signals is empty
        if symbol_strategy_signals is None or symbol_strategy_signals.empty:
            module_logger.warning("Empty symbol strategy signals provided to align_data. Cannot proceed with alignment.")
            return symbol_strategy_signals

        # Check if 'symbol' column exists
        if 'symbol' not in symbol_strategy_signals.columns:
            module_logger.warning("Symbol strategy signals missing 'symbol' column. Cannot proceed with alignment.")
            return symbol_strategy_signals

        symbol = symbol_strategy_signals['symbol'].iloc[0]

        # Check if index is empty
        if len(symbol_strategy_signals.index) == 0:
            module_logger.warning(f"Symbol {symbol} has no data points. Cannot proceed with alignment.")
            return symbol_strategy_signals

        symbol_start_date = symbol_strategy_signals.index[0]
        symbol_end_date = symbol_strategy_signals.index[-1]
        portfolio_start_date = pd.to_datetime(self.portfolio_manager.start_date)
        portfolio_end_date = pd.to_datetime(self.portfolio_manager.end_date)

        # Check if trading_days is valid
        if trading_days is None or len(trading_days) == 0:
            module_logger.error(f"Invalid trading days provided. Cannot align data for {symbol}.")
            return symbol_strategy_signals

        # Handle case where symbol start date is after portfolio start date
        if symbol_start_date > portfolio_start_date:
            module_logger.info(f"Symbol {symbol} has a start date of {symbol_start_date}, which is after the portfolio start date of {portfolio_start_date}. Filling in missing data...")

            # Only fill data on trading days
            fill_dates = trading_days[(trading_days >= portfolio_start_date) & (trading_days < symbol_start_date)]
            # Ensure data types match the original data when creating supplementary data
            fill_data = pd.DataFrame(index=fill_dates, columns=symbol_strategy_signals.columns)
            # Set the same data type for each column
            for col in symbol_strategy_signals.columns:
                if col in ['symbol']:
                    fill_data[col] = symbol
                elif col in ['open', 'high', 'low', 'close', 'volume']:
                    fill_data[col] = 0
                    fill_data[col] = fill_data[col].astype(symbol_strategy_signals[col].dtype)
                elif col == 'signal':
                    fill_data[col] = TradeSignalState.EMPTY.value
                    fill_data[col] = fill_data[col].astype(symbol_strategy_signals[col].dtype)
                # For other possible columns, keep as NaN

            # Use concat_kwargs to prevent FutureWarning
            concat_kwargs = {'ignore_index': False, 'sort': False}
            symbol_strategy_signals = pd.concat([fill_data, symbol_strategy_signals], **concat_kwargs)
            module_logger.info(f"Added {len(fill_dates)} days of data for {symbol} at the beginning")

        # Handle case where symbol end date is before portfolio end date (suspended stocks)
        if symbol_end_date < portfolio_end_date:
            module_logger.info(f"Symbol {symbol} has an end date of {symbol_end_date}, which is before the portfolio end date of {portfolio_end_date}. Forward filling data...")

            # Only fill data on trading days
            fill_dates = trading_days[(trading_days > symbol_end_date) & (trading_days <= portfolio_end_date)]

            if not fill_dates.empty:
                # Create fill data
                fill_data_end = pd.DataFrame(index=fill_dates, columns=symbol_strategy_signals.columns)

                # Get the last row data as fill value
                last_row = symbol_strategy_signals.iloc[-1].copy()

                # Set the same data type and value for each column
                for col in symbol_strategy_signals.columns:
                    # For all columns, use the last valid value
                    fill_data_end[col] = last_row[col]
                    if col in symbol_strategy_signals.dtypes:
                        fill_data_end[col] = fill_data_end[col].astype(symbol_strategy_signals[col].dtype)

                # Add fill data to original data
                symbol_strategy_signals = pd.concat([symbol_strategy_signals, fill_data_end])

                # Ensure index is datetime type and sorted
                symbol_strategy_signals.index = pd.to_datetime(symbol_strategy_signals.index)
                symbol_strategy_signals = symbol_strategy_signals.sort_index()

                module_logger.info(f"Added {len(fill_dates)} days of data for {symbol} at the end")

        return symbol_strategy_signals

    def collect_data(self):
        with sentry_sdk.start_span(op="collect_trade_signals"):
            try:
                # 确定投资组合的主要市场
                symbols = self.portfolio_manager.symbols
                market = "US"  # 默认为美股市场
                
                # 检查是否有A股或加密货币
                if any(is_china_stock(s) for s in symbols):
                    market = "CN"
                elif any(is_cryptocurrency(s) for s in symbols):
                    market = "CRYPTO"
                    
                # 获取回测期间的交易日
                trading_days = self.portfolio_manager.data_loader.get_trading_days(
                    self.portfolio_manager.start_date, 
                    self.portfolio_manager.end_date,
                    market=market
                )
                
                if trading_days is None or len(trading_days) == 0:
                    raise ValueError(f"No trading days found between {self.portfolio_manager.start_date} and {self.portfolio_manager.end_date}")
                
                # 判断策略是否需要全局数据
                strategy_needs_global_data = getattr(self.portfolio_manager.strategy, 'needs_global_data', False)
                
                if strategy_needs_global_data:
                    # 全局数据处理：一次性加载所有数据并生成所有信号
                    module_logger.info("Processing strategy with global data view")
                    
                    # 加载所有交易标的的数据
                    all_symbol_data = {}
                    for symbol in self.portfolio_manager.symbols:
                        data = self.portfolio_manager.data_loader.get_qfq_close_price(symbol, self.portfolio_manager.end_date)
                        
                        if data is None or data.empty:
                            module_logger.warning(f"Unable to get price data for {symbol}, skipping this symbol")
                            continue
                            
                        # 确保日期格式正确
                        data.index = pd.to_datetime(data.index)
                        all_symbol_data[symbol] = data
                    
                    if not all_symbol_data:
                        raise ValueError("No data could be loaded for any symbol")
                    
                    # 设置全局数据
                    self.portfolio_manager.strategy.set_global_data(all_symbol_data)
                    
                    # 一次性生成所有信号
                    all_signals = self.portfolio_manager.strategy.generate_all_signals()
                    
                    # 处理所有生成的信号
                    self.signals = []
                    for symbol, symbol_strategy_signals in all_signals.items():
                        if symbol_strategy_signals is None or symbol_strategy_signals.empty:
                            module_logger.warning(f"No signals generated for {symbol}, skipping this symbol")
                            continue
                            
                        # 确保日期格式正确
                        symbol_strategy_signals.index = pd.to_datetime(symbol_strategy_signals.index)
                        
                        # 对齐数据
                        symbol_strategy_signals = self.align_data(symbol_strategy_signals, trading_days)
                        market_data_with_signals = convert_signals_to_numeric(symbol_strategy_signals)
                        
                        # 确保日期索引格式正确
                        market_data_with_signals.index = pd.to_datetime(market_data_with_signals.index)
                        
                        # 只保留回测期间的数据
                        start_date = pd.to_datetime(self.portfolio_manager.start_date)
                        end_date = pd.to_datetime(self.portfolio_manager.end_date)
                        market_data_with_signals = market_data_with_signals.loc[start_date:end_date]
                        
                        # 检查是否有足够的数据
                        if market_data_with_signals.empty:
                            module_logger.warning(f"{symbol} has no data in the backtest period, skipping this symbol")
                            continue
                            
                        module_logger.info(f"{symbol} final data range: {market_data_with_signals.index[0]} to {market_data_with_signals.index[-1]}")
                        
                        # 添加数据到Cerebro
                        bt_data = CustomPandasData(dataname=market_data_with_signals)
                        self.portfolio_manager.cerebro.adddata(bt_data, name=symbol)
                        
                        self.signals.append(convert_numeric_signals_to_string(symbol_strategy_signals))
                else:
                    # 原有的逐个标的处理逻辑
                    # Load data and add to Cerebro
                    for symbol in self.portfolio_manager.symbols:
                        # Use data_loader to get data and generate trading signals
                        data = self.portfolio_manager.data_loader.get_qfq_close_price(symbol, self.portfolio_manager.end_date)

                        if data is None or data.empty:
                            module_logger.warning(f"Unable to get price data for {symbol}, skipping this symbol")
                            continue

                        # Ensure date format is correct
                        data.index = pd.to_datetime(data.index)

                        self.portfolio_manager.strategy.set_data(symbol, data)
                        symbol_strategy_signals = self.portfolio_manager.strategy.generate_signals()

                        # Ensure date format is correct
                        symbol_strategy_signals.index = pd.to_datetime(symbol_strategy_signals.index)

                        # Check if trading signals are complete and align data
                        symbol_strategy_signals = self.align_data(symbol_strategy_signals, trading_days)
                        market_data_with_signals = convert_signals_to_numeric(symbol_strategy_signals)

                        # Ensure date index format is correct
                        market_data_with_signals.index = pd.to_datetime(market_data_with_signals.index)

                        # Only keep data within the backtest period
                        start_date = pd.to_datetime(self.portfolio_manager.start_date)
                        end_date = pd.to_datetime(self.portfolio_manager.end_date)
                        market_data_with_signals = market_data_with_signals.loc[start_date:end_date]

                        # Check if there is enough data
                        if market_data_with_signals.empty:
                            module_logger.warning(f"{symbol} has no data in the backtest period, skipping this symbol")
                            continue

                        module_logger.info(f"{symbol} final data range: {market_data_with_signals.index[0]} to {market_data_with_signals.index[-1]}")

                        # Add data to Cerebro
                        bt_data = CustomPandasData(dataname=market_data_with_signals)
                        self.portfolio_manager.cerebro.adddata(bt_data, name=symbol)

                        if self.signals is None:
                            self.signals = [convert_numeric_signals_to_string(symbol_strategy_signals)]
                        else:
                            self.signals.append(convert_numeric_signals_to_string(symbol_strategy_signals))

                # Check if we have any successfully processed data
                if self.signals is None or len(self.signals) == 0:
                    raise IndexError("index out of bounds - no data was successfully processed for any symbol")

                # Use updated concat method to avoid FutureWarning
                concat_kwargs = {'ignore_index': False, 'sort': False}
                combined_signals = pd.concat(self.signals, **concat_kwargs)
                combined_signals.index = pd.to_datetime(combined_signals.index)
                combined_signals = combined_signals.sort_index()

                combined_signals.index.names = ['date']

                self.signals = combined_signals
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                sentry_sdk.capture_exception(e)
                raise

    def persist_to_db(self):
        db_name = self.portfolio_manager.signals_db_name

        # Ensure signals is a DataFrame
        if isinstance(self.signals, list):
            concat_kwargs = {'ignore_index': False, 'sort': False}
            self.signals = pd.concat(self.signals, **concat_kwargs)

        # Reset index to make symbol a column
        self.signals = self.signals.reset_index()

        # Create a mapping dictionary for sorting
        symbol_order = {symbol: i for i, symbol in enumerate(self.portfolio_manager.symbols)}

        # Add a sorting column
        self.signals['symbol_order'] = self.signals['symbol'].map(symbol_order)

        # Sort by date and symbol_order
        self.signals = self.signals.sort_values(['date', 'symbol_order'])

        # Remove the sorting column
        self.signals = self.signals.drop('symbol_order', axis=1)

        # Persist to database
        persist_dataframe_to_sqlite(self.signals, db_name, 'trade_signals')
