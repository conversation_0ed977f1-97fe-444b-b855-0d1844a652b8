"""
Base portfolio management implementation.

This module provides the foundation for portfolio management functionality through
the BasePortfolioManager class.
"""

from typing import Any, Dict, Optional
from utilities.helpers import get_days_between, get_today_str
from datetime import date
import logging

module_logger = logging.getLogger(__name__)

class BasePortfolioManager:
    """
    Base class for portfolio management implementations.
    
    This class defines the interface and common functionality for portfolio management.
    Specific implementations should inherit from this class and implement the
    required methods.
    
    Attributes:
        name (str): The name of the portfolio
        code (str): Unique identifier code for the portfolio
        description (str): Detailed description of the portfolio
        strategy: The trading strategy to be used
        capital_strategy: The capital allocation strategy
        commission (float): Trading commission rate
        start_date (str): Portfolio start date in YYYY-MM-DD format
        end_date (str): Portfolio end date in YYYY-MM-DD format
        portfolio_status (Optional[Dict[str, Any]]): Current portfolio status
        data_loader: Data loader instance for fetching market data
        _current_period_net_cash_flow_for_observer (float): 当前周期用于通知观察者的净现金流
    """
    
    def __init__(
        self,
        name: str,
        code: str,
        description: str,
        strategy: Any,  # TODO: Create proper Strategy type
        capital_strategy: Any,  # TODO: Create proper CapitalStrategy type
        commission: float,
        data_loader: Any,  # TODO: Create proper DataLoader type
        start_date: str,
        end_date: str = get_today_str()
    ) -> None:
        """
        Initialize a new portfolio manager instance.
        
        Args:
            name: Portfolio name
            code: Unique identifier for the portfolio
            description: Detailed description of the portfolio
            strategy: Trading strategy instance
            capital_strategy: Capital allocation strategy instance
            commission: Trading commission rate as a decimal (e.g., 0.0003 for 0.03%)
            data_loader: Data loader instance for market data
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format, defaults to today
        """
        self.name = name
        self.code = code
        self.description = description
        self.strategy = strategy
        self.capital_strategy = capital_strategy
        self.commission = commission
        self.start_date = start_date
        self.end_date = end_date
        self.portfolio_status: Optional[Dict[str, Any]] = None
        self.data_loader = data_loader
        self._current_period_net_cash_flow_for_observer = 0.0

    def execute_trades(self) -> None:
        """
        Execute trading operations based on the portfolio's strategy.
        
        This method must be implemented by subclasses to define specific trading logic.
        
        Raises:
            NotImplementedError: If the subclass does not implement this method
        """
        raise NotImplementedError("Must implement execute_trades method!")

    def get_portfolio_status(self) -> Optional[Dict[str, Any]]:
        """
        Get the current status of the portfolio.
        
        Returns:
            Optional[Dict[str, Any]]: Dictionary containing portfolio status metrics,
            or None if status hasn't been calculated yet
        """
        return self.portfolio_status

    def calculate_running_time(self) -> int:
        """
        Calculate the number of days the portfolio has been running.
        
        Returns:
            int: Number of days between start_date and end_date
        """
        return get_days_between(self.start_date, self.end_date)
        
    def _report_cash_flow_for_observer(self, amount: float) -> None:
        """
        内部方法，用于累积报告给观察者的净现金流。
        
        Args:
            amount: 现金流金额，正数表示流入，负数表示流出
        """
        self._current_period_net_cash_flow_for_observer += amount
        module_logger.debug(f"Accumulated cash flow for observer: {self._current_period_net_cash_flow_for_observer}")

    def get_and_reset_current_period_net_cash_flow_for_observer(self) -> float:
        """
        获取并重置当前周期的净现金流。此方法供观察者调用。
        
        Returns:
            float: 当前周期的净现金流
        """
        flow = self._current_period_net_cash_flow_for_observer
        self._current_period_net_cash_flow_for_observer = 0.0
        return flow
    
    def deposit_cash(self, amount: float, current_date: Optional[date] = None) -> None:
        """
        向投资组合中存入资金 (例如，定投、增资)。
        所有策略应通过此方法进行外部资金注入。

        Args:
            amount: 存入的金额，必须为正数
            current_date: 操作日期，如果为None则自动获取
        """
        if amount <= 0:
            module_logger.warning(f"尝试存入非正数金额: {amount}")
            return
        
        # 获取当前日期：优先使用传入参数，然后尝试从回测环境获取，最后使用今天
        if current_date is None:
            if hasattr(self, 'datas') and len(self.datas) > 0:
                try:
                    current_date = self.datas[0].datetime.date(0)
                except (AttributeError, IndexError):
                    current_date = date.today()
            else:
                current_date = date.today()
        
        # 调用实际的券商操作
        if hasattr(self, 'broker'):
            if hasattr(self.broker, 'add_cash') and current_date is not None:
                self.broker.add_cash(amount, current_date, change_type='deposit')
            else:
                self.broker.add_cash(amount)
        
        # 记录用于观察者的现金流
        self._report_cash_flow_for_observer(amount)  # 正数表示资金流入
        
        module_logger.info(f"Deposited {amount} to portfolio")

    def withdraw_cash(self, amount: float, current_date: Optional[date] = None) -> None:
        """
        从投资组合中提取资金 (例如，用户撤资)。
        所有策略应通过此方法进行外部资金提取。

        Args:
            amount: 提取的金额，必须为正数
            current_date: 操作日期，如果为None则自动获取
        """
        if amount <= 0:
            module_logger.warning(f"尝试提取非正数金额: {amount}")
            return
        
        # 获取当前日期：优先使用传入参数，然后尝试从回测环境获取，最后使用今天
        if current_date is None:
            if hasattr(self, 'datas') and len(self.datas) > 0:
                try:
                    current_date = self.datas[0].datetime.date(0)
                except (AttributeError, IndexError):
                    current_date = date.today()
            else:
                current_date = date.today()
        
        # 检查现金是否足够
        current_broker_cash = 0
        if hasattr(self, 'broker') and hasattr(self.broker, 'get_cash'):
            current_broker_cash = self.broker.get_cash()
        else:
            current_broker_cash = self.broker.getcash()
        
        if current_broker_cash >= amount:
            # 调用实际的券商操作
            if hasattr(self, 'broker'):
                if hasattr(self.broker, 'add_cash') and current_date is not None:
                    self.broker.add_cash(-amount, current_date, change_type='withdrawal')
                elif hasattr(self.broker, 'set_cash') and current_date is not None:
                    self.broker.set_cash(current_broker_cash - amount, current_date)
                else:
                    self.broker.add_cash(-amount)
            
            # 记录用于观察者的现金流
            self._report_cash_flow_for_observer(-amount)  # 负数表示资金流出
            
            module_logger.info(f"Withdrew {amount} from portfolio")
        else:
            module_logger.warning(f"现金不足 {amount} 以供提取。当前现金: {current_broker_cash}")

