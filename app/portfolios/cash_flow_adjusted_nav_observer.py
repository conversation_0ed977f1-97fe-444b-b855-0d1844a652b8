import backtrader as bt
import logging

module_logger = logging.getLogger(__name__)

class CashFlowAdjustedNAVObserver(bt.Observer):
    """
    Observer for performance NAV adjusted by cash flows in Backtrader.
    Tracks the time-weighted return (TWR) NAV, adjusted for external cash flows.
    """
    lines = ('nav_adjusted',)
    plotinfo = dict(plot=True, subplot=True, plotname='CashFlowAdjNAV')
    plotlines = dict(nav_adjusted=dict(_name='CashFlowAdjNAV'))

    def __init__(self):
        self._logger = module_logger
        self._prev_nav = 1.0
        self._prev_value = None
        self._broker = None
        self._portfolio = None
        self._initialized = False

    def next(self):
        if not self._initialized:
            self._broker = self._owner.broker
            self._portfolio = getattr(self._owner, 'strategy', None)
            self._prev_value = float(self._broker.getvalue())
            self._prev_nav = 1.0
            self._initialized = True
            # 只在 next 第一次赋初值
            self.lines.nav_adjusted[0] = self._prev_nav
            self._logger.info(f"[CFANAV] INIT: nav={self._prev_nav}, value={self._prev_value}")
            return

        cur_value = float(self._broker.getvalue())
        cashflow = 0.0
        if self._portfolio and hasattr(self._portfolio, 'get_and_reset_current_period_net_cash_flow_for_observer'):
            try:
                cashflow = float(self._portfolio.get_and_reset_current_period_net_cash_flow_for_observer())
            except Exception as e:
                self._logger.warning(f"CashFlowAdjustedNAVObserver: get cashflow failed: {e}")
        denominator = self._prev_value + cashflow
        if denominator == 0:
            nav = self._prev_nav
        else:
            nav = self._prev_nav * (cur_value / denominator)
        self.lines.nav_adjusted[0] = nav
        self._logger.info(f"[CFANAV] bar={len(self)}, nav={nav}, cashflow={cashflow}, cur_value={cur_value}, prev_value={self._prev_value}")
        self._prev_nav = nav
        self._prev_value = cur_value
