import backtrader as bt
import logging
import pandas as pd

module_logger = logging.getLogger(__name__)

class CashFlowAdjustedNAVObserver(bt.Observer):
    """
    Observer for performance NAV adjusted by cash flows in Backtrader.
    Tracks the time-weighted return (TWR) NAV, adjusted for external cash flows.
    """
    lines = ('nav_adjusted',)
    plotinfo = dict(plot=True, subplot=True, plotname='CashFlowAdjNAV')
    plotlines = dict(nav_adjusted=dict(_name='CashFlowAdjNAV'))

    def __init__(self):
        self._logger = module_logger
        self._prev_nav = 1.0
        self._prev_value = None
        self._broker = None
        self._strategy = None
        self._initialized = False
        self._nav_records = []  # 存储净值记录用于生成DataFrame
        self._cash_flow_this_period = 0.0  # 当前周期的现金流

    def next(self):
        if not self._initialized:
            self._broker = self._owner.broker
            self._strategy = self._owner  # 获取策略实例
            self._prev_value = float(self._broker.getvalue())
            self._prev_nav = 1.0
            self._initialized = True

            # 记录初始净值
            current_date = self._strategy.datas[0].datetime.date(0)
            self._nav_records.append({
                'date': current_date,
                'nav_adjusted': self._prev_nav
            })
            self.lines.nav_adjusted[0] = self._prev_nav
            self._logger.info(f"[CFANAV] INIT: nav={self._prev_nav}, value={self._prev_value}, date={current_date}")
            return

        # 获取当前日期和账户价值
        current_date = self._strategy.datas[0].datetime.date(0)
        cur_value = float(self._broker.getvalue())

        # 获取当前周期的现金流
        cashflow = self._get_current_period_cash_flow()

        # 计算调整后的净值
        denominator = self._prev_value + cashflow
        if denominator == 0:
            nav = self._prev_nav
        else:
            nav = self._prev_nav * (cur_value / denominator)

        # 记录净值
        self._nav_records.append({
            'date': current_date,
            'nav_adjusted': nav
        })
        self.lines.nav_adjusted[0] = nav

        self._logger.info(f"[CFANAV] bar={len(self)}, nav={nav:.6f}, cashflow={cashflow}, cur_value={cur_value}, prev_value={self._prev_value}, date={current_date}")

        # 更新状态
        self._prev_nav = nav
        self._prev_value = cur_value
        self._cash_flow_this_period = 0.0  # 重置现金流

    def _get_current_period_cash_flow(self):
        """获取当前周期的现金流"""
        # 从broker的capital_records中获取当天的现金流变动
        if hasattr(self._broker, 'capital_records'):
            current_date = self._strategy.datas[0].datetime.date(0)
            total_flow = 0.0

            for record in self._broker.capital_records:
                # 检查记录的键名，CustomBroker使用'trade_type'和'change_amount'
                record_date = record.get('date')
                record_type = record.get('trade_type', record.get('type'))  # 兼容两种格式
                record_amount = record.get('change_amount', record.get('amount', 0))  # 兼容两种格式

                if (record_date == current_date and
                    record_type in ['annual_investment', 'monthly_investment', 'deposit', 'withdrawal']):
                    total_flow += record_amount
                    self._logger.debug(f"Found cash flow: {record_amount} on {current_date}, type: {record_type}")

            return total_flow
        return 0.0

    def get_analysis(self):
        """返回分析结果，供PortfolioDataCollector使用"""
        if not self._nav_records:
            return pd.DataFrame(columns=['date', 'net_value'])

        # 转换为DataFrame
        df = pd.DataFrame(self._nav_records)
        df['net_value'] = df['nav_adjusted']  # 重命名列以匹配期望格式
        df.set_index('date', inplace=True)
        df.index = pd.to_datetime(df.index)
        df.sort_index(inplace=True)

        # 只返回net_value列
        return df[['net_value']]
