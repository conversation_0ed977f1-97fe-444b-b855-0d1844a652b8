import pandas as pd
import json
import logging
from contextlib import contextmanager
from typing import Any, Callable, Dict, List, Optional
import sentry_sdk
import backtrader as bt

from utilities.db import persist_dataframe_to_sqlite
from utilities.helpers import convert_keys_to_strings, convert_to_dict, json_serial, round_floats
from portfolios import PORTFOLIO_CASH_VALUE_COLUMNS, PORTFOLIO_DB_COLUMN_DEFINITIONS as COLUMN_DEFINITIONS

module_logger = logging.getLogger(__name__)

class PortfolioDataCollector:
    def __init__(self, portfolio_manager):
        self.portfolio_manager = portfolio_manager
        self.portfolio_data = None
        self.net_value_records = None
        self.trade_records = None
        self.position_records = None
        self.capital_records = None
        self.cash_value_records = None
        self.benchmark_index_records = None
        self.equal_weight_benchmark = None
        self.return_attribution_annual = None
        self.return_attribution_assets = None
        self.risk_metrics = {}
        self.portfolio_status = None

    @contextmanager
    def error_handling(self, operation_name: str):
        """Error handling context manager"""
        try:
            module_logger.info(f"Starting {operation_name}")
            yield
            module_logger.info(f"Finished {operation_name}")
        except Exception as e:
            module_logger.error(f"Error in {operation_name}: {str(e)}")
            raise

    def _collect_data_template(self, data_key: str, target_attr: str, column_def_key: str, 
                             process_func: Callable[[Dict], pd.DataFrame]):
        """Generic data collection template method"""
        with self.error_handling(f"collect_{data_key}"):
            data = self.portfolio_data.get(data_key, {})
            if not data:
                module_logger.info(f"No {data_key} data found.")
                setattr(self, target_attr, pd.DataFrame(columns=COLUMN_DEFINITIONS[column_def_key]))
                return
            
            df = process_func(data)
            setattr(self, target_attr, df)
            
            module_logger.info(f'--- {data_key} DataFrame ---')
            module_logger.info(df)
            module_logger.info('-' * (len(data_key) + 18))

    def collect_data(self, portfolio_data: Dict):
        """Main data collection method"""
        with sentry_sdk.start_span(op="collect_portfolio_data"):
            try:
                self.portfolio_data = portfolio_data
                self.collect_net_value()
                self.collect_trade_records()
                self.collect_position_records()
                self.collect_capital_records()
                self.collect_cash_value_records()
                self.collect_equal_weight()
                self.collect_return_attribution()
                self.collect_risk_metrics()
                self.compare_with_benchmark()
                self.update_portfolio_status()
            except Exception as e:
                module_logger.error(f"Error in collect_data: {str(e)}")
                sentry_sdk.capture_exception(e)
                raise

    def _ensure_date_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """Ensure correct date format in DataFrame"""
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df = df[df['date'] > pd.Timestamp('1971-01-01')]
            df['date'] = df['date'].dt.strftime('%Y-%m-%d')
        return df

    def _validate_numeric_fields(self, data_dict: Dict, fields: List[str]) -> bool:
        """Validate numeric fields in dictionary"""
        return all(isinstance(data_dict.get(f, 0), (int, float)) for f in fields)

    def collect_net_value(self):
        # 首先尝试使用现金流调整后的净值数据（如果可用）
        if 'cash_flow_adjusted_nav' in self.portfolio_data and self.portfolio_data['cash_flow_adjusted_nav'] is not None:
            adjusted_nav_df = self.portfolio_data['cash_flow_adjusted_nav']
            if not adjusted_nav_df.empty:
                module_logger.info("Using cash flow adjusted NAV data from CashFlowAdjustedNAVObserver")
                self.net_value_records = adjusted_nav_df
                return
        
        # 检查是否已经有净值记录
        if hasattr(self, 'net_value_records') and self.net_value_records is not None and not self.net_value_records.empty:
            # 如果已经有表现净值记录，直接使用
            module_logger.info("Using existing performance-adjusted net value records")
            return
            
        # 如果没有或为空，尝试使用传统方式从 time_return_records 计算（保持向后兼容）
        def process_net_value(time_returns):
            if not time_returns:  # 如果没有时间序列回报记录
                module_logger.warning("No time return records available for net value calculation")
                return pd.DataFrame(columns=['date', 'net_value'])
                
            net_values = []
            current_value = 1
            for date, return_rate in time_returns.items():
                current_value *= (1 + return_rate)
                net_values.append((date, current_value))
            
            df = pd.DataFrame(net_values, columns=['date', 'net_value'])
            df.set_index('date', inplace=True)
            df.index = pd.to_datetime(df.index)
            df.sort_index(inplace=True)
            return df

        self._collect_data_template('time_return_records', 'net_value_records', 
                                  'net_values', process_net_value)
        
        module_logger.warning("Using legacy method to calculate net values without cash flow adjustments")

    def collect_trade_records(self):
        def process_trade_records(records):
            df = pd.DataFrame(records)
            df.set_index('date', inplace=True)
            df['name'] = df['code'].map(lambda x: self.portfolio_manager.symbol_names.get(x, (x, x))[0])
            return df

        self._collect_data_template('trade_records', 'trade_records', 
                                  'trade_records', process_trade_records)

    def collect_position_records(self):
        def process_position_records(records):
            df = pd.DataFrame(records)
            if df.empty:
                return df
            
            df = df[df['position_size'] != 0]
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            df['name'] = df['symbol'].map(lambda x: self.portfolio_manager.symbol_names.get(x, (x, x))[0])
            
            for col in COLUMN_DEFINITIONS['position_records']:
                if col not in df.columns and col != 'date':
                    df[col] = None
                    
            return df

        self._collect_data_template('positions_records', 'position_records', 
                                  'position_records', process_position_records)

    def collect_capital_records(self):
        def process_capital_records(records):
            df = pd.DataFrame(records)
            for col in COLUMN_DEFINITIONS['capital_records']:
                if col not in df.columns:
                    df[col] = None
            
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            return df

        self._collect_data_template('capital_records', 'capital_records', 
                                  'capital_records', process_capital_records)

    def collect_cash_value_records(self):
        def process_cash_value_records(records):
            df = pd.DataFrame(records)
            for col in PORTFOLIO_CASH_VALUE_COLUMNS:
                if col not in df.columns:
                    df[col] = None
            
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            df.sort_index(inplace=True)
            return df

        self._collect_data_template('cash_value_records', 'cash_value_records', 
                                  'cash_value_records', process_cash_value_records)

    def collect_equal_weight(self):
        def process_equal_weight(data):
            current_date = pd.to_datetime(self.portfolio_manager.end_date)
            df = pd.DataFrame([{
                'date': current_date,
                'initial_capital': data.get('initial_capital', 0),
                'final_value': data.get('final_value', 0),
                'overall_return': data.get('overall_return', 0),
                'cagr': data.get('cagr', 0),
                'max_drawdown': data.get('max_drawdown', 0),
                'sharpe_ratio': data.get('sharpe_ratio', 0)
            }]).set_index('date')
            return df

        self._collect_data_template('equal_weight_buy_hold', 'equal_weight_benchmark', 
                                  'equal_weight_benchmark', process_equal_weight)

    def collect_return_attribution(self):
        def process_annual_attribution(attribution_data):
            annual_data = []
            annual_returns = attribution_data.get('annual_returns', {})
            annual_contributions = attribution_data.get('annual_contributions', {})
            annual_contribution_percentages = attribution_data.get('annual_contribution_percentages', {})

            for year in set(annual_returns.keys()) | set(annual_contributions.keys()) | set(annual_contribution_percentages.keys()):
                if not isinstance(year, (int, str)) or str(year).strip() == '':
                    continue
                
                data = {
                    'year': str(year),
                    'return': annual_returns.get(year, 0),
                    'contribution': annual_contributions.get(year, 0),
                    'contribution_percentage': annual_contribution_percentages.get(year, 0)
                }
                
                if self._validate_numeric_fields(data, ['return', 'contribution', 'contribution_percentage']):
                    annual_data.append(data)
            
            return pd.DataFrame(annual_data) if annual_data else pd.DataFrame(columns=COLUMN_DEFINITIONS['return_attribution_annual'])

        def process_asset_attribution(attribution_data):
            assets_data = []
            asset_returns = attribution_data.get('asset_returns', {})
            asset_contributions = attribution_data.get('asset_contributions', {})
            asset_contribution_percentages = attribution_data.get('asset_contribution_percentages', {})
            
            current_date = pd.to_datetime(self.portfolio_manager.end_date)
            
            for symbol in set(asset_returns.keys()) | set(asset_contributions.keys()) | set(asset_contribution_percentages.keys()):
                if not isinstance(symbol, str) or symbol.strip() == '':
                    continue
                
                data = {
                    'date': current_date,
                    'symbol': symbol,
                    'name': self.portfolio_manager.symbol_names.get(symbol, (symbol, ''))[0],
                    'return': asset_returns.get(symbol, 0),
                    'contribution': asset_contributions.get(symbol, 0),
                    'contribution_percentage': asset_contribution_percentages.get(symbol, 0)
                }
                
                if self._validate_numeric_fields(data, ['return', 'contribution', 'contribution_percentage']):
                    assets_data.append(data)
            
            return pd.DataFrame(assets_data) if assets_data else pd.DataFrame(columns=COLUMN_DEFINITIONS['return_attribution_assets'])

        with self.error_handling("collect_return_attribution"):
            attribution_data = self.portfolio_data.get('return_attribution', {})
            if not attribution_data:
                self.return_attribution_annual = pd.DataFrame(columns=COLUMN_DEFINITIONS['return_attribution_annual'])
                self.return_attribution_assets = pd.DataFrame(columns=COLUMN_DEFINITIONS['return_attribution_assets'])
                return
            
            self.return_attribution_annual = process_annual_attribution(attribution_data)
            self.return_attribution_assets = process_asset_attribution(attribution_data)

    def collect_risk_metrics(self):
        """Collect risk metrics from portfolio data. All metrics are now pre-processed in transform_portfolio_data."""
        with self.error_handling("collect_risk_metrics"):
            # Get the combined risk metrics that were already processed in transform_portfolio_data
            self.risk_metrics = convert_to_dict(self.portfolio_data.get('risk_metrics', {}))
            
            # Ensure all values are properly formatted
            self.risk_metrics = convert_keys_to_strings(self.risk_metrics)

    def compare_with_benchmark(self):
        benchmarks = {
            "000300.SH": "沪深300", "000905.SH": "中证500", "399006.SZ": "创业板",
            "HSI": "恒生指数", "SPX": "标普500指数", "IXIC": "纳斯达克指数",
            "GDAXI": "德国DAX指数", "N225": "日经225指数", "KS11": "韩国综合指数",
            "AS51": "澳大利亚标普200指数", "SENSEX": "印度孟买SENSEX指数"
        }

        benchmark_nvs = {}
        start_date = self.portfolio_manager.start_date
        end_date = self.portfolio_manager.end_date

        with self.error_handling("compare_with_benchmark"):
            for symbol, name in benchmarks.items():
                df = self.portfolio_manager.data_loader.get_market_index(symbol, start_date, end_date)
                nv = df['Close'] / df['Close'].iloc[0]
                nv_resampled = nv.resample('D').ffill()
                
                symbol_map = {'000300.SH': 'HS300', '000905.SH': 'ZZ500', '399006.SZ': 'CYB'}
                benchmark_nvs[symbol_map.get(symbol, symbol)] = nv_resampled

            nv_df = pd.DataFrame(benchmark_nvs, index=pd.date_range(start_date, end_date))
            nv_df.ffill(inplace=True)

            # Add 15% annual return benchmark
            annual_return = 0.15
            daily_return = (1 + annual_return) ** (1/365) - 1
            cumulative_return = [1] + [(1 + daily_return) ** i for i in range(1, len(nv_df.index))]
            nv_df['15pct_annual_return'] = cumulative_return

            self.benchmark_index_records = nv_df.join(self.net_value_records, how='right').resample('D').ffill()

    def update_portfolio_status(self):
        with self.error_handling("update_portfolio_status"):
            if self.net_value_records is None or self.net_value_records.empty:
                self.portfolio_status = pd.DataFrame(columns=COLUMN_DEFINITIONS['portfolio_status'])
                return

            latest_date = self.net_value_records.index[-1]
            running_days = (latest_date - self.net_value_records.index[0]).days

            # Core metrics come from RiskMetricsAnalyzer, while supplementary metrics are added separately
            self.portfolio_status = pd.DataFrame({
                # Basic portfolio values
                'net_value': [self.net_value_records.iloc[-1]['net_value']],
                'fund_value': [self.portfolio_data['fund_value']],
                'running_days': [running_days],
                'inception_date': [self.net_value_records.index[0]],
                
                # Core metrics from RiskMetricsAnalyzer
                'cagr': [self.risk_metrics.get('cagr', 0)],
                'current_drawdown': [self.risk_metrics.get('current_drawdown', 0)],
                'max_drawdown': [self.risk_metrics.get('max_drawdown', 0)],
                'max_drawdown_duration_days': [self.risk_metrics.get('max_drawdown_duration', 0)],
                'total_trades': [self.risk_metrics.get('total_trades', 0)],
                'profit_trades': [self.risk_metrics.get('won_trades', 0)],
                'loss_trades': [self.risk_metrics.get('lost_trades', 0)],
                'win_rate': [self.risk_metrics.get('win_rate', 0)],
                'profit_loss_ratio': [self.risk_metrics.get('profit_loss_ratio', 0)],
                'annual_returns': [json.dumps(self.risk_metrics.get('annual_returns', {}), default=json_serial)],
                'calmar': [self.risk_metrics.get('calmar', 0)],
                
                # Supplementary metrics from other analyzers
                'sharpe_ratio': [self.risk_metrics.get('sharpe_ratio', 0)],
                'sqn': [self.risk_metrics.get('sqn', 0)],
                'vwr': [self.risk_metrics.get('vwr', 0)],
                'xirr': [self.risk_metrics.get('xirr', 0)],
                
                # Legacy field kept for backward compatibility
                'annual_return': [self.risk_metrics.get('cagr', 0)]  # Using CAGR as annual return
            }, index=[latest_date])

    def _persist_dataframe(self, df: pd.DataFrame, table_name: str, db_name: str):
        """Helper method to persist a single DataFrame"""
        if df is None or df.empty or df.isnull().all().all():
            module_logger.info(f"Creating empty table for {table_name}")
            # Create empty table with schema even when no data
            columns = COLUMN_DEFINITIONS[table_name]
            persist_dataframe_to_sqlite(None, db_name, table_name, columns)
            return
            
        df_copy = df.copy()
        if df_copy.index.name in ['date', 'year', 'symbol']:
            df_copy = df_copy.reset_index()
        
        if 'date' in df_copy.columns:
            df_copy = self._ensure_date_format(df_copy)
            
        if table_name == 'portfolio_status' and 'inception_date' in df_copy.columns:
            df_copy['inception_date'] = pd.to_datetime(df_copy['inception_date']).dt.strftime('%Y-%m-%d')
            df_copy = df_copy.iloc[-1:].copy()
        
        module_logger.info(f'--- {table_name} DataFrame to persist ---')
        module_logger.info(f'Shape: {df_copy.shape}')
        module_logger.info(df_copy)
        module_logger.info('-' * (len(table_name) + 28))
        
        persist_dataframe_to_sqlite(df_copy, db_name, table_name, COLUMN_DEFINITIONS[table_name])

    def persist_to_db(self):
        """Persist data to SQLite database"""
        with self.error_handling("persist_to_db"):
            attribute_table_map = {
                'net_value_records': 'net_values',
                'trade_records': 'trade_records',
                'position_records': 'position_records',
                'capital_records': 'capital_records',
                'benchmark_index_records': 'benchmark_index',
                'portfolio_status': 'portfolio_status',
                'equal_weight_benchmark': 'equal_weight_benchmark',
                'return_attribution_annual': 'return_attribution_annual',
                'return_attribution_assets': 'return_attribution_assets'
            }
            
            db_name = self.portfolio_manager.portfolio_db_name
            for attr, table_name in attribute_table_map.items():
                self._persist_dataframe(getattr(self, attr, None), table_name, db_name)
