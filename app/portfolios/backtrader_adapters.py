"""
Backtrader adapters for portfolio management.

This module provides adapter classes for integrating with the Backtrader framework,
including strategy and capital sizer adapters.
"""

from typing import Any, Dict, List, Optional
import datetime
import logging
import backtrader as bt

from brokers.custom_broker import CustomBroker
from trade_strategies import TradeSignalState, convert_numeric_signal_to_string
from .constants import PORTFOLIO_DB_COLUMN_DEFINITIONS, PORTFOLIO_CASH_VALUE_COLUMNS

module_logger = logging.getLogger(__name__)

class BacktraderStrategyAdapter(bt.Strategy):
    """
    Adapter to convert a trading strategy into a Backtrader strategy.
    
    This adapter implements the Backtrader Strategy interface while supporting:
    - Regular and fixed investment strategies
    - Position tracking
    - Trade recording
    - Capital management
    
    The adapter prevents duplicate trades and manages position limits based on
    the capital strategy configuration.
    """
    
    def __init__(self) -> None:
        """Initialize the strategy adapter with empty tracking collections."""
        super().__init__()
        # Dictionary to store position records by date
        self.positions_records: Dict[datetime.date, Dict[str, Dict[str, Any]]] = {}
        # Dictionary to store trade records by date
        self.trade_records: Dict[datetime.date, Dict[str, Dict[str, Any]]] = {}
        # List to store capital changes
        self.capital_records: List[Dict[str, Any]] = []
        # List to store cash and value records
        self.cash_value_records: List[Dict[str, Any]] = []
        # Dictionary to track current orders by data source
        self.order: Dict[str, Optional[bt.Order]] = {}
        # Dictionary to track last sell date by data source
        self.last_sell_date: Dict[str, datetime.date] = {}
        
    def start(self) -> None:
        """
        Initialize the strategy when trading starts.
        
        Raises:
            ValueError: If not using CustomBroker or missing required configuration
        """
        if not isinstance(self.broker, CustomBroker):
            raise ValueError("This strategy requires the CustomBroker")
        
        if hasattr(self, 'sizer') and hasattr(self.sizer, 'params'):
            capital_strategy = self.sizer.params.capital_strategy
            if capital_strategy.is_fixed_investment():
                module_logger.info("Setting broker strategy for fixed investment strategy")
                self.broker.strategy = self

    def stop(self) -> None:
        """Finalize strategy execution by merging broker and strategy capital records."""
        broker_capital_records = self.broker.get_capital_records()
        all_records = broker_capital_records + self.capital_records
        self.capital_records = sorted(all_records, key=lambda x: x['date'])

    def log(self, txt: str, dt: Optional[datetime.date] = None) -> None:
        """
        Log strategy actions with timestamp.
        
        Args:
            txt: Message to log
            dt: Date for the log entry, defaults to current date
        """
        dt = dt or self.datas[0].datetime.date(0)
        module_logger.info('%s, %s' % (dt.isoformat(), txt))

    def _record_current_state(self, current_date: datetime.date) -> None:
        """
        Record current portfolio state including cash and value.
        
        Args:
            current_date: Current trading date
        """
        self.log('Cash: {:.2f}, Value: {:.2f}'.format(
            self.broker.getcash(),
            self.broker.getvalue()
        ))
        
        self.cash_value_records.append(dict(zip(
            PORTFOLIO_CASH_VALUE_COLUMNS,
            [current_date, self.broker.getcash(), self.broker.getvalue()]
        )))
        
        # Initialize position records for current date
        self.positions_records[current_date] = {}
        
    def _process_cash_flows(self, current_date: datetime.date) -> None:
        """Process any cash flows in a uniform way, regardless of strategy type.

        This method provides a unified interface for all cash flow operations,
        supporting both legacy strategies and new unified cash flow management.

        Args:
            current_date: Current trading date
        """
        if not hasattr(self, 'sizer') or not hasattr(self.sizer, 'params'):
            return

        capital_strategy = self.sizer.params.capital_strategy
        if not capital_strategy:
            return

        # Method 1: Try new unified cash flow interface (preferred)
        cash_flows = self._get_unified_cash_flows(capital_strategy, current_date)
        if cash_flows:
            for flow_type, amount in cash_flows.items():
                if amount != 0:
                    self._handle_cash_flow(amount, flow_type, current_date)
            return

        # Method 2: Try legacy specific methods (for backward compatibility)
        self._process_legacy_cash_flows(capital_strategy, current_date)

    def _get_unified_cash_flows(self, capital_strategy, current_date: datetime.date) -> dict:
        """Get cash flows using the new unified interface.

        Args:
            capital_strategy: The capital strategy instance
            current_date: Current trading date

        Returns:
            dict: Dictionary of {flow_type: amount} or empty dict if not supported
        """
        # Try the new unified interface
        if hasattr(capital_strategy, 'get_cash_flows'):
            try:
                cash_flows = capital_strategy.get_cash_flows(
                    current_date, self.broker.get_cash(), self.broker.getvalue()
                )
                if isinstance(cash_flows, dict):
                    return cash_flows
            except Exception as e:
                self.log(f"Error getting unified cash flows: {e}")

        return {}

    def _process_legacy_cash_flows(self, capital_strategy, current_date: datetime.date) -> None:
        """Process cash flows using legacy methods for backward compatibility.

        Args:
            capital_strategy: The capital strategy instance
            current_date: Current trading date
        """
        # Legacy annual investment
        if hasattr(capital_strategy, 'invest_conditionally'):
            try:
                investment_amount = capital_strategy.invest_conditionally(
                    current_date, self.broker.get_cash(), self.broker.getvalue()
                )
                if investment_amount > 0:
                    self._handle_cash_flow(investment_amount, 'annual_investment', current_date)
            except Exception as e:
                self.log(f"Error processing annual investment: {e}")

        # Legacy monthly investment
        if hasattr(capital_strategy, 'invest_monthly'):
            try:
                investment_amount = capital_strategy.invest_monthly(
                    current_date, self.broker.get_cash(), self.broker.getvalue()
                )
                if investment_amount > 0:
                    self._handle_cash_flow(investment_amount, 'monthly_investment', current_date)
            except Exception as e:
                self.log(f"Error processing monthly investment: {e}")

    def _handle_cash_flow(self, amount: float, flow_type: str, current_date: datetime.date) -> None:
        """Handle a single cash flow operation.

        Args:
            amount: Cash flow amount (positive for inflow, negative for outflow)
            flow_type: Type of cash flow (e.g., 'annual_investment', 'monthly_investment', etc.)
            current_date: Current trading date
        """
        if amount == 0:
            return

        # Record the cash flow in broker
        if hasattr(self.broker, 'add_cash'):
            self.broker.add_cash(amount, current_date, change_type=flow_type)

        # Notify the portfolio manager about the cash flow (unified interface)
        if hasattr(self, 'portfolio_manager'):
            try:
                if amount > 0:
                    self.portfolio_manager.deposit_cash(amount)
                else:
                    self.portfolio_manager.withdraw_cash(-amount)
            except Exception as e:
                self.log(f"Error notifying portfolio manager about cash flow: {e}")

        # Log the operation
        direction = "inflow" if amount > 0 else "outflow"
        self.log(f"Processed cash {direction}: {abs(amount):.2f}, type: {flow_type}, date: {current_date}")


    def _process_sell_signals(self, current_date: datetime.date) -> None:
        """
        Process sell signals for all data feeds.
        
        Args:
            current_date: Current trading date
        """
        for data in self.datas:
            signal = convert_numeric_signal_to_string(data.signal[0])
            self.log('{}: {}, {}'.format(data._name, data.close[0], signal))
            
            if signal == TradeSignalState.SELL.value:
                self._handle_sell_signal(data, current_date)

    def _handle_sell_signal(self, data: bt.feeds.DataBase, current_date: datetime.date) -> None:
        """
        Handle a sell signal for a specific data feed.
        
        Args:
            data: Data feed generating the sell signal
            current_date: Current trading date
        """
        position = self.getposition(data)
        if position.size > 0:
            if data._name not in self.last_sell_date or self.last_sell_date[data._name] != current_date:
                self.log('The position for {} is sold'.format(data._name))
                if data._name not in self.order or not self.order[data._name]:
                    self.order[data._name] = self.close(data=data)
                    self.last_sell_date[data._name] = current_date
            else:
                self.log('Already sold {} today, skipping duplicate sell signal'.format(data._name))
        else:
            self.log('No position to sell for {}'.format(data._name))

    def _process_buy_signals(self, current_date: datetime.date) -> None:
        """
        Process buy signals for all data feeds.
        
        Args:
            current_date: Current trading date
        """
        buy_signals = [
            data for data in self.datas
            if (convert_numeric_signal_to_string(data.signal[0]) == TradeSignalState.BUY.value and
                (data._name not in self.order or not self.order[data._name]))
        ]

        if not buy_signals:
            return

        capital_strategy = self.sizer.params.capital_strategy
                
        # Apply position limits
        max_positions = capital_strategy.get_max_positions()
        if max_positions is not None:
            current_positions = sum(1 for data in self.datas if self.getposition(data).size > 0)
            available_slots = max(0, max_positions - current_positions)
            
            self.log(f"Current positions: {current_positions}, Max allowed: {max_positions}, Available slots: {available_slots}")
            
            if available_slots <= 0:
                self.log("Maximum position limit reached, no new positions will be added")
                return
                
            if len(buy_signals) > available_slots:
                buy_signals = buy_signals[:available_slots]
                self.log(f"Limited buy signals to available slots: {available_slots}")

        self._execute_buy_signals(buy_signals)

    def _execute_buy_signals(self, buy_signals: List[bt.feeds.DataBase]) -> None:
        """
        Execute buy orders for the given signals.
        
        Args:
            buy_signals: List of data feeds with buy signals
        """
        min_cash_required = 1000
        capital_strategy = self.sizer.params.capital_strategy
        
        for data in buy_signals:
            current_cash = self.broker.getcash()
            if current_cash < min_cash_required:
                self.log(f'Insufficient funds ({current_cash:.2f}) to process more buy signals')
                break
            
            total_value = self.broker.getvalue()
            self.log('The buy signal for {} is received'.format(data._name))
            
            # Get allocated capital and calculate shares
            allocated_capital = capital_strategy.allocate_capital(current_cash, total_value, data, self.broker)
            allocated_capital = min(allocated_capital, current_cash)
            
            if allocated_capital < min_cash_required:
                self.log(f"Allocated capital too small for {data._name}, skipping. Min cash required: {min_cash_required}")
                continue
            
            self._place_buy_order(data, allocated_capital)

    def _place_buy_order(self, data: bt.feeds.DataBase, allocated_capital: float) -> None:
        """
        Place a buy order with proper position sizing.
        
        Args:
            data: Data feed to buy
            allocated_capital: Capital allocated for this purchase
        """
        current_price = data.close[0]
        commission_rate = self.broker.getcommissioninfo(data).p.commission
        
        # Calculate shares with safety margins
        max_shares = allocated_capital / (current_price * 1.05 * (1 + commission_rate))
        shares = int(max_shares * 0.95)
        
        if shares > 0:
            buffer_price = current_price * 1.05
            self.log(f'Try to buying {shares} shares of {data._name} at max {buffer_price:.2f}, '
                    f'total max cost: {shares * buffer_price:.2f}, allocated capital: {allocated_capital:.2f}')
            
            self.order[data._name] = self.buy(
                data=data,
                size=shares,
                price=buffer_price,
                exectype=bt.Order.Limit
            )
        else:
            self.log(f'Calculated shares for {data._name} is zero, skipping')

    def next(self) -> None:
        """Execute one step of the strategy."""
        current_date = self.datas[0].datetime.date(0)

        # Record current portfolio state
        self._record_current_state(current_date)

        # Process any cash flows (strategy-agnostic)
        self._process_cash_flows(current_date)

        # Process sell signals first to free up capital
        self._process_sell_signals(current_date)

        # Then process buy signals
        self._process_buy_signals(current_date)

        # Update position records
        self._update_positions_records(current_date)

    def _update_positions_records(self, current_date: datetime.date) -> None:
        """
        Update position records for the current date.
        
        Args:
            current_date: Current trading date
        """
        for data in self.datas:
            position = self.getposition(data)
            if position.size != 0:
                self.positions_records[current_date][data._name] = dict(zip(
                    PORTFOLIO_DB_COLUMN_DEFINITIONS['position_records'],
                    [current_date, data._name, data._name, position.size,
                     data.close[0], self.broker.get_value([data])]
                ))

    def notify_order(self, order: bt.Order) -> None:
        """
        Process order notifications from the broker.
        
        Args:
            order: Order being notified about
        """
        if order.status in [order.Submitted, order.Accepted]:
            return

        if order.status in [order.Completed]:
            self._handle_completed_order(order)
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            status_map = {
                order.Canceled: 'Canceled',
                order.Margin: 'Margin',
                order.Rejected: 'Rejected'
            }
            self.log('Order for {} is not executed: {}. Current Cash: {}'.format(
                order.data._name,
                status_map[order.status],
                self.broker.getcash()
            ))
        
        self.order[order.data._name] = None

    def _handle_completed_order(self, order: bt.Order) -> None:
        """
        Handle a completed order by updating records.
        
        Args:
            order: Completed order to process
        """
        current_date = self.datas[0].datetime.date(0)

        action = 'BUY' if order.isbuy() else 'SELL'
        self.log('{} EXECUTED, Stock: {}, Price: {:.2f}, Cost: {:.2f}, Comm {:.2f}, Size: {}'.format(
            action,
            order.data._name,
            order.executed.price,
            order.executed.value,
            order.executed.comm,
            order.executed.size
        ))
                
        trade_record = dict(zip(
            PORTFOLIO_DB_COLUMN_DEFINITIONS['trade_records'],
            [current_date, order.data._name, order.data._name,
             'buy' if order.isbuy() else 'sell', order.executed.size,
             order.executed.price, order.executed.price * order.executed.size]
        ))
        
        if current_date not in self.trade_records:
            self.trade_records[current_date] = {}
        self.trade_records[current_date][order.data._name] = trade_record

        self._record_capital_change(current_date, order, trade_record)

    def _record_capital_change(self, current_date: datetime.date, order: bt.Order,
                             trade_record: Dict[str, Any]) -> None:
        """
        Record changes in capital after a trade.
        
        Args:
            current_date: Current trading date
            order: Completed order
            trade_record: Trade record dictionary
        """
        capital_change = dict(zip(
            PORTFOLIO_DB_COLUMN_DEFINITIONS['capital_records'],
            [current_date, trade_record['type'],
             order.executed.value if order.isbuy() else -order.executed.value,
             self.broker.getcash(), self.broker.getvalue()]
        ))
        self.capital_records.append(capital_change)

    def notify_trade(self, trade: bt.Trade) -> None:
        """
        Process trade notifications from the broker.
        
        Args:
            trade: Trade being notified about
        """
        if not trade.isclosed:
            self.log('TRADE OPEN, Name: {}, Price: {:.2f}, Size: {}'.format(
                trade.data._name,
                trade.price,
                trade.size))
            return
        
        self.log('TRADE CLOSED, Name: {}, Gross Profit: {:.2f}, Net Profit: {:.2f}'.format(
                trade.data._name,
                trade.pnl,
                trade.pnlcomm))
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Get all position records.
        
        Returns:
            List[Dict[str, Any]]: List of all position records
        """
        positions_list = []
        for positions in self.positions_records.values():
            positions_list.extend(positions.values())
        return positions_list
    
    def get_trades(self) -> List[Dict[str, Any]]:
        """
        Get all trade records.
        
        Returns:
            List[Dict[str, Any]]: List of all trade records
        """
        trades_list = []
        for trades in self.trade_records.values():
            trades_list.extend(trades.values())
        return trades_list
    
    def get_capital(self) -> List[Dict[str, Any]]:
        """
        Get all capital records.
        
        Returns:
            List[Dict[str, Any]]: List of all capital change records
        """
        return self.capital_records
    
    def get_cash_value(self) -> List[Dict[str, Any]]:
        """
        Get all cash and value records.
        
        Returns:
            List[Dict[str, Any]]: List of all cash and value records
        """
        return self.cash_value_records


class CapitalStrategySizerAdapter(bt.Sizer):
    """
    Adapter to convert capital allocation strategies into Backtrader sizers.
    
    This adapter implements Backtrader's Sizer interface while delegating actual
    position sizing logic to the capital strategy.
    
    Attributes:
        params (tuple): Sizer parameters including capital strategy
    """
    
    params = (('capital_strategy', None),)

    def __init__(self) -> None:
        """
        Initialize the sizer adapter.
        
        Raises:
            ValueError: If no capital strategy is provided
        """
        super().__init__()
        if self.params.capital_strategy is None:
            raise ValueError("Capital strategy must be provided")

    def _getsizing(self, comminfo: bt.CommissionInfo, cash: float,
                   data: bt.feeds.DataBase, isbuy: bool) -> int:
        """
        Calculate the number of shares to trade.
        
        Real position sizing is handled in the strategy's next method.
        This method only returns the full position size for selling,
        or 1 for buying (actual size calculated later).
        
        Args:
            comminfo: Commission information
            cash: Available cash
            data: Data feed being traded
            isbuy: Whether this is a buy order
            
        Returns:
            int: Number of shares to trade
        """
        if not isbuy:
            position = self.broker.getposition(data)
            return position.size
            
        return 1  # Buy sizing is handled in next() method
