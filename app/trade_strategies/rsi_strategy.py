import pandas as pd
from trade_strategies import BaseTradeStrategy, TradeSignalState

class RsiStrategy(BaseTradeStrategy):
    """Relative Strength Index (RSI) Trading Strategy"""

    # 默认参数
    params = {
        'rsi_period': 14,
        'rsi_overbought': 70,
        'rsi_oversold': 30,
    }

    def __init__(self, params=None):
        super().__init__()
        if params:
            self.params.update(params)

    def set_data(self, symbol, data):
        self.symbol = symbol
        self.data = data
        self.calculate_indicators()

    def calculate_indicators(self):
        delta = self.data['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.params['rsi_period'], min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.params['rsi_period'], min_periods=1).mean()

        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))

    def is_buy_signal(self, rsi):
        return rsi < self.params['rsi_oversold']

    def is_sell_signal(self, rsi):
        return rsi > self.params['rsi_overbought']

    def generate_signals(self):
        signals = [TradeSignalState.EMPTY.value for _ in range(len(self.data))]

        for i in range(1, len(self.data)):
            if self.is_buy_signal(self.data['rsi'].iloc[i]):
                signals[i] = TradeSignalState.BUY.value
            elif self.is_sell_signal(self.data['rsi'].iloc[i]):
                signals[i] = TradeSignalState.SELL.value
            else:
                signals[i] = TradeSignalState.EMPTY.value

        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'rsi': self.data['rsi'],
            'signal': signals
        })

        return signal_df
