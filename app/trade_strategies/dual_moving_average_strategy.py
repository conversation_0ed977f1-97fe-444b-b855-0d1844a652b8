import pandas as pd
from trade_strategies import BaseTradeStrategy, TradeSignalState

class DualMovingAverageStrategy(BaseTradeStrategy):
    """Dual Moving Average Trading Strategy"""

    # 默认参数
    params = {
        'short_window': 11,
        'long_window': 22,
    }

    def __init__(self, params=None):
        super().__init__()
        if params:
            self.params.update(params)

    def set_data(self, symbol, data):
        self.symbol = symbol
        self.data = data
        self.calculate_indicators()

    def calculate_indicators(self):
        self.data['short_ma'] = self.data['Close'].rolling(window=self.params['short_window'], min_periods=1).mean()
        self.data['long_ma'] = self.data['Close'].rolling(window=self.params['long_window'], min_periods=1).mean()

    def is_buy_signal(self, short_ma, long_ma):
        return short_ma > long_ma
    
    def is_sell_signal(self, short_ma, long_ma):
        return short_ma < long_ma

    def generate_signals(self):
        signals = [TradeSignalState.EMPTY.value for _ in range(len(self.data))]
        
        last_state = TradeSignalState.EMPTY.value
        for i in range(1, len(self.data)):
            if self.is_buy_signal(self.data['short_ma'].iloc[i], self.data['long_ma'].iloc[i]):
                if last_state in [TradeSignalState.EMPTY.value, TradeSignalState.SELL.value]:
                    signals[i] = TradeSignalState.BUY.value
                    last_state = TradeSignalState.HOLD.value
                elif last_state == TradeSignalState.HOLD.value:
                    signals[i] = TradeSignalState.HOLD.value
            elif self.is_sell_signal(self.data['short_ma'].iloc[i], self.data['long_ma'].iloc[i]):
                if last_state in [TradeSignalState.HOLD.value, TradeSignalState.BUY.value]:
                    signals[i] = TradeSignalState.SELL.value
                    last_state = TradeSignalState.EMPTY.value
                else:
                    signals[i] = TradeSignalState.EMPTY.value
            else:
                signals[i] = last_state

        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'short_ma': self.data['short_ma'],
            'long_ma': self.data['long_ma'],
            'signal': signals
        })

        return signal_df
