import pandas as pd
from trade_strategies import BaseTradeStrategy, TradeSignalState

class BuyHoldStrategy(BaseTradeStrategy):
    """买入持有策略"""

    def __init__(self, params=None):
        super().__init__()

    def set_data(self, symbol, data):
        self.symbol = symbol
        self.data = data

    def generate_signals(self):
        # 初始化信号数组，初始状态为HOLD
        signals = [TradeSignalState.BUY.value for _ in range(len(self.data))]

        # 创建信号DataFrame
        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'signal': signals
        })

        return signal_df
