import pandas as pd
from trade_strategies import BaseTradeStrategy, TradeSignalState

class TrippleMovingAverageStrategy(BaseTradeStrategy):
    """Tripple Moving Average Trading Strategy"""

    # 默认参数
    params = {
        'm1': 15,
        'm2': 20,
        'm3': 50,
    }

    def __init__(self, params=None):
        super().__init__()
        if params:
            self.params.update(params)

    def set_data(self, symbol, data):
        self.symbol = symbol
        self.data = data
        self.calculate_indicators()

    def calculate_indicators(self):
        self.data['m1'] = self.data['Close'].rolling(window=self.params['m1'], min_periods=1).mean()
        self.data['m2'] = self.data['Close'].rolling(window=self.params['m2'], min_periods=1).mean()
        self.data['m3'] = self.data['Close'].rolling(window=self.params['m3'], min_periods=1).mean()

    def is_buy_signal(self, m1, m2, m3, close):
        return (m1 > m2) and (close > m3)
    
    def is_sell_signal(self, m1, m2, m3, close):
        return (m1 < m2) and (close < m3)

    def generate_signals(self):
        signals = [TradeSignalState.EMPTY.value for _ in range(len(self.data))]
        
        last_state = TradeSignalState.EMPTY.value
        for i in range(1, len(self.data)):
            if self.is_buy_signal(self.data['m1'].iloc[i], self.data['m2'].iloc[i], self.data['m3'].iloc[i], self.data['Close'].iloc[i]):
                if last_state in [TradeSignalState.EMPTY.value, TradeSignalState.SELL.value]:
                    signals[i] = TradeSignalState.BUY.value
                    last_state = TradeSignalState.HOLD.value
                elif last_state == TradeSignalState.HOLD.value:
                    signals[i] = TradeSignalState.HOLD.value
            elif self.is_sell_signal(self.data['m1'].iloc[i], self.data['m2'].iloc[i], self.data['m3'].iloc[i], self.data['Close'].iloc[i]):
                if last_state in [TradeSignalState.HOLD.value, TradeSignalState.BUY.value]:
                    signals[i] = TradeSignalState.SELL.value
                    last_state = TradeSignalState.EMPTY.value
                else:
                    signals[i] = TradeSignalState.EMPTY.value
            else:
                signals[i] = last_state

        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'm1': self.data['m1'],
            'm2': self.data['m2'],
            'm3': self.data['m3'],
            'signal': signals
        })

        return signal_df
