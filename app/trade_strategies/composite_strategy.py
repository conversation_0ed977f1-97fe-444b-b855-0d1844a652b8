"""
Composite Strategy implementation using primitive components.

This module provides a dynamic strategy implementation that uses primitive components
(indicators and signals) to generate trading signals based on a configuration.
"""

import pandas as pd
from typing import Dict, Any, List

from trade_strategies import BaseTradeStrategy, TradeSignalState
from components.registry import ComponentRegistry
from components.signal_evaluator import SignalEvaluator

class CompositeStrategy(BaseTradeStrategy):
    """
    Dynamically configurable strategy using primitive components.
    
    This strategy uses SignalEvaluator to evaluate a tree of indicators and signals
    defined in a configuration object to generate trading signals.
    
    Attributes:
        indicator_instances (Dict): Dictionary of indicator instances by ID
        signals_config (Dict): Dictionary of signal configurations
        outputs (Dict): Mapping of signal output names to signal IDs
        needs_global_data (bool): Whether this strategy instance requires a global view of all symbol data. 
                                  This is typically set by the factory based on signal types.
    """
    
    # 恢复类级别默认值，PortfolioFactory会根据需要覆盖它
    needs_global_data: bool = False
    
    def __init__(self, indicator_instances: Dict, signals_config: Dict, outputs: Dict, signal_evaluator=None):
        """
        Initialize the composite strategy.
        
        Args:
            indicator_instances: Dictionary of indicator instances by ID
            signals_config: Dictionary of signal configurations
            outputs: Mapping of signal output names to signal IDs
            signal_evaluator: Optional custom SignalEvaluator instance
                             (if not provided, a new one will be created)
        """
        super().__init__()
        self.indicator_instances = indicator_instances
        self.signals_config = signals_config
        self.outputs = outputs
        self._registry = ComponentRegistry.get_instance()
        # self.needs_global_data 由工厂设置，此处不再从参数初始化
        
        if signal_evaluator is not None:
            self._signal_evaluator = signal_evaluator
        else:
            self._signal_evaluator = SignalEvaluator(self._registry)
            
        self.all_symbol_data = {}
        self.all_symbols = []
    
    def set_global_data(self, all_symbol_data: Dict[str, pd.DataFrame]):
        """
        为需要全局视角的策略设置所有symbol的数据
        
        Args:
            all_symbol_data: 所有交易标的的历史OHLC数据，格式为 {symbol: dataframe}
        """
        self.all_symbol_data = all_symbol_data
        self.all_symbols = list(all_symbol_data.keys())
    
    def generate_all_signals(self) -> Dict[str, pd.DataFrame]:
        """
        一次性为所有symbol生成信号(仅适用于needs_global_data=True的策略)
        
        Returns:
            Dict[str, pd.DataFrame]: 所有交易标的的信号DataFrame字典
        """
        if not self.needs_global_data:
            # 如果策略不需要全局数据，但被调用此方法，则返回空字典或记录警告
            # module_logger.warning("generate_all_signals called on a strategy not requiring global data.")
            return {}
            
        result = {}
        # 确保 self.all_symbols 已经被 set_global_data 正确设置
        if not self.all_symbols and self.all_symbol_data:
             self.all_symbols = list(self.all_symbol_data.keys())

        for symbol in self.all_symbols:
            if symbol in self.all_symbol_data:
                self.set_data(symbol, self.all_symbol_data[symbol])
                signal_df = self.generate_signals()
                if not signal_df.empty:
                    result[symbol] = signal_df
        
        return result
        
    def set_data(self, symbol, data):
        """
        Set the data for the strategy and clear any cached values.
        
        Args:
            symbol: Trading symbol
            data: OHLCV price data
        """
        self.symbol = symbol
        self.data = data
    
    def generate_signals(self):
        """
        Generate trading signals using the configured primitive components.
        
        The method uses SignalEvaluator to evaluate the signal tree and convert
        boolean signals to trade states (BUY, SELL, HOLD, EMPTY).
        
        Returns:
            pd.DataFrame: DataFrame containing the symbol, price data, and signals
        """
        # 构建评估器需要的配置格式
        indicator_configs = []
        for indicator_id, indicator_instance in self.indicator_instances.items():
            # 从实例中提取必要的信息构建配置
            indicator_configs.append({
                'id': indicator_id,
                'type': indicator_instance.__class__.__name__,
                'params': indicator_instance.params if hasattr(indicator_instance, 'params') else {}
            })
        
        # 准备给 SignalEvaluator 的配置
        config = {
            'indicators': indicator_configs,
            'signals': list(self.signals_config.values()),  # 从字典转为列表
            'outputs': self.outputs
        }
        
        # 确保将市场指标管理器传递给 SignalEvaluator
        if hasattr(self, '_market_indicator_manager'):
            self._signal_evaluator._market_indicator_manager = self._market_indicator_manager
            
        # 使用 SignalEvaluator 生成交易信号
        signals = self._signal_evaluator.evaluate_for_symbol(self.symbol, config, self.data)
        
        # 创建基础 DataFrame
        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'signal': signals
        })
        
        # 从 outputs 配置中获取要输出的指标列表
        # 约定：只有配置了 outputs.indicators 时才输出指标值
        if 'indicators' in self.outputs and isinstance(self.outputs['indicators'], list):
            # 遍历配置的指标列表
            for indicator_config in self.outputs['indicators']:
                # 检查配置格式是否正确
                if not isinstance(indicator_config, dict) or 'id' not in indicator_config or 'output_name' not in indicator_config:
                    continue
                    
                indicator_id = indicator_config['id']
                output_name = indicator_config['output_name']
                
                # 从评估器的缓存结果中获取指标值
                if indicator_id in self._signal_evaluator._evaluated_components:
                    signal_df[output_name] = self._signal_evaluator._evaluated_components[indicator_id]
                    
        # 添加市场指标到输出
        if 'market_indicators' in self.outputs and hasattr(self._signal_evaluator, '_evaluated_market_indicators'):
            for market_config in self.outputs['market_indicators']:
                # 获取市场指标
                indicator_key = market_config['market']
                
                # 处理基础市场指标
                if 'field' in market_config:
                    field = market_config['field']
                    key = f"{indicator_key}_{field}"
                    output_name = market_config.get('output_name', key)
                    if key in self._signal_evaluator._evaluated_market_indicators:
                        signal_df[output_name] = self._signal_evaluator._evaluated_market_indicators[key]
                
                # 处理转换后的市场指标
                if 'transformer' in market_config:
                    transformer = market_config['transformer']
                    output_name = market_config.get('output_name', transformer)
                    if transformer in self._signal_evaluator._evaluated_market_indicators:
                        signal_df[output_name] = self._signal_evaluator._evaluated_market_indicators[transformer]
        
        return signal_df
