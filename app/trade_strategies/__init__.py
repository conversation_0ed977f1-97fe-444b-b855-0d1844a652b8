class BaseTradeStrategy:
    # 是否需要全局数据的标志
    needs_global_data = False
    
    def __init__(self):
        self.symbol = None
        self.data = None
        
    @classmethod
    def from_dsl(cls, dsl_script):
        # 实现从 DSL 加载策略的逻辑
        pass

    def set_data(self, symbol, data):
        """
        设定特定交易标的的历史OHLC数据
        """
        self.symbol = symbol
        self.data = data

    def set_global_data(self, all_symbol_data):
        """
        为需要全局视角的策略设置所有symbol的数据
        
        Args:
            all_symbol_data (Dict[str, pd.DataFrame]): 所有交易标的的历史OHLC数据，格式为 {symbol: dataframe}
        """
        pass

    def generate_signals(self):
        """
        生成特定交易标的的历史交易信号。
        交易信号应该是一个pandas DataFrame，包含以下列：['symbol', 'signal', 'date']，其中'signal'列应该是'buy', 'sell'或'hold'
        """
        raise NotImplementedError("Must implement generate_signals method!")
    
    def generate_all_signals(self):
        """
        一次性为所有symbol生成信号(仅适用于needs_global_data=True的策略)
        
        Returns:
            Dict[str, pd.DataFrame]: 所有交易标的的信号DataFrame字典，格式为 {symbol: signal_dataframe}
        """
        return {}

from enum import Enum
    
class TradeSignalState(Enum):
    BUY = 'B' # buy
    SELL = 'S' # sell
    HOLD = 'H' # hold
    EMPTY = 'E' # empty
    ERROR = 'X' # error

def convert_signals_to_numeric(dataframe):
    signal_mapping = {
        TradeSignalState.BUY.value: 1, 
        TradeSignalState.SELL.value: 2, 
        TradeSignalState.HOLD.value: 3,
        TradeSignalState.EMPTY.value: 4,
        TradeSignalState.ERROR.value: 5
        }
    dataframe['signal'] = dataframe['signal'].map(signal_mapping).fillna(4)  # Default to 'E' for NaNs
    return dataframe

def convert_numeric_signals_to_string(dataframe):
    signal_mapping = {
        1: TradeSignalState.BUY.value, 
        2: TradeSignalState.SELL.value, 
        3: TradeSignalState.HOLD.value,
        4: TradeSignalState.EMPTY.value,
        5: TradeSignalState.ERROR.value
        }
    dataframe['signal'] = dataframe['signal'].map(signal_mapping).fillna(TradeSignalState.ERROR.value)  # Default to 'X' for NaNs
    return dataframe

def convert_numeric_signal_to_string(signal_numeric):
    signal_mapping = {
        1: TradeSignalState.BUY.value, 
        2: TradeSignalState.SELL.value, 
        3: TradeSignalState.HOLD.value,
        4: TradeSignalState.EMPTY.value,
        5: TradeSignalState.ERROR.value
        }
    return signal_mapping.get(signal_numeric, TradeSignalState.ERROR.value)
