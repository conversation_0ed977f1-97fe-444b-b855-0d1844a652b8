"""
Momentum Rotation Strategy implementation.

This module provides a strategy that rotates between multiple ETFs based on momentum,
selecting the ETF with the highest momentum for investment.
"""

import logging
import pandas as pd
import numpy as np
import traceback
from typing import Dict

from trade_strategies import BaseTradeStrategy, TradeSignalState

module_logger = logging.getLogger(__name__)

class MomentumRotationStrategy(BaseTradeStrategy):
    """
    ETF Momentum Rotation Strategy.

    This strategy calculates momentum for all ETFs in the portfolio and rotates to the one
    with the highest momentum. Only one ETF is held at a time.

    Attributes:
        params (Dict): Strategy parameters including:
            - momentum_period (int): Period for momentum calculation, default 21 days
        portfolio_data (Dict): Dictionary to store price data for each ETF
        all_symbols_in_strategy (List[str]): List of all symbols used in the strategy
    """

    # Flag to indicate this strategy needs global data
    needs_global_data = True

    # Default parameters
    params = {
        'momentum_period': 21
    }

    def __init__(self, params=None):
        """
        Initialize the momentum rotation strategy.

        Args:
            params (Dict, optional): Parameters to override defaults
        """
        super().__init__()
        if params:
            self.params.update(params)

        # Initialize data storage
        self.portfolio_data = {}
        self.all_symbols_in_strategy = []
        module_logger.info(f"Initialized MomentumRotationStrategy")

    def set_global_data(self, all_symbol_data: Dict[str, pd.DataFrame]):
        """
        Set historical OHLCV data for all symbols at once.

        Args:
            all_symbol_data (Dict[str, pd.DataFrame]): Dictionary mapping symbols to their OHLCV data
        """
        # Store all available symbols data
        self.portfolio_data = all_symbol_data
        self.all_symbols_in_strategy = list(all_symbol_data.keys())
        
        module_logger.info(f"Global data set for {len(self.all_symbols_in_strategy)} symbols: {self.all_symbols_in_strategy}")

    def _calculate_momentum(self, price_series: pd.Series, period: int) -> float:
        """
        Calculate momentum as percentage price change over the specified period.

        Args:
            price_series (pd.Series): Series of prices
            period (int): Lookback period for momentum calculation

        Returns:
            float: Momentum value as percentage change, or np.nan if calculation not possible
        """
        if len(price_series) < period + 1:
            return np.nan

        # Get current price and price 'period' days ago
        current_price = price_series.iloc[-1]
        past_price = price_series.iloc[-period-1]

        # Check for valid prices
        if pd.isna(current_price) or pd.isna(past_price) or past_price <= 0:
            return np.nan

        # Calculate momentum as percentage change
        momentum = ((current_price / past_price) - 1) * 100
        return momentum

    def generate_all_signals(self) -> Dict[str, pd.DataFrame]:
        """
        Generate trading signals for all symbols at once.
        
        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping each symbol to its signal DataFrame
        """
        try:
            signals_df = self.generate_signals()
            
            if signals_df.empty:
                return {}
            
            # Split the signals DataFrame by symbol
            result = {}
            for symbol in self.all_symbols_in_strategy:
                symbol_signals = signals_df[signals_df['symbol'] == symbol].copy()
                if not symbol_signals.empty:
                    # Keep only the columns needed for the signal DataFrame
                    columns_to_keep = ['symbol', 'open', 'high', 'low', 'close', 'volume', 'signal']
                    if 'momentum' in symbol_signals.columns:
                        columns_to_keep.append('momentum')
                    
                    symbol_signals = symbol_signals[columns_to_keep]
                    result[symbol] = symbol_signals
            
            return result
        except Exception as e:
            module_logger.error(f"Error in generate_all_signals: {str(e)}")
            module_logger.error(traceback.format_exc())
            return {}

    def generate_signals(self) -> pd.DataFrame:
        """
        Generate trading signals based on momentum rotation logic.

        The strategy:
        1. Calculates momentum for all ETFs
        2. Selects the ETF with highest momentum
        3. Generates BUY signal for the selected ETF
        4. Generates SELL signal for previously held ETF if different
        5. Maintains HOLD signal for currently held ETF if it remains the highest momentum

        Returns:
            pd.DataFrame: DataFrame with trading signals for all ETFs
        """
        try:
            # Validate data availability
            if not self.portfolio_data:
                module_logger.warning("No data available for any symbols in the strategy")
                return pd.DataFrame()

            # Check if we have data for all symbols
            missing_symbols = set(self.all_symbols_in_strategy) - set(self.portfolio_data.keys())
            if missing_symbols:
                module_logger.warning(f"Missing data for symbols: {missing_symbols}")

            # Get a reference date index from any available symbol
            if not self.portfolio_data:
                module_logger.error("No portfolio data available")
                return pd.DataFrame()

            reference_symbol = next(iter(self.portfolio_data))
            date_index = self.portfolio_data[reference_symbol].index

            # Initialize collection for all signals
            all_signals_list = []

            # Track currently held asset
            currently_held_asset = None

            # Process each date
            module_logger.info(f"Processing {len(date_index)} trading days")
            for i, current_date in enumerate(date_index):
                # Log progress periodically
                if i % 100 == 0:
                    module_logger.info(f"Processing date {i+1}/{len(date_index)}: {current_date}")

                # Calculate momentum for each ETF on this date
                daily_momentums = {}

                for symbol in self.all_symbols_in_strategy:
                    if symbol in self.portfolio_data:
                        try:
                            # Get price data up to current date
                            price_data = self.portfolio_data[symbol]['Close'].loc[:current_date]

                            # Calculate momentum if we have enough data
                            if len(price_data) > self.params['momentum_period']:
                                momentum = self._calculate_momentum(price_data, self.params['momentum_period'])
                                daily_momentums[symbol] = momentum
                        except Exception as e:
                            module_logger.error(f"Error calculating momentum for {symbol} on {current_date}: {str(e)}")

                # Select ETF with highest momentum
                target_asset_for_today = None
                if daily_momentums:
                    # Filter out NaN values
                    valid_momentums = {k: v for k, v in daily_momentums.items() if not pd.isna(v)}

                    if valid_momentums:
                        try:
                            # Find symbol with highest momentum
                            target_asset_for_today = max(valid_momentums.items(), key=lambda x: x[1])[0]
                            module_logger.info(f"Date: {current_date}, Selected target: {target_asset_for_today}, Momentum: {valid_momentums[target_asset_for_today]:.2f}%, Currently held: {currently_held_asset}")
                        except Exception as e:
                            module_logger.error(f"Error selecting target asset on {current_date}: {str(e)}")

                # Generate signals for each ETF
                daily_signals = []  # Track signals for this day

                for symbol in self.all_symbols_in_strategy:
                    if symbol in self.portfolio_data:
                        # Get OHLCV data for this date
                        try:
                            if current_date not in self.portfolio_data[symbol].index:
                                module_logger.debug(f"No data for {symbol} on {current_date}")
                                continue

                            symbol_data = self.portfolio_data[symbol].loc[current_date]

                            # Default signal is EMPTY
                            current_signal = TradeSignalState.EMPTY.value

                            # Determine signal based on rotation logic
                            if target_asset_for_today:
                                if symbol == target_asset_for_today:
                                    # This is our target asset
                                    if currently_held_asset == symbol:
                                        # Continue holding
                                        current_signal = TradeSignalState.HOLD.value
                                    else:
                                        # New buy
                                        current_signal = TradeSignalState.BUY.value
                                elif symbol == currently_held_asset:
                                    # We're holding this but it's no longer the target - sell
                                    current_signal = TradeSignalState.SELL.value
                            elif symbol == currently_held_asset:
                                # No target asset but we're holding this - sell
                                current_signal = TradeSignalState.SELL.value

                            # Validate OHLCV data
                            for field in ['Open', 'High', 'Low', 'Close', 'Volume']:
                                if field not in symbol_data or pd.isna(symbol_data[field]) or not np.isfinite(symbol_data[field]):
                                    module_logger.warning(f"Invalid {field} data for {symbol} on {current_date}: {symbol_data.get(field, 'missing')}")
                                    # Use a default value or previous value
                                    if field == 'Volume':
                                        symbol_data[field] = 0
                                    else:
                                        # For price fields, use the last valid price or a default
                                        prev_data = self.portfolio_data[symbol]['Close'].loc[:current_date]
                                        if len(prev_data) > 1:
                                            symbol_data[field] = prev_data.iloc[-2]  # Use previous day's close
                                        else:
                                            symbol_data[field] = 100.0  # Default fallback

                            # Create signal record
                            signal_record = {
                                'symbol': symbol,
                                'date': current_date,
                                'open': float(symbol_data['Open']),
                                'high': float(symbol_data['High']),
                                'low': float(symbol_data['Low']),
                                'close': float(symbol_data['Close']),
                                'volume': float(symbol_data['Volume']),
                                'signal': current_signal,
                                'momentum': float(daily_momentums.get(symbol, np.nan))
                            }

                            daily_signals.append(signal_record)
                            all_signals_list.append(signal_record)
                        except KeyError as e:
                            module_logger.warning(f"KeyError for {symbol} on {current_date}: {str(e)}")
                        except Exception as e:
                            module_logger.error(f"Error generating signal for {symbol} on {current_date}: {str(e)}")

                # Log signals for this day
                buy_signals = [s['symbol'] for s in daily_signals if s['signal'] == TradeSignalState.BUY.value]
                sell_signals = [s['symbol'] for s in daily_signals if s['signal'] == TradeSignalState.SELL.value]
                hold_signals = [s['symbol'] for s in daily_signals if s['signal'] == TradeSignalState.HOLD.value]

                if buy_signals or sell_signals or hold_signals:
                    module_logger.info(f"Date: {current_date}, BUY: {buy_signals}, SELL: {sell_signals}, HOLD: {hold_signals}")

                # Verify we have at most one BUY or HOLD signal
                if len(buy_signals) + len(hold_signals) > 1:
                    module_logger.error(f"ERROR: Multiple BUY/HOLD signals on {current_date}: BUY={buy_signals}, HOLD={hold_signals}")

                # Update currently held asset for next iteration
                if target_asset_for_today:
                    currently_held_asset = target_asset_for_today
                elif currently_held_asset and any(s['symbol'] == currently_held_asset and s['signal'] == TradeSignalState.SELL.value for s in daily_signals):
                    currently_held_asset = None

                module_logger.info(f"End of day {current_date}, currently held asset: {currently_held_asset}")

            # Create DataFrame from collected signals
            if all_signals_list:
                module_logger.info(f"Generated {len(all_signals_list)} signal records")
                final_signals_df = pd.DataFrame(all_signals_list)
                final_signals_df['date'] = pd.to_datetime(final_signals_df['date'])
                final_signals_df.set_index('date', inplace=True)
                return final_signals_df
            else:
                module_logger.warning("No signals generated")
                return pd.DataFrame()

        except Exception as e:
            module_logger.error(f"Error in generate_signals: {str(e)}")
            module_logger.error(traceback.format_exc())
            return pd.DataFrame()
