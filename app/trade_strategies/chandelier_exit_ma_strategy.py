import pandas as pd
from utilities.helpers import calculate_chandelier_stop, calculate_ma
from trade_strategies import BaseTradeStrategy, TradeSignalState

class ChandelierExitMAStrategy(BaseTradeStrategy):
    """吊灯止损+移动平均线策略"""

    # 默认参数
    params = {
        'n_atr': 60,
        'atr_multiplier': 4,
        'n_ma': 250,
    }

    def __init__(self, params=None):
        super().__init__()
        if params:
            self.params.update(params)

    def set_data(self, symbol, data):
        self.symbol = symbol
        self.data = data
        self.calculate_indicators()

    def calculate_indicators(self):
        # 计算ATR, MA, 吊灯止损
        self.data = calculate_chandelier_stop(calculate_ma(self.data, self.params['n_ma']), self.params['n_atr'], self.params['atr_multiplier'])

    def true_range(self, series):
        return series.max() - series.min()
    
    def is_buy_signal(self, close, chandelier_stop, ma):
        return (close > chandelier_stop) and (close > ma)
    
    def is_sell_signal(self, close, chandelier_stop, ma):
        return (close < chandelier_stop) and (close < ma)

    def generate_signals(self):
        # 初始化信号数组，初始状态为Empty
        signals = [TradeSignalState.EMPTY.value for _ in range(len(self.data))]
        
        last_state = TradeSignalState.EMPTY.value

        for i in range(1, len(self.data)):
            if self.is_buy_signal(self.data['Close'].iloc[i], self.data['chandelier_stop'].iloc[i], self.data['ma'].iloc[i]):
                if last_state in [TradeSignalState.EMPTY.value, TradeSignalState.SELL.value]:
                    signals[i] = TradeSignalState.BUY.value
                    last_state = TradeSignalState.HOLD.value
                elif last_state == TradeSignalState.HOLD.value:
                    signals[i] = TradeSignalState.HOLD.value
            elif self.is_sell_signal(self.data['Close'].iloc[i], self.data['chandelier_stop'].iloc[i], self.data['ma'].iloc[i]):
                if last_state in [TradeSignalState.HOLD.value, TradeSignalState.BUY.value]:
                    signals[i] = TradeSignalState.SELL.value
                    last_state = TradeSignalState.EMPTY.value
                else:
                    signals[i] = TradeSignalState.EMPTY.value
            else:
                signals[i] = last_state

        # 创建信号DataFrame
        signal_df = pd.DataFrame({
            'symbol': self.symbol,
            'open': self.data['Open'],
            'high': self.data['High'],
            'low': self.data['Low'],
            'close': self.data['Close'],
            'volume': self.data['Volume'],
            'atr': self.data['atr'],
            'ma': self.data['ma'],
            'chandelier_stop': self.data['chandelier_stop'],
            'signal': signals
        })

        return signal_df
