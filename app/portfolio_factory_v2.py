import json
import logging
from typing import Dict, Any

from portfolios.portfolio_manager import PortfolioManager
from components.registry import ComponentRegistry
from components.signal_evaluator import SignalEvaluator
from components.market_indicators import MarketIndicatorManager
from trade_strategies.composite_strategy import CompositeStrategy

module_logger = logging.getLogger(__name__)

class PortfolioFactory:
    def __init__(self):
        self.config_registry = {}
        # 使用全局组件注册表替代硬编码的策略注册表
        self.component_registry = ComponentRegistry.get_instance()
        module_logger.info("PortfolioFactory 初始化，使用 ComponentRegistry 管理策略组件")

    def load_portfolio_config_from_file(self, config_path=None):
        """从配置文件加载投资组合配置

        Args:
            config_path (str, optional): 配置文件路径，如果不提供，则默认加载 config/portfolio_config.json
        """
        # 如果没有提供配置路径，使用默认路径
        if config_path is None:
            config_path = 'config/portfolio_config.json'

        # 支持多种配置路径格式：相对路径、绝对路径或只有文件名
        if not config_path.startswith('/'):
            # 相对路径，需要补全
            if not config_path.startswith('config/'):
                # 只有文件名，假设在 config 目录下
                config_path = f'config/{config_path}'

        module_logger.info(f"Loading portfolio configurations from {config_path}")
        try:
            with open(config_path) as file:
                configs = json.load(file)
                for config in configs['portfolios']:
                    self.register_portfolio(config)
                module_logger.info(f"Loaded {len(configs['portfolios'])} portfolio configurations")
        except FileNotFoundError:
            module_logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            module_logger.error(f"配置文件 {config_path} 格式错误")
            raise

    def load_all_config_files_from_directory(self, directory='config'):
        """从指定目录加载所有JSON配置文件

        Args:
            directory (str): 配置文件目录，默认为 'config'

        Returns:
            int: 成功加载的配置文件数量
        """
        import os

        loaded_files = 0

        if not os.path.exists(directory):
            module_logger.warning(f"配置目录 {directory} 不存在")
            return loaded_files

        module_logger.info(f"从目录 {directory} 加载所有配置文件")

        for filename in os.listdir(directory):
            if filename.endswith('.json'):
                config_path = os.path.join(directory, filename)
                try:
                    module_logger.info(f"加载配置文件: {config_path}")
                    with open(config_path) as file:
                        configs = json.load(file)
                        if 'portfolios' in configs:
                            for config in configs['portfolios']:
                                self.register_portfolio(config)
                            module_logger.info(f"从 {filename} 加载了 {len(configs['portfolios'])} 个投资组合配置")
                            loaded_files += 1
                        else:
                            module_logger.warning(f"配置文件 {filename} 没有 'portfolios' 键")
                except Exception as e:
                    module_logger.error(f"加载配置文件 {filename} 时出错: {str(e)}")
                    # 继续处理其他文件，不会因一个文件错误而中断

        module_logger.info(f"共加载了 {loaded_files} 个配置文件，注册了 {len(self.config_registry)} 个投资组合")
        return loaded_files

    def register_portfolio(self, config):
        self.config_registry[config['code']] = config

    def get_portfolio_configs(self):
        return self.config_registry

    def get_portfolio(self, code, data_loader, params=None):
        """Create a portfolio instance from a configuration code.

        Args:
            code: Portfolio configuration code
            data_loader: Data loader instance for fetching market data
            params: Optional parameters to override configuration

        Returns:
            PortfolioManager: Initialized portfolio manager instance

        Raises:
            ValueError: If portfolio code is not found or strategy components cannot be created
        """
        if code not in self.config_registry:
            raise ValueError(f"Portfolio code '{code}' not found.")
        config = self.config_registry[code]

        # Check if we have a strategy_definition (primitive-based strategy)
        if "strategy_definition" in config:
            return self._create_portfolio_from_definition(config, data_loader, params)
        else:
            # Legacy strategy configuration
            return self._create_portfolio_from_legacy_config(config, data_loader, params)

    def _create_portfolio_from_legacy_config(self, config: Dict[str, Any], data_loader, params=None):
        """Create a portfolio using the legacy strategy configuration format.

        Args:
            config: Portfolio configuration dictionary
            data_loader: Data loader instance
            params: Optional parameters to override configuration

        Returns:
            PortfolioManager: Initialized portfolio manager instance
        """
        # 使用 ComponentRegistry 获取交易策略类
        strategy_name = config['strategy']['name']
        strategy_class = self.component_registry.get_trade_strategy(strategy_name)
        if not strategy_class:
            raise ValueError(f"Trade strategy '{strategy_name}' not found in registry.")
        strategy = strategy_class(params=config['strategy'].get('params', {}))

        # 使用 ComponentRegistry 获取资金策略类
        capital_strategy_name = config['capital_strategy']['name']
        capital_strategy_class = self.component_registry.get_capital_strategy(capital_strategy_name)
        if not capital_strategy_class:
            raise ValueError(f"Capital strategy '{capital_strategy_name}' not found in registry.")
        capital_strategy = capital_strategy_class(params=config['capital_strategy'].get('params', {}))

        portfolio_config = config.copy()
        portfolio_config['strategy'] = strategy
        portfolio_config['capital_strategy'] = capital_strategy

        if params:
            for key, value in params.items():
                if value is not None:
                    portfolio_config[key] = value

        portfolio = PortfolioManager(
            config=portfolio_config,
            data_loader=data_loader
        )
        return portfolio

    def _create_portfolio_from_definition(self, config: Dict[str, Any], data_loader, params=None):
        """Create a portfolio using the primitive-based strategy definition.

        Args:
            config: Portfolio configuration with strategy_definition
            data_loader: Data loader instance
            params: Optional parameters to override configuration

        Returns:
            PortfolioManager: Initialized portfolio manager instance
        """
        # 创建 portfolio_config 的副本并应用参数覆盖
        portfolio_config = config.copy()
        if params:
            for key, value in params.items():
                if value is not None:
                    portfolio_config[key] = value

        strategy_def = config.get('strategy_definition', {})
        trade_strategy_def = strategy_def.get('trade_strategy', {})

        # 初始化市场指标管理器（如果配置中包含市场指标定义）
        market_indicator_manager = None
        market_indicators_def = strategy_def.get('market_indicators', {})

        if market_indicators_def:
            module_logger.info(f"Initializing market indicator manager for portfolio {config.get('code')}")
            market_indicator_manager = MarketIndicatorManager(data_loader)

            # 加载市场指标
            for indicator_def in market_indicators_def.get('indicators', []):
                indicator_code = indicator_def.get('code')
                indicator_name = indicator_def.get('name', indicator_code)

                # 使用组合配置的时间范围作为市场指标的默认时间范围
                portfolio_start_date = portfolio_config.get('start_date', '2018-01-01')
                portfolio_end_date = portfolio_config.get('end_date')

                # 只有在指标定义中明确指定时，才覆盖组合的时间范围
                start_date = indicator_def.get('start_date', portfolio_start_date)
                end_date = indicator_def.get('end_date', portfolio_end_date)

                market_indicator_manager.load_indicator(indicator_name, indicator_code, start_date, end_date)

            # 注册市场转换器
            for transformer_def in market_indicators_def.get('transformers', []):
                transformer_name = transformer_def.get('name')
                transformer_type = transformer_def.get('type')
                params = transformer_def.get('params', {})
                indicator_name = params.get('indicator')

                module_logger.info(f"Registering market transformer {transformer_name} for indicator {indicator_name}")
                market_indicator_manager.register_transformer(
                    transformer_name, indicator_name, transformer_type, params
                )

        # Create indicator instances
        indicator_instances = {}
        for indicator_config in trade_strategy_def.get('indicators', []):
            indicator_id = indicator_config.get('id')
            indicator_type = indicator_config.get('type')
            indicator_params = indicator_config.get('params', {})

            indicator_class = self.component_registry.get_indicator(indicator_type)
            if not indicator_class:
                raise ValueError(f"Indicator type '{indicator_type}' not found in registry.")

            indicator_instance = indicator_class(params=indicator_params)
            indicator_instances[indicator_id] = indicator_instance

        # Process signal configurations
        signal_configs = {}
        for signal_config in trade_strategy_def.get('signals', []):
            signal_id = signal_config.get('id')
            signal_type = signal_config.get('type')
            signal_inputs = signal_config.get('inputs', [])
            signal_params = signal_config.get('params', {})

            # Verify signal type exists in registry
            if not self.component_registry.get_signal(signal_type):
                raise ValueError(f"Signal type '{signal_type}' not found in registry.")

            # 保留完整的信号配置，包含id字段，这对于SignalEvaluator很重要
            signal_configs[signal_id] = {
                'id': signal_id,
                'type': signal_type,
                'inputs': signal_inputs,
                'params': signal_params
            }

        # Create signal evaluator with market indicator manager if available
        signal_evaluator = None
        if market_indicator_manager:
            module_logger.info("Creating SignalEvaluator with market indicator manager")
            signal_evaluator = SignalEvaluator(
                registry=self.component_registry,
                market_indicator_manager=market_indicator_manager
            )

        # Create CompositeStrategy instance
        outputs = trade_strategy_def.get('outputs', {})
        trade_strategy = CompositeStrategy(
            indicator_instances=indicator_instances,
            signals_config=signal_configs,
            outputs=outputs,
            signal_evaluator=signal_evaluator
        )

        # Add market indicator manager to trade strategy
        trade_strategy._market_indicator_manager = market_indicator_manager
        # 通过检查信号组件的类属性来自动检测是否需要全局数据
        needs_global_data_auto_detected = False
        for signal_id, signal_config_item in signal_configs.items():
            signal_type_name = signal_config_item.get('type')
            if signal_type_name:
                SignalClass = self.component_registry.get_signal(signal_type_name)
                if SignalClass and getattr(SignalClass, 'REQUIRES_GLOBAL_CONTEXT', False):
                    needs_global_data_auto_detected = True
                    module_logger.info(
                        f"自动启用全局数据支持，因为信号 '{signal_id}' (类型: {signal_type_name}) "
                        f"声明了 REQUIRES_GLOBAL_CONTEXT = True。组合策略: {config.get('code')}"
                    )
                    break # 一旦检测到任何一个需要全局数据的信号，即可确定
        
        if needs_global_data_auto_detected:
            trade_strategy.needs_global_data = True
        else:
            trade_strategy.needs_global_data = False # 确保默认或未检测到时为False

        # Create capital strategy
        capital_strategy_def = strategy_def.get('capital_strategy', {})
        capital_strategy_name = capital_strategy_def.get('name')
        capital_strategy_params = capital_strategy_def.get('params', {})

        capital_strategy_class = self.component_registry.get_capital_strategy(capital_strategy_name)
        if not capital_strategy_class:
            raise ValueError(f"Capital strategy '{capital_strategy_name}' not found in registry.")

        capital_strategy = capital_strategy_class(params=capital_strategy_params)

        # 更新 portfolio_config 添加策略和资金策略
        portfolio_config['strategy'] = trade_strategy
        portfolio_config['capital_strategy'] = capital_strategy

        # Create the portfolio manager
        portfolio = PortfolioManager(
            config=portfolio_config,
            data_loader=data_loader
        )

        module_logger.info(f"Created portfolio with CompositeStrategy for code: {config.get('code')}")
        return portfolio

    def list_portfolio_codes(self):
        return list(self.config_registry.keys())
