"""
Helper functions for common strategy patterns.

This module provides utility functions to easily create common trading strategy patterns
using primitive components. These functions generate the indicator and signal configurations
needed for typical strategies.
"""

from typing import Dict, Any, List, Optional, Union, <PERSON><PERSON>


def create_dual_ma_strategy(
    fast_period: int = 20,
    slow_period: int = 50,
    column: str = "Close",
    ma_type: str = "SMA"
) -> Dict[str, Any]:
    """
    Create a dual moving average crossover strategy configuration.
    
    Args:
        fast_period: Period for the fast moving average
        slow_period: Period for the slow moving average
        column: Price column to use for calculation
        ma_type: Type of moving average ("SMA" or "EMA")
        
    Returns:
        Dictionary with indicators, signals and outputs configuration
    """
    return {
        "indicators": [
            {
                "id": f"{ma_type.lower()}{fast_period}",
                "type": ma_type,
                "params": {"period": fast_period, "column": column}
            },
            {
                "id": f"{ma_type.lower()}{slow_period}",
                "type": ma_type,
                "params": {"period": slow_period, "column": column}
            }
        ],
        "signals": [
            {
                "id": "buy_signal",
                "type": "Crossover",
                "params": {"mode": "simple"},
                "inputs": [
                    {"ref": f"{ma_type.lower()}{fast_period}"},
                    {"ref": f"{ma_type.lower()}{slow_period}"}
                ]
            },
            {
                "id": "sell_signal",
                "type": "Crossunder",
                "params": {"mode": "simple"},
                "inputs": [
                    {"ref": f"{ma_type.lower()}{fast_period}"},
                    {"ref": f"{ma_type.lower()}{slow_period}"}
                ]
            }
        ],
        "outputs": {
            "buy_signal": "buy_signal",
            "sell_signal": "sell_signal"
        }
    }


def create_rsi_strategy(
    period: int = 14,
    overbought: int = 70,
    oversold: int = 30,
    column: str = "Close",
    use_streak: bool = False,
    streak_length: int = 2
) -> Dict[str, Any]:
    """
    Create a RSI-based strategy configuration.
    
    Args:
        period: RSI calculation period
        overbought: Overbought threshold
        oversold: Oversold threshold
        column: Price column to use for calculation
        use_streak: Whether to require conditions to hold for several periods
        streak_length: Required streak length if use_streak is True
        
    Returns:
        Dictionary with indicators, signals and outputs configuration
    """
    config = {
        "indicators": [
            {
                "id": f"rsi{period}",
                "type": "RSI",
                "params": {"period": period, "column": column}
            }
        ],
        "signals": [
            {
                "id": "rsi_oversold",
                "type": "Comparison",
                "params": {"comparison": "less", "threshold": oversold},
                "inputs": [{"ref": f"rsi{period}"}]
            },
            {
                "id": "rsi_overbought",
                "type": "Comparison",
                "params": {"comparison": "greater", "threshold": overbought},
                "inputs": [{"ref": f"rsi{period}"}]
            }
        ],
        "outputs": {
            "buy_signal": "buy_signal",
            "sell_signal": "sell_signal"
        }
    }
    
    if use_streak:
        config["signals"].extend([
            {
                "id": "buy_signal",
                "type": "Streak",
                "params": {"min_length": streak_length},
                "inputs": [{"ref": "rsi_oversold"}]
            },
            {
                "id": "sell_signal",
                "type": "Streak",
                "params": {"min_length": streak_length},
                "inputs": [{"ref": "rsi_overbought"}]
            }
        ])
    else:
        config["signals"].extend([
            {"id": "buy_signal", "type": "identity", "inputs": [{"ref": "rsi_oversold"}]},
            {"id": "sell_signal", "type": "identity", "inputs": [{"ref": "rsi_overbought"}]}
        ])
    
    return config


def create_bollinger_strategy(
    period: int = 20,
    std_dev: float = 2.0,
    column: str = "Close",
    mode: str = "reversal",
    additional_filters: bool = False
) -> Dict[str, Any]:
    """
    Create a Bollinger Bands strategy configuration.
    
    Args:
        period: Bollinger Bands period
        std_dev: Standard deviation multiplier
        column: Price column to use for calculation
        mode: Strategy mode ('reversal' or 'breakout')
        additional_filters: Add ATR and volume filters
        
    Returns:
        Dictionary with indicators, signals and outputs configuration
    """
    config = {
        "indicators": [
            {
                "id": "bb",
                "type": "BollingerBands",
                "params": {
                    "period": period,
                    "std_dev": std_dev,
                    "column": column,
                    "method": "sma"
                }
            }
        ],
        "signals": [],
        "outputs": {
            "buy_signal": "buy_signal",
            "sell_signal": "sell_signal"
        }
    }
    
    # Add ATR indicator if using additional filters
    if additional_filters:
        config["indicators"].append({
            "id": "atr",
            "type": "ATR",
            "params": {"period": 14, "method": "sma"}
        })
        
        config["signals"].append({
            "id": "high_volatility",
            "type": "Comparison",
            "params": {"comparison": "greater", "threshold": 0.02},
            "inputs": [
                {"divide": [
                    {"ref": "atr"},
                    {"column": "Close"}
                ]}
            ]
        })
    
    if mode == "reversal":
        # Reversal strategy (buy at lower band, sell at upper band)
        config["signals"].extend([
            {
                "id": "price_cross_lower",
                "type": "Crossover",
                "params": {"mode": "simple"},
                "inputs": [{"column": column}, {"ref": "bb", "output": "lower"}]
            },
            {
                "id": "price_cross_upper",
                "type": "Crossunder",
                "params": {"mode": "simple"},
                "inputs": [{"column": column}, {"ref": "bb", "output": "upper"}]
            }
        ])
        
        if additional_filters:
            config["signals"].extend([
                {
                    "id": "buy_signal",
                    "type": "And",
                    "inputs": [{"ref": "price_cross_lower"}, {"ref": "high_volatility"}]
                },
                {
                    "id": "sell_signal",
                    "type": "identity",
                    "inputs": [{"ref": "price_cross_upper"}]
                }
            ])
        else:
            config["signals"].extend([
                {"id": "buy_signal", "type": "identity", "inputs": [{"ref": "price_cross_lower"}]},
                {"id": "sell_signal", "type": "identity", "inputs": [{"ref": "price_cross_upper"}]}
            ])
    else:
        # Breakout strategy (buy when breaking upper band, sell when breaking lower band)
        config["signals"].extend([
            {
                "id": "price_break_upper",
                "type": "Crossover",
                "params": {"mode": "simple"},
                "inputs": [{"column": column}, {"ref": "bb", "output": "upper"}]
            },
            {
                "id": "price_break_lower",
                "type": "Crossunder",
                "params": {"mode": "simple"},
                "inputs": [{"column": column}, {"ref": "bb", "output": "lower"}]
            },
            {"id": "buy_signal", "type": "identity", "inputs": [{"ref": "price_break_upper"}]},
            {"id": "sell_signal", "type": "identity", "inputs": [{"ref": "price_break_lower"}]}
        ])
    
    return config


def create_macd_strategy(
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
    use_volume: bool = False,
    volume_period: int = 20,
    volume_threshold: float = 1.5
) -> Dict[str, Any]:
    """
    Create a MACD-based strategy configuration, optionally with volume confirmation.
    
    Args:
        fast_period: MACD fast period
        slow_period: MACD slow period
        signal_period: MACD signal line period
        use_volume: Whether to add volume confirmation
        volume_period: Volume MA period if use_volume is True
        volume_threshold: Volume surge threshold if use_volume is True
        
    Returns:
        Dictionary with indicators, signals and outputs configuration
    """
    config = {
        "indicators": [
            {
                "id": "macd",
                "type": "MACD",
                "params": {
                    "fast_period": fast_period,
                    "slow_period": slow_period,
                    "signal_period": signal_period,
                    "column": "Close"
                }
            }
        ],
        "signals": [
            {
                "id": "macd_crossover",
                "type": "Crossover",
                "params": {"mode": "simple"},
                "inputs": [
                    {"ref": "macd", "output": "macd"},
                    {"ref": "macd", "output": "signal"}
                ]
            },
            {
                "id": "macd_crossunder",
                "type": "Crossunder",
                "params": {"mode": "simple"},
                "inputs": [
                    {"ref": "macd", "output": "macd"},
                    {"ref": "macd", "output": "signal"}
                ]
            }
        ],
        "outputs": {
            "buy_signal": "buy_signal",
            "sell_signal": "sell_signal"
        }
    }
    
    if use_volume:
        config["indicators"].append({
            "id": "volume_ma",
            "type": "SMA",
            "params": {"period": volume_period, "column": "Volume"}
        })
        
        config["signals"].extend([
            {
                "id": "volume_surge",
                "type": "Comparison",
                "params": {"comparison": "greater", "threshold": volume_threshold},
                "inputs": [{"column": "Volume"}, {"ref": "volume_ma"}]
            },
            {
                "id": "buy_signal",
                "type": "And",
                "inputs": [{"ref": "macd_crossover"}, {"ref": "volume_surge"}]
            },
            {
                "id": "sell_signal",
                "type": "identity",
                "inputs": [{"ref": "macd_crossunder"}]
            }
        ])
    else:
        config["signals"].extend([
            {"id": "buy_signal", "type": "identity", "inputs": [{"ref": "macd_crossover"}]},
            {"id": "sell_signal", "type": "identity", "inputs": [{"ref": "macd_crossunder"}]}
        ])
    
    return config


def combine_strategies(
    strategies: List[Dict[str, Any]],
    combine_mode: str = "and"
) -> Dict[str, Any]:
    """
    Combine multiple strategy configurations into one unified strategy.
    
    This function takes multiple strategy configurations (each containing
    indicators, signals, and outputs) and combines them into a single
    coherent strategy configuration. It handles ID conflicts by prefixing
    all components with a strategy-specific identifier and properly
    redirects all internal references.
    
    The buy and sell signals from all strategies are combined using either
    the 'and' or 'or' logical operation, as specified by the combine_mode.
    
    Args:
        strategies: List of strategy configurations, each should be a dictionary
            with 'indicators', 'signals', and 'outputs' keys. Each strategy must
            have 'buy_signal' and 'sell_signal' defined in its outputs.
        combine_mode: How to combine buy/sell signals, must be either 'and' or 'or'.
            'and': All strategies must agree for a signal to be generated.
            'or': Any strategy can trigger a signal.
    
    Returns:
        Dict[str, Any]: A combined strategy configuration with the following structure:
            - indicators: List of all indicators from all strategies (with prefixed IDs)
            - signals: List of all signals from all strategies (with prefixed IDs)
                plus two additional signals 'combined_buy' and 'combined_sell'
            - outputs: Dictionary with 'buy_signal' and 'sell_signal' mapped to
                the combined signals
    
    Raises:
        ValueError: If no strategies are provided or if the combine_mode is invalid
        KeyError: If a required key is missing in any strategy configuration
    
    Example:
        ```python
        # Create two separate strategy configurations
        ma_strategy = create_dual_ma_strategy(fast_period=10, slow_period=30)
        rsi_strategy = create_rsi_strategy(period=14, overbought=70, oversold=30)
        
        # Combine them so both must agree to generate signals
        combined = combine_strategies([ma_strategy, rsi_strategy], combine_mode="and")
        ```
    """
    if not strategies:
        raise ValueError("No strategies provided to combine")
    
    # Initialize the combined configuration
    combined = {
        "indicators": [],
        "signals": [],
        "outputs": {
            "buy_signal": "combined_buy",
            "sell_signal": "combined_sell"
        }
    }
    
    # Track used IDs to avoid conflicts
    used_ids = set()
    buy_signals = []
    sell_signals = []
    
    # Process each strategy
    for idx, strategy in enumerate(strategies):
        strategy_prefix = f"s{idx}_"
        
        # Add indicators with prefixed IDs
        for indicator in strategy["indicators"]:
            new_id = f"{strategy_prefix}{indicator['id']}"
            
            # Skip if ID already used
            if new_id in used_ids:
                continue
                
            used_ids.add(new_id)
            combined["indicators"].append({
                "id": new_id,
                "type": indicator["type"],
                "params": indicator["params"]
            })
        
        # Update signal references and add them
        buy_signal_id = None
        sell_signal_id = None
        
        for signal in strategy["signals"]:
            new_id = f"{strategy_prefix}{signal['id']}"
            
            if new_id in used_ids:
                continue
                
            used_ids.add(new_id)
            
            # Update references in inputs
            updated_inputs = []
            for input_item in signal["inputs"]:
                if "ref" in input_item:
                    updated_input = {
                        "ref": f"{strategy_prefix}{input_item['ref']}"
                    }
                    if "output" in input_item:
                        updated_input["output"] = input_item["output"]
                    updated_inputs.append(updated_input)
                else:
                    updated_inputs.append(input_item)
            
            combined["signals"].append({
                "id": new_id,
                "type": signal["type"],
                "params": signal["params"] if "params" in signal else {},
                "inputs": updated_inputs
            })
            
            # Track buy and sell signals
            if strategy["outputs"]["buy_signal"] == signal["id"]:
                buy_signal_id = new_id
            if strategy["outputs"]["sell_signal"] == signal["id"]:
                sell_signal_id = new_id
        
        if buy_signal_id:
            buy_signals.append({"ref": buy_signal_id})
        if sell_signal_id:
            sell_signals.append({"ref": sell_signal_id})
    
    # Create combined signals
    combined_type = "And" if combine_mode == "and" else "Or"
    
    combined["signals"].extend([
        {
            "id": "combined_buy",
            "type": combined_type,
            "inputs": buy_signals
        },
        {
            "id": "combined_sell",
            "type": combined_type,
            "inputs": sell_signals
        }
    ])
    
    return combined
