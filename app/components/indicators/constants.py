import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union
from components.base.primitives import BaseIndicatorPrimitive
import logging

module_logger = logging.getLogger(__name__)

class Constant(BaseIndicatorPrimitive):
    """常量指标原语。
    
    生成一个固定值的时间序列，长度与输入数据相同。
    
    Attributes:
        params (Dict[str, Any]): 包含以下参数:
            - value (float): 要生成的常量值
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """获取默认参数。
        
        Returns:
            Dict[str, Any]: 默认参数 value=1.0
        """
        return {
            'value': 1.0
        }
    
    def validate_params(self) -> None:
        """验证参数。
        
        Raises:
            ValueError: 如果value参数未提供或不能转换为浮点数
        """
        if 'value' not in self.params:
            raise ValueError("必须提供'value'参数")
        
        try:
            float(self.params['value'])
        except (ValueError, TypeError):
            raise ValueError("'value'参数必须是一个数值")
    
    def calculate(self, data: pd.DataFrame, *args, **kwargs) -> pd.Series:
        """计算常量指标。
        
        生成一个与输入数据索引相同的常量序列。
        
        Args:
            data (pd.DataFrame): 输入数据，包含价格和其他市场数据
            
        Returns:
            pd.Series: 包含常量值的pandas Series，索引与输入数据相同
        """
        module_logger.info(f"Creating constant series with value {self.params['value']}")
        
        # 创建与输入数据索引相同的常量序列
        constant_value = float(self.params['value'])
        constant_series = pd.Series(constant_value, index=data.index)
        
        return constant_series
