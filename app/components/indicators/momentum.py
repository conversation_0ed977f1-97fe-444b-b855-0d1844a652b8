"""
Momentum indicators.

This module provides momentum-based technical indicators including:
- Relative Strength Index (RSI)
"""

import pandas as pd
import numpy as np
from typing import Dict, Any

from components.base.primitives import BaseIndicatorPrimitive


class RSI(BaseIndicatorPrimitive):
    """Relative Strength Index indicator primitive.
    
    Calculates the RSI technical indicator which measures the magnitude of recent
    price changes to evaluate overbought or oversold conditions.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the RSI calculation (default: 14)
            - column (str): The column to calculate RSI on (default: 'Close')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with period=14 and column='Close'
        """
        return {
            'period': 14,
            'column': 'Close'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If column is not a string
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Relative Strength Index.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The calculated RSI values
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        # Extract price series
        price = data[self.params['column']]
        
        # Calculate price changes
        delta = price.diff()
        
        # Separate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # Calculate initial average gain and loss using SMA
        avg_gain = gain.rolling(window=self.params['period'], min_periods=1).mean()
        avg_loss = loss.rolling(window=self.params['period'], min_periods=1).mean()
        
        # Apply Wilder's smoothing after initial period
        # This is the traditional RSI calculation method
        for i in range(self.params['period'] + 1, len(price)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (self.params['period']-1) + gain.iloc[i]) / self.params['period']
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (self.params['period']-1) + loss.iloc[i]) / self.params['period']
        
        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        # Handle division by zero and other anomalies
        rsi = rsi.replace([np.inf, -np.inf], np.nan)
        
        # Use 50 as a neutral value for NaN (e.g., when avg_loss is zero)
        rsi = rsi.fillna(50)
        
        return rsi


class MACD(BaseIndicatorPrimitive):
    """Moving Average Convergence Divergence indicator primitive.
    
    Calculates the MACD technical indicator which shows the relationship
    between two moving averages of a security's price.
    
    This is a multi-output indicator that produces three separate values:
    1. MACD Line: The difference between fast and slow EMAs
    2. Signal Line: An EMA of the MACD Line
    3. Histogram: The difference between MACD Line and Signal Line
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - fast_period (int): The period for the fast EMA (default: 12)
            - slow_period (int): The period for the slow EMA (default: 26)
            - signal_period (int): The period for the signal EMA (default: 9)
            - column (str): The column to calculate MACD on (default: 'Close')
            - output_format (str): Output format, either 'dict' or 'dataframe' (default: 'dict')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with standard MACD settings
        """
        return {
            'fast_period': 12,      # Default fast EMA period
            'slow_period': 26,      # Default slow EMA period
            'signal_period': 9,     # Default signal EMA period
            'column': 'Close',      # Default price column
            'output_format': 'dict'  # Default output format
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If periods are not positive integers
            ValueError: If column is not a string
            ValueError: If output_format is not 'dict' or 'dataframe'
        """
        # Validate periods
        for param in ['fast_period', 'slow_period', 'signal_period']:
            if not isinstance(self.params[param], int) or self.params[param] <= 0:
                raise ValueError(f"{param} must be a positive integer")
        
        # Validate fast < slow period (standard MACD requirement)
        if self.params['fast_period'] >= self.params['slow_period']:
            raise ValueError("fast_period must be less than slow_period")
        
        # Validate column
        if not isinstance(self.params['column'], str):
            raise ValueError("column must be a string")
        
        # Validate output_format
        if self.params['output_format'] not in ['dict', 'dataframe']:
            raise ValueError("output_format must be either 'dict' or 'dataframe'")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate the MACD indicator components.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            Dict[str, pd.Series]: A dictionary containing the MACD components:
                - 'macd': MACD line (fast EMA - slow EMA)
                - 'signal': Signal line (EMA of MACD line)
                - 'histogram': Histogram (MACD line - signal line)
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        # Extract price series
        price = data[self.params['column']]
        
        # Calculate EMAs
        fast_ema = price.ewm(span=self.params['fast_period'], adjust=False).mean()
        slow_ema = price.ewm(span=self.params['slow_period'], adjust=False).mean()
        
        # Calculate MACD line (fast EMA - slow EMA)
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line (EMA of MACD line)
        signal_line = macd_line.ewm(span=self.params['signal_period'], adjust=False).mean()
        
        # Calculate histogram (MACD line - signal line)
        histogram = macd_line - signal_line
        
        # Return result based on specified output format
        result = {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
        
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame(result)
        
        return result
