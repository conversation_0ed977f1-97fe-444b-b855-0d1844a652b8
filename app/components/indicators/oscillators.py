"""
Oscillator indicators.

This module provides oscillator-based technical indicators including:
- Stochastic Oscillator
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Union

from components.base.primitives import BaseIndicatorPrimitive


class Stochastic(BaseIndicatorPrimitive):
    """Stochastic Oscillator indicator primitive.
    
    Calculates the Stochastic Oscillator which compares a security's closing price
    to its price range over a given time period. The indicator consists of two lines:
    %K (the main line) and %D (the signal line).
    
    This is a multi-output indicator that produces two separate values:
    1. %K: The main stochastic line
    2. %D: A moving average of %K
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - k_period (int): The lookback period for %K calculation (default: 14)
            - d_period (int): The period for the %D moving average (default: 3)
            - d_method (str): The method for calculating %D, 'simple' or 'exponential' (default: 'simple')
            - output_format (str): Output format, either 'dict' or 'dataframe' (default: 'dict')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with standard Stochastic Oscillator settings
        """
        return {
            'k_period': 14,          # Default %K period
            'd_period': 3,           # Default %D period
            'd_method': 'simple',    # Default %D method (SMA)
            'output_format': 'dict'  # Default output format
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If k_period or d_period are not positive integers
            ValueError: If d_method is not one of the allowed methods
            ValueError: If output_format is not 'dict' or 'dataframe'
        """
        # Validate periods
        for param, name in [('k_period', 'k_period'), ('d_period', 'd_period')]:
            if not isinstance(self.params[param], int) or self.params[param] <= 0:
                raise ValueError(f"{name} must be a positive integer")
        
        # Validate d_method
        if self.params['d_method'] not in ['simple', 'exponential']:
            raise ValueError("d_method must be either 'simple' or 'exponential'")
        
        # Validate output_format
        if self.params['output_format'] not in ['dict', 'dataframe']:
            raise ValueError("output_format must be either 'dict' or 'dataframe'")
    
    def calculate(self, data: pd.DataFrame) -> Union[Dict[str, pd.Series], pd.DataFrame]:
        """Calculate the Stochastic Oscillator components.
        
        Args:
            data (pd.DataFrame): OHLCV price data with 'High', 'Low', and 'Close' columns
        
        Returns:
            Union[Dict[str, pd.Series], pd.DataFrame]: A dictionary or DataFrame containing:
                - 'k': %K line (fast stochastic)
                - 'd': %D line (signal line, moving average of %K)
            
        Raises:
            KeyError: If required columns are not found in the data
        """
        # Check required columns
        required_columns = ['High', 'Low', 'Close']
        for col in required_columns:
            if col not in data.columns:
                raise KeyError(f"Column {col} not found in data")
        
        # Extract price series
        high = data['High']
        low = data['Low']
        close = data['Close']
        
        # Calculate %K
        # Formula: %K = 100 * (Current Close - Lowest Low) / (Highest High - Lowest Low)
        lowest_low = low.rolling(window=self.params['k_period']).min()
        highest_high = high.rolling(window=self.params['k_period']).max()
        
        # Calculate raw stochastic value
        k = pd.Series(index=close.index, dtype='float64')
        
        # Handle each point individually to properly handle division by zero
        for i in range(len(close)):
            high_val = highest_high.iloc[i]
            low_val = lowest_low.iloc[i]
            close_val = close.iloc[i]
            
            # Check for NaN values
            if pd.isna(high_val) or pd.isna(low_val) or pd.isna(close_val):
                k.iloc[i] = 50.0  # Neutral value for NaN
                continue
                
            # Handle zero range case (high == low)
            if high_val == low_val:
                # When there's no range, %K is set to 50 (neutral) by convention
                k.iloc[i] = 50.0
            else:
                # Standard calculation when range is non-zero
                k.iloc[i] = 100.0 * (close_val - low_val) / (high_val - low_val)
        
        # Calculate %D (Signal Line) - Moving Average of %K
        if self.params['d_method'] == 'simple':
            d = k.rolling(window=self.params['d_period']).mean()
        else:  # 'exponential'
            d = k.ewm(span=self.params['d_period'], adjust=False).mean()
        
        # Handle NaN values that may occur at the beginning
        k = k.fillna(50)  # Neutral value for stochastic
        d = d.fillna(50)  # Neutral value for stochastic
        
        # Return result based on specified output format
        result = {
            'k': k,
            'd': d
        }
        
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame(result)
        
        return result
