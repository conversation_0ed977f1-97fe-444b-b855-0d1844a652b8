"""
Chandelier Exit indicator primitive.

This module provides the Chandelier Exit technical indicator, which is a
volatility-based indicator designed to identify potential exit points.
"""

import pandas as pd
import logging
from typing import Dict, Any

from components.base.primitives import BaseIndicatorPrimitive

module_logger = logging.getLogger(__name__)


class ChandelierExit(BaseIndicatorPrimitive):
    """Chandelier Exit indicator primitive.
    
    Calculates the Chandelier Exit technical indicator which combines
    the highest price over a period with ATR to identify potential stop levels.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for highest high and ATR (default: 60)
            - multiplier (float): The ATR multiplier (default: 4.0)
            - add_ma (bool): Whether to also calculate MA (default: False)
            - ma_period (int): The period for MA calculation if add_ma=True (default: 250)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'period': 60,  # Default period for ATR and highest high
            'multiplier': 4.0,  # Default ATR multiplier
            'add_ma': False,  # Whether to also calculate MA
            'ma_period': 250   # Default MA period if add_ma=True
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If multiplier is not a positive number
            ValueError: If add_ma is not a boolean
            ValueError: If ma_period is not a positive integer when add_ma is True
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        
        if not isinstance(self.params['multiplier'], (int, float)) or self.params['multiplier'] <= 0:
            raise ValueError("Multiplier must be a positive number")
        
        if not isinstance(self.params['add_ma'], bool):
            raise ValueError("add_ma must be a boolean")
            
        if self.params['add_ma'] and (not isinstance(self.params['ma_period'], int) or self.params['ma_period'] <= 0):
            raise ValueError("ma_period must be a positive integer when add_ma is True")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Chandelier Exit indicator.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The Chandelier Exit values (chandelier_stop)
        
        Raises:
            KeyError: If required columns (High, Low, Close) are not found in the data
        """
        # Verify required columns exist
        required_columns = ['High', 'Low', 'Close']
        for col in required_columns:
            if col not in data.columns:
                raise KeyError(f"Required column '{col}' not found in data")
        
        # Create a copy of the input data to avoid modifying it
        df = data.copy()
        
        # Calculate True Range (TR)
        df.loc[:, 'tr1'] = abs(df['High'] - df['Low'])
        df.loc[:, 'tr2'] = abs(df['High'] - df['Close'].shift())
        df.loc[:, 'tr3'] = abs(df['Low'] - df['Close'].shift())
        df.loc[:, 'tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        
        # Calculate ATR - Simple Moving Average method
        atr = df['tr'].rolling(window=self.params['period']).mean()
        
        # Calculate highest high over the period
        highest_high = df['High'].rolling(window=self.params['period']).max()
        
        # Calculate Chandelier Exit stop level
        chandelier_stop = highest_high - (self.params['multiplier'] * atr)
        
        # Round to 3 decimal places, exactly as in the original implementation
        chandelier_stop = chandelier_stop.apply(lambda x: round(x, 3))
        
        # Drop intermediate columns
        df = df.drop(['tr1', 'tr2', 'tr3', 'tr'], axis=1, errors='ignore')
        
        # We'll just return the chandelier_stop series for simplicity
        # If MA is needed, it should be calculated separately as another indicator
        return chandelier_stop
