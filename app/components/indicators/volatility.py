"""
Volatility indicator primitives.

This module provides volatility-based technical indicators, including:
- ATR (Average True Range)
- Bollinger Bands
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Any, Union

from components.base.primitives import BaseIndicatorPrimitive

module_logger = logging.getLogger(__name__)


class ATR(BaseIndicatorPrimitive):
    """Average True Range indicator primitive.
    
    Calculates the ATR technical indicator which measures market volatility by
    decomposing the entire range of an asset price for a specified period.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for calculating ATR (default: 14)
            - method (str): The calculation method ('sma' or 'ema', default: 'sma')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with period=14, method='sma'
        """
        return {
            'period': 14,
            'method': 'sma'  # 'sma' or 'ema'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If method is not one of the supported methods
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        
        if self.params['method'] not in ['sma', 'ema']:
            raise ValueError("Method must be one of: 'sma', 'ema'")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Average True Range.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The calculated ATR values
            
        Raises:
            KeyError: If required columns (High, Low, Close) are not found in the data
        """
        # Verify required columns exist
        required_columns = ['High', 'Low', 'Close']
        for col in required_columns:
            if col not in data.columns:
                raise KeyError(f"Required column '{col}' not found in data")
        
        # Calculate True Range (TR)
        high = data['High']
        low = data['Low']
        close_prev = data['Close'].shift(1)
        
        # Handle first row where previous close is NaN
        close_prev.iloc[0] = data['Close'].iloc[0]
        
        # TR is the greatest of:
        # 1. Current High - Current Low
        # 2. Abs(Current High - Previous Close)
        # 3. Abs(Current Low - Previous Close)
        tr1 = high - low
        tr2 = (high - close_prev).abs()
        tr3 = (low - close_prev).abs()
        
        # Combine to get True Range
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # Apply the specified calculation method
        if self.params['method'] == 'sma':
            # Simple Moving Average method
            atr = tr.rolling(window=self.params['period']).mean()
        else:  # 'ema'
            # Exponential Moving Average method - Wilder's smoothing factor
            alpha = 1.0 / self.params['period']
            atr = tr.ewm(alpha=alpha, min_periods=self.params['period'], adjust=False).mean()
        
        return atr


class BollingerBands(BaseIndicatorPrimitive):
    """Bollinger Bands indicator primitive.
    
    Calculates Bollinger Bands, which consist of:
    - Middle band: SMA/EMA of the price
    - Upper band: Middle band + (standard deviation * multiplier)
    - Lower band: Middle band - (standard deviation * multiplier)
    
    This is a multi-output indicator returning a dictionary of Series.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the moving average (default: 20)
            - std_dev (float): Standard deviation multiplier (default: 2.0)
            - column (str): The column to calculate bands on (default: 'Close')
            - method (str): MA calculation method ('sma' or 'ema', default: 'sma')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'period': 20,
            'std_dev': 2.0,
            'column': 'Close',
            'method': 'sma'  # 'sma' or 'ema'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If std_dev is not a positive number
            ValueError: If column is not a string
            ValueError: If method is not one of the supported methods
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        
        if not isinstance(self.params['std_dev'], (int, float)) or self.params['std_dev'] <= 0:
            raise ValueError("std_dev must be a positive number")
        
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
            
        if self.params['method'] not in ['sma', 'ema']:
            raise ValueError("Method must be one of: 'sma', 'ema'")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate the Bollinger Bands.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            Dict[str, pd.Series]: Dictionary containing 'upper', 'middle', and 'lower' band Series
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        column = self.params['column']
        if column not in data.columns:
            raise KeyError(f"Column {column} not found in data")
        
        # Extract price series
        price = data[column]
        
        # Calculate middle band (moving average)
        if self.params['method'] == 'sma':
            # Simple Moving Average
            middle_band = price.rolling(
                window=self.params['period'], 
                min_periods=1
            ).mean()
        else:  # 'ema'
            # Exponential Moving Average
            middle_band = price.ewm(
                span=self.params['period'], 
                min_periods=1,
                adjust=False
            ).mean()
        
        # Calculate standard deviation
        std_dev = price.rolling(
            window=self.params['period'], 
            min_periods=1
        ).std(ddof=0)  # ddof=0 for population standard deviation
        
        # Calculate upper and lower bands
        deviation = std_dev * self.params['std_dev']
        upper_band = middle_band + deviation
        lower_band = middle_band - deviation
        
        # Log some debug information
        module_logger.debug(
            f"Calculated Bollinger Bands with period={self.params['period']}, "
            f"std_dev={self.params['std_dev']}, method={self.params['method']}"
        )
        
        # Return a dictionary of all bands
        return {
            'middle': middle_band,
            'upper': upper_band,
            'lower': lower_band
        }
