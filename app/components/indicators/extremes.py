"""
Extremes indicators for trading strategies.

This module provides indicator primitives that identify extreme values (highest/lowest)
over a specified lookback period. These indicators are useful for identifying price levels
that may act as support or resistance, or for developing channel-based trading strategies.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional

from components.base.primitives import BaseIndicatorPrimitive


class ExtremeValueIndicator(BaseIndicatorPrimitive):
    """Base class for extreme value indicators.
    
    This abstract class provides common functionality for indicators that
    identify extreme values (highest or lowest) over a lookback period.
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - column (str): Column name to use from the input DataFrame
            - period (int): Lookback period for finding extreme values
            - output_format (str): Output format ('series' or 'dataframe')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        return {
            'column': 'Close',       # Column to analyze (Close, Open, High, Low, etc.)
            'period': 14,            # Lookback period
            'output_format': 'series', # 'series' or 'dataframe'
        }
    
    def validate_params(self) -> None:
        """
        Validate the current parameter values.
        
        Raises:
            ValueError: If any parameters are invalid
            KeyError: If required parameters are missing
        """
        # 检查必需的参数是否存在
        required_params = ['period', 'output_format', 'column']
        for param in required_params:
            if param not in self.params:
                raise KeyError(f"Required parameter '{param}' is missing")
                
        if self.params['period'] <= 0:
            raise ValueError(f"Period must be a positive integer, got {self.params['period']}")
            
        valid_formats = ['series', 'dataframe']
        if self.params['output_format'] not in valid_formats:
            raise ValueError(f"Output format must be one of {valid_formats}, got {self.params['output_format']}")
            
        if not isinstance(self.params['column'], str):
            raise ValueError(f"Column parameter must be a string, got {type(self.params['column'])}")


class HighestValue(ExtremeValueIndicator):
    """Indicator that calculates the highest value over a lookback period.
    
    This indicator finds the highest value of a specified column (e.g., High, Close)
    over a given lookback period. This can be used for identifying potential
    resistance levels, calculating Donchian channels, or as components in
    other technical indicators.
    
    Example:
        highest = HighestValue(params={'column': 'High', 'period': 20})
        high_values = highest.calculate(price_data)
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - column (str): Column name to use from the input DataFrame (default: 'Close')
            - period (int): Lookback period for finding highest values (default: 14)
            - output_format (str): Output format ('series' or 'dataframe') (default: 'series')
    """
    
    def calculate(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        """Calculate the highest values over the specified lookback period.
        
        Args:
            data (pd.DataFrame): Price data with at least OHLCV columns
            
        Returns:
            Union[pd.Series, pd.DataFrame]: Highest values over the lookback period
            
        Raises:
            ValueError: If column doesn't exist in the data
        """
        column = self.params['column']
        period = self.params['period']
        
        # Validate column exists
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in input data. Available columns: {list(data.columns)}")
        
        # Calculate rolling highest values
        highest = data[column].rolling(window=period).max()
        
        # Return in requested format
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame({
                'highest': highest
            }, index=data.index)
        
        return highest


class LowestValue(ExtremeValueIndicator):
    """Indicator that calculates the lowest value over a lookback period.
    
    This indicator finds the lowest value of a specified column (e.g., Low, Close)
    over a given lookback period. This can be used for identifying potential
    support levels, calculating Donchian channels, or as components in
    other technical indicators.
    
    Example:
        lowest = LowestValue(params={'column': 'Low', 'period': 20})
        low_values = lowest.calculate(price_data)
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - column (str): Column name to use from the input DataFrame (default: 'Close')
            - period (int): Lookback period for finding lowest values (default: 14)
            - output_format (str): Output format ('series' or 'dataframe') (default: 'series')
    """
    
    def calculate(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        """Calculate the lowest values over the specified lookback period.
        
        Args:
            data (pd.DataFrame): Price data with at least OHLCV columns
            
        Returns:
            Union[pd.Series, pd.DataFrame]: Lowest values over the lookback period
            
        Raises:
            ValueError: If column doesn't exist in the data
        """
        column = self.params['column']
        period = self.params['period']
        
        # Validate column exists
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in input data. Available columns: {list(data.columns)}")
        
        # Calculate rolling lowest values
        lowest = data[column].rolling(window=period).min()
        
        # Return in requested format
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame({
                'lowest': lowest
            }, index=data.index)
        
        return lowest


class PercentFromHighest(ExtremeValueIndicator):
    """Indicator that calculates the percent distance from the highest value.
    
    This indicator computes how far the current price is from the highest value
    over a given lookback period, expressed as a percentage. This can be useful for
    identifying potential entry or exit points based on price pullbacks.
    
    Example:
        percent_from_high = PercentFromHighest(params={'column': 'Close', 'period': 20})
        percent_values = percent_from_high.calculate(price_data)
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - column (str): Column name to use from the input DataFrame (default: 'Close')
            - period (int): Lookback period for finding highest values (default: 14)
            - output_format (str): Output format ('series' or 'dataframe') (default: 'series')
    """
    
    def calculate(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        """Calculate the percentage distance from the highest value.
        
        Args:
            data (pd.DataFrame): Price data with at least OHLCV columns
            
        Returns:
            Union[pd.Series, pd.DataFrame]: Percentage distance from highest values
            
        Raises:
            ValueError: If column doesn't exist in the data
        """
        column = self.params['column']
        period = self.params['period']
        
        # Validate column exists
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in input data. Available columns: {list(data.columns)}")
        
        # Calculate rolling highest values
        highest = data[column].rolling(window=period).max()
        
        # Calculate percentage from highest (negative values indicate price is below the highest)
        percent_from_highest = 100 * (data[column] - highest) / highest
        
        # Return in requested format
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame({
                'percent_from_highest': percent_from_highest
            }, index=data.index)
        
        return percent_from_highest


class PercentFromLowest(ExtremeValueIndicator):
    """Indicator that calculates the percent distance from the lowest value.
    
    This indicator computes how far the current price is from the lowest value
    over a given lookback period, expressed as a percentage. This can be useful for
    identifying potential entry or exit points based on price rebounds.
    
    Example:
        percent_from_low = PercentFromLowest(params={'column': 'Close', 'period': 20})
        percent_values = percent_from_low.calculate(price_data)
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - column (str): Column name to use from the input DataFrame (default: 'Close')
            - period (int): Lookback period for finding lowest values (default: 14)
            - output_format (str): Output format ('series' or 'dataframe') (default: 'series')
    """
    
    def calculate(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        """Calculate the percentage distance from the lowest value.
        
        Args:
            data (pd.DataFrame): Price data with at least OHLCV columns
            
        Returns:
            Union[pd.Series, pd.DataFrame]: Percentage distance from lowest values
            
        Raises:
            ValueError: If column doesn't exist in the data
        """
        column = self.params['column']
        period = self.params['period']
        
        # Validate column exists
        if column not in data.columns:
            raise ValueError(f"Column '{column}' not found in input data. Available columns: {list(data.columns)}")
        
        # Calculate rolling lowest values
        lowest = data[column].rolling(window=period).min()
        
        # Calculate percentage from lowest (positive values indicate price is above the lowest)
        percent_from_lowest = 100 * (data[column] - lowest) / lowest
        
        # Return in requested format
        if self.params['output_format'] == 'dataframe':
            return pd.DataFrame({
                'percent_from_lowest': percent_from_lowest
            }, index=data.index)
        
        return percent_from_lowest
