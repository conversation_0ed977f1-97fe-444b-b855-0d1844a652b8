"""
Moving average indicators.

This module provides moving average indicator implementations using pandas
built-in functionality, including:
- Simple Moving Average (SMA)
- Exponential Moving Average (EMA)
"""

import pandas as pd
from typing import Dict, Any

from components.base.primitives import BaseIndicatorPrimitive

class SMA(BaseIndicatorPrimitive):
    """Simple Moving Average indicator primitive.
    
    Uses pandas' rolling window calculation for Simple Moving Average computation.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the moving average
            - column (str): The column to calculate SMA on (default: 'Close')
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with period=20 and column='Close'
        """
        return {
            'period': 20,
            'column': 'Close'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If column is not a string
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Simple Moving Average.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The calculated SMA values
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        # Use pandas' built-in rolling calculation
        return data[self.params['column']].rolling(
            window=self.params['period']
            # 移除min_periods=1以确保与原始策略一致，不足期数将返回NaN
        ).mean()


class EMA(BaseIndicatorPrimitive):
    """Exponential Moving Average indicator primitive.
    
    Uses pandas' exponential weighted moving average (ewm) for EMA calculation.
    EMA gives more weight to recent prices compared to SMA.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the moving average
            - column (str): The column to calculate EMA on (default: 'Close')
            - adjust (bool): Specify if the weights should be normalized (default: False)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with period=20, column='Close', and adjust=False
        """
        return {
            'period': 20,
            'column': 'Close',
            'adjust': False
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If column is not a string
            ValueError: If adjust is not a boolean
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
        if not isinstance(self.params['adjust'], bool):
            raise ValueError("Adjust must be a boolean")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Exponential Moving Average.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The calculated EMA values
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        # Calculate alpha (smoothing factor)
        # Formula: alpha = 2 / (period + 1)
        alpha = 2 / (self.params['period'] + 1)
        
        # Use pandas' ewm (exponential weighted moving average)
        return data[self.params['column']].ewm(
            alpha=alpha,
            adjust=self.params['adjust']
            # 移除min_periods=1以确保与原始策略一致
        ).mean()
