"""
Signal evaluator for component-based trading strategies.

This evaluator enables declarative strategy definitions using primitive components.
The evaluation process follows these steps:

1. Component Tree Evaluation:
   - Evaluates indicators first to establish base metrics
   - Then evaluates signals using the indicator results
   - Handles nested dependencies through recursive tree traversal

2. Signal State Management:
   The state machine follows these transitions:
   EMPTY -> BUY -> HOLD -> SELL -> EMPTY

   Rules:
   - Buy signal moves EMPTY/SELL to BUY, BUY becomes HOLD
   - Sell signal moves HOLD/BUY to SELL, SELL becomes EMPTY
   - States persist until a valid transition signal

3. Multi-data Support:
   - Supports multiple data sources and symbols
   - Maintains independent state machines per symbol
   - Handles cross-symbol dependencies if needed
"""

import pandas as pd
import logging
from typing import Dict, Any, Union
from components.base.primitives import BaseIndicatorPrimitive, BaseSignalPrimitive
from trade_strategies import TradeSignalState

module_logger = logging.getLogger(__name__)

class SignalEvaluator:
    """
    Evaluates trading signals based on primitive components.

    This class handles:
    - Tree-based evaluation of indicators and signals
    - State transitions based on signal conditions 
    - Component dependency resolution
    """

    def __init__(self, registry, market_indicator_manager=None):
        """Initialize signal evaluator.
        
        Args:
            registry: ComponentRegistry instance for accessing primitives
            market_indicator_manager: Optional MarketIndicatorManager instance for
                                      accessing market indicators
        """
        self._registry = registry
        self._market_indicator_manager = market_indicator_manager

    def _create_component(self, config: Dict[str, Any], component_source: str = None) -> Union[BaseIndicatorPrimitive, BaseSignalPrimitive]:
        """Create a component instance from configuration.
        
        Args:
            config: Component configuration dictionary
            component_source: Source of the component ('indicators' or 'signals')
                              to determine which registry to check first
            
        Returns:
            BaseIndicatorPrimitive or BaseSignalPrimitive instance
        """
        if 'type' not in config:
            module_logger.error(f"Required field 'type' missing in component configuration: {config}")
            raise ValueError(f"Missing required 'type' in component configuration. All components must have a type. Got: {config}")
            
        component_type = config['type']
        
        # 根据组件来源决定首先查找哪个注册表
        if component_source == 'signals':
            # 对于信号配置，首先尝试信号注册表
            component_class = self._registry.get_signal(component_type)
            if component_class:
                return component_class(params=config.get('params', {}))
                
            # 然后尝试指标注册表（以防万一）
            component_class = self._registry.get_indicator(component_type)
            if component_class:
                return component_class(params=config.get('params', {}))
        else:
            # 默认情况下，首先尝试指标注册表
            component_class = self._registry.get_indicator(component_type)
            if component_class:
                return component_class(params=config.get('params', {}))
                
            # 然后尝试信号注册表
            component_class = self._registry.get_signal(component_type)
            if component_class:
                return component_class(params=config.get('params', {}))
            
        raise ValueError(f"Unknown component type: {component_type}")

    def _evaluate_node(self, node: Dict[str, Any], data: pd.DataFrame, component_source: str = None) -> Union[pd.Series, Dict[str, pd.Series]]:
        """Recursively evaluate a node in the signal tree.
        
        Args:
            node: Node configuration
            data: Input data frame
            component_source: Source of the component ('indicators' or 'signals')
            
        Returns:
            Union[pd.Series, Dict[str, pd.Series]]: Evaluation result, either a single Series
                or a dictionary of Series for multi-output indicators
        """
        # Handle direct data references
        if 'column' in node:
            return data[node['column']]
            
        # Handle market indicator references
        if 'market' in node:
            if self._market_indicator_manager is None:
                error_msg = f"Market indicator manager is not available but required for '{node['market']}'"
                module_logger.error(error_msg)
                raise ValueError(error_msg)
                
            indicator_name = node['market']
            field = node.get('field', 'close')
            transformer = node.get('transformer')
            
            if transformer:
                series = self._market_indicator_manager.get_transformed_indicator(transformer)
                if series is None:
                    raise ValueError(f"Transformer '{transformer}' not found or failed to transform")
            else:
                series = self._market_indicator_manager.get_indicator_series(indicator_name, field)
                if series is None:
                    raise ValueError(f"Market indicator '{indicator_name}' not found or field '{field}' not available")
                    
            # 确保与数据索引对齐
            aligned_series = series.reindex(data.index, method='ffill')
            
            # 初始化市场指标缓存字典（如果不存在）
            if not hasattr(self, '_evaluated_market_indicators'):
                self._evaluated_market_indicators = {}
                
            # 存储市场指标值
            if transformer:
                self._evaluated_market_indicators[transformer] = aligned_series
            else:
                key = f"{indicator_name}_{field}"
                self._evaluated_market_indicators[key] = aligned_series
                
            return aligned_series
        
        # Handle references to other components
        if 'ref' in node:
            ref_id = node['ref']
            
            # Handle multi-output references with format 'id.output'
            if '.' in ref_id:
                base_id, output_key = ref_id.split('.', 1)
                if base_id in self._evaluated_components and isinstance(self._evaluated_components[base_id], dict):
                    return self._evaluated_components[base_id][output_key]
                else:
                    raise ValueError(f"Invalid reference: {ref_id}. Either {base_id} doesn't exist or isn't a multi-output component.")
            
            return self._evaluated_components[ref_id]
            
        component = self._create_component(node, component_source)
        
        # Evaluate inputs if component needs them
        if 'inputs' in node:
            input_results = []
            for input_config in node['inputs']:
                input_result = self._evaluate_node(input_config, data, component_source)
                input_results.append(input_result)
            result = component.evaluate(*input_results)
        else:
            result = component.calculate(data)
            
        # Cache result if node has an ID
        if 'id' in node:
            self._evaluated_components[node['id']] = result
            
            # If this is a multi-output indicator, also create individual references
            # for each output with id.output_key format
            if isinstance(result, dict):
                for output_key, output_value in result.items():
                    self._evaluated_components[f"{node['id']}.{output_key}"] = output_value
            
        return result

    def evaluate_for_symbol(self, symbol: str, config: Dict[str, Any], data: pd.DataFrame) -> pd.Series:
        """
        Evaluate a complete signal chain for one symbol.
        
        The evaluation follows this process:
        1. Initialize component cache
        2. Evaluate all indicators in order
        3. Evaluate all signals using indicator results
        4. Convert boolean signals to trade states
        
        Args:
            symbol: Symbol being evaluated
            config: Strategy configuration with indicators and signals
            data: Historical market data
            
        Returns:
            pd.Series: Generated trade signals (EMPTY, BUY, SELL, HOLD)
        """
        # Initialize evaluation cache
        self._evaluated_components = {
            '_symbol': symbol,
            '_data': data
        }
        
        # Calculate indicators (metrics and technical analysis)
        for indicator_config in config.get('indicators', []):
            result = self._evaluate_node(indicator_config, data, 'indicators')
            if 'id' in indicator_config:
                self._evaluated_components[indicator_config['id']] = result

        # Calculate signals (logical conditions)
        signal_results = {}
        for signal_config in config.get('signals', []):
            result = self._evaluate_node(signal_config, data, 'signals')
            if 'id' in signal_config:
                signal_results[signal_config['id']] = result
            
        # Generate final trade signals
        return self._convert_to_trade_signals(signal_results, config['outputs'])

    def _convert_to_trade_signals(self, signal_results: Dict[str, pd.Series], 
                                outputs: Dict[str, str]) -> pd.Series:
        """
        Convert boolean signals to trade states following state machine rules.
        
        State Transitions:
        1. EMPTY/SELL + Buy Signal  -> BUY  -> HOLD
        2. HOLD/BUY  + Sell Signal -> SELL -> EMPTY
        3. No signals = maintain current state
        
        Args:
            signal_results: Boolean signals from strategy evaluation
            outputs: Signal mapping configuration
        
        Returns:
            pd.Series: Final trade signals
        """
        buy_signal = signal_results[outputs['buy_signal']]
        sell_signal = signal_results[outputs.get('sell_signal', '_none')]

        # Initialize state vector
        signals = [TradeSignalState.EMPTY.value] * len(buy_signal)
        last_state = TradeSignalState.EMPTY.value
        
        # Process signals sequentially
        for i in range(1, len(signals)):
            # Log signal conflicts for debugging
            if buy_signal.iloc[i] and sell_signal is not None and sell_signal.iloc[i]:
                module_logger.warning(
                    f"Signal conflict detected for {self._evaluated_components.get('_symbol', 'Unknown')} "
                    f"at {buy_signal.index[i]}"
                )
            
            # Apply state transitions
            if sell_signal is not None and sell_signal.iloc[i]:
                if last_state in [TradeSignalState.HOLD.value, TradeSignalState.BUY.value]:
                    signals[i] = TradeSignalState.SELL.value
                    last_state = TradeSignalState.EMPTY.value
                else:
                    signals[i] = TradeSignalState.EMPTY.value
            elif buy_signal.iloc[i]:
                if last_state in [TradeSignalState.EMPTY.value, TradeSignalState.SELL.value]:
                    signals[i] = TradeSignalState.BUY.value
                    last_state = TradeSignalState.HOLD.value
                elif last_state == TradeSignalState.HOLD.value:
                    signals[i] = TradeSignalState.HOLD.value
            else:
                signals[i] = last_state
        
        return pd.Series(signals, index=buy_signal.index)
