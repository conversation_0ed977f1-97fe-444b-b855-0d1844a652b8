"""
Mathematical operation signals for trading strategies.

This module provides signal primitives that perform mathematical operations
on time series data, such as addition, subtraction, multiplication, and division.
These signals are useful for creating custom calculations and combinations of indicators.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Union, Optional

from components.base.primitives import BaseSignalPrimitive


class MathOperation(BaseSignalPrimitive):
    """Base class for mathematical operations.
    
    This class provides the foundation for all mathematical operation signals.
    It handles the common functionality of performing operations and comparing
    results against a threshold or another series.
    
    Attributes:
        params (Dict[str, Any]): Configuration parameters including:
            - operation (str): Type of operation to perform ('add', 'subtract', 'multiply', 'divide')
            - comparison (str): Type of comparison ('greater', 'less', 'equal', 'greater_equal', 'less_equal')
            - threshold (float, optional): Value to compare result against (if not comparing against another series)
            - absolute (bool): Whether to take the absolute value of the result before comparison
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        return {
            'operation': 'add',  # 'add', 'subtract', 'multiply', 'divide'
            'comparison': 'greater',  # 'greater', 'less', 'equal', 'greater_equal', 'less_equal'
            'threshold': None,  # Used when comparing result to a fixed value
            'absolute': False,  # Whether to take the absolute value of the result before comparison
        }
    
    def validate_params(self) -> None:
        """Validate the current parameter values.
        
        Raises:
            ValueError: If any parameters are invalid
        """
        valid_operations = ['add', 'subtract', 'multiply', 'divide']
        if self.params['operation'] not in valid_operations:
            raise ValueError(f"Operation must be one of {valid_operations}, got {self.params['operation']}")
        
        valid_comparisons = ['greater', 'less', 'equal', 'greater_equal', 'less_equal']
        if self.params['comparison'] not in valid_comparisons:
            raise ValueError(f"Comparison must be one of {valid_comparisons}, got {self.params['comparison']}")
        
        # threshold can be None (if comparing against another series)
        if self.params['threshold'] is not None and not isinstance(self.params['threshold'], (int, float)):
            raise ValueError(f"Threshold must be a number or None, got {type(self.params['threshold'])}")
        
        if not isinstance(self.params['absolute'], bool):
            raise ValueError(f"Absolute must be a boolean, got {type(self.params['absolute'])}")
    
    def _perform_operation(self, a: pd.Series, b: Union[pd.Series, float]) -> pd.Series:
        """Perform the specified mathematical operation.
        
        Args:
            a (pd.Series): First operand
            b (Union[pd.Series, float]): Second operand (Series or scalar)
            
        Returns:
            pd.Series: Result of the operation
            
        Raises:
            ValueError: If operation is invalid
        """
        operation = self.params['operation']
        
        if operation == 'add':
            result = a + b
        elif operation == 'subtract':
            result = a - b
        elif operation == 'multiply':
            result = a * b
        elif operation == 'divide':
            # 特殊处理除法，确保除零结果为 NaN
            with np.errstate(divide='ignore', invalid='ignore'):
                if isinstance(b, pd.Series):
                    # 创建一个掩码，标记除数为零或 NaN 的位置
                    zero_mask = (b == 0) | pd.isna(b) | pd.isna(a)
                    
                    # 执行除法操作
                    result = a / b
                    
                    # 使用mask方法设置NaN值，更清晰且不会修改原始Series
                    result = result.mask(zero_mask, np.nan)
                else:
                    # 如果除数是标量
                    if b == 0 or pd.isna(b):
                        # 如果除数为零或 NaN，所有结果都是 NaN
                        result = pd.Series(np.nan, index=a.index)
                    else:
                        result = a / b
        else:
            raise ValueError(f"Invalid operation: {operation}")
        
        # Apply absolute value if specified
        if self.params['absolute']:
            result = result.abs()
            
        return result
    
    def _compare_result(self, result: pd.Series, threshold=None) -> pd.Series:
        """Compare the result against a threshold or another series.
        
        Args:
            result (pd.Series): Result of the mathematical operation
            threshold: Value or Series to compare against (None uses self.params['threshold'])
            
        Returns:
            pd.Series: Boolean Series with comparison results, preserving NaN values
        """
        comparison = self.params['comparison']
        
        # If no threshold is provided, use the one from params
        if threshold is None:
            threshold = self.params['threshold']
        
        # 保存结果的索引，以便确保返回 Series 而不是 numpy array
        index = result.index
        
        # 创建一个掩码标记 NaN 值，以便在比较后保留这些 NaN
        nan_mask = pd.isna(result)
        
        # 初始化结果 Series，暂时使用对象类型以兼容布尔和NaN
        comparison_result = pd.Series(False, index=index, dtype='object')
        
        # 对非 NaN 的值进行比较
        valid_data = result[~nan_mask]
        
        if len(valid_data) > 0:  # 确保有有效数据进行比较
            # 预先计算比较结果，而不是直接赋值
            if comparison == 'greater':
                comp_values = valid_data > threshold
            elif comparison == 'less':
                comp_values = valid_data < threshold
            elif comparison == 'equal':
                # Use isclose for floating point equality
                if isinstance(threshold, pd.Series):
                    comp_values = np.isclose(valid_data, threshold[~nan_mask] if isinstance(threshold, pd.Series) else threshold)
                else:
                    comp_values = np.isclose(valid_data, threshold)
            elif comparison == 'greater_equal':
                comp_values = valid_data >= threshold
            elif comparison == 'less_equal':
                comp_values = valid_data <= threshold
            else:
                raise ValueError(f"Invalid comparison: {comparison}")
                
            # 对于非NaN的部分，通过loc赋值，避免类型不兼容警告
            # 用numpy数组的情况下直接使用comp_values，如果是Series则使用.values
            if isinstance(comp_values, pd.Series):
                comparison_result.loc[~nan_mask] = comp_values.values
            else:
                comparison_result.loc[~nan_mask] = comp_values
        
        # 将 NaN 值保留在结果中，使用loc赋值避免警告
        if nan_mask.any():
            comparison_result.loc[nan_mask] = np.nan
            
        # 处理布尔类型与NaN混合时的兼容性问题
        # 如果有NaN值，使用pandas的NA类型系统
        if nan_mask.any():
            # 创建一个带有NA的布尔Series
            final_result = pd.Series(False, index=index, dtype=pd.BooleanDtype())
            
            # 将非NaN的值设置为正确的布尔值
            non_na_vals = comparison_result.dropna().astype(bool)
            final_result.loc[non_na_vals.index] = non_na_vals
            
            # 将NaN对应位置的值设置为NA（pandas的空值）
            final_result.loc[nan_mask] = pd.NA
        else:
            # 如果没有NaN值，直接转换为布尔型
            final_result = comparison_result.astype(bool)
            
        return final_result
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate the mathematical operation and comparison.
        
        Args:
            *inputs (pd.Series): Input series to evaluate. 
                If threshold is None, at least 3 inputs are required:
                - First input: First operand of the operation
                - Second input: Second operand of the operation
                - Third input: Threshold to compare against
                
                If threshold is provided in params, at least 2 inputs are required:
                - First input: First operand of the operation
                - Second input: Second operand of the operation
                
            date (Optional[pd.Timestamp], optional): Specific date to evaluate.
                If None, evaluates for all dates in the series.
                
        Returns:
            Union[bool, pd.Series]: Boolean result of the comparison
            
        Raises:
            ValueError: If inputs are invalid
        """
        # Check inputs
        if self.params['threshold'] is None and len(inputs) < 3:
            raise ValueError("At least 3 inputs required when threshold is not provided")
        elif len(inputs) < 2:
            raise ValueError("At least 2 inputs required")
        
        # Get first and second operands
        a = inputs[0]
        b = inputs[1]
        
        # Perform the operation
        result = self._perform_operation(a, b)
        
        # Get threshold for comparison
        threshold = self.params['threshold']
        if threshold is None and len(inputs) >= 3:
            threshold = inputs[2]
        
        # Perform the comparison
        comparison_result = self._compare_result(result, threshold)
        
        # Return result for specific date if requested
        if date is not None:
            if date in comparison_result.index:
                return comparison_result.loc[date]
            else:
                # Handle case where date is not in the index
                return False
        
        return comparison_result


class Add(MathOperation):
    """Signal that adds two time series and compares the result.
    
    This signal adds two input series and compares the result against 
    either a threshold value or a third input series.
    
    Example:
        Add signal to check if the sum of two indicators is greater than a threshold:
        
        add_signal = Add(params={
            'comparison': 'greater',
            'threshold': 50
        })
        result = add_signal.evaluate(series1, series2)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        params = super().get_default_params()
        params['operation'] = 'add'
        return params


class Subtract(MathOperation):
    """Signal that subtracts one time series from another and compares the result.
    
    This signal subtracts the second input series from the first and compares 
    the result against either a threshold value or a third input series.
    
    Example:
        Subtract signal to check if the difference between two indicators is less than zero:
        
        subtract_signal = Subtract(params={
            'comparison': 'less',
            'threshold': 0
        })
        result = subtract_signal.evaluate(series1, series2)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        params = super().get_default_params()
        params['operation'] = 'subtract'
        return params


class Multiply(MathOperation):
    """Signal that multiplies two time series and compares the result.
    
    This signal multiplies two input series and compares the result against 
    either a threshold value or a third input series.
    
    Example:
        Multiply signal to check if the product of two indicators is greater than a threshold:
        
        multiply_signal = Multiply(params={
            'comparison': 'greater',
            'threshold': 100
        })
        result = multiply_signal.evaluate(series1, series2)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        params = super().get_default_params()
        params['operation'] = 'multiply'
        return params


class Divide(MathOperation):
    """Signal that divides one time series by another and compares the result.
    
    This signal divides the first input series by the second and compares 
    the result against either a threshold value or a third input series.
    Special handling is included to safely handle division by zero.
    
    Example:
        Divide signal to check if the ratio of two indicators is greater than 1:
        
        divide_signal = Divide(params={
            'comparison': 'greater',
            'threshold': 1.0
        })
        result = divide_signal.evaluate(series1, series2)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        params = super().get_default_params()
        params['operation'] = 'divide'
        return params
