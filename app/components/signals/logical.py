"""
Logical signal primitives.

This module provides logical operators for combining signal primitives,
including AND, OR, NOT operations for boolean series.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional

from components.base.primitives import BaseSignalPrimitive

module_logger = logging.getLogger(__name__)


class And(BaseSignalPrimitive):
    """Logical AND operator for signal primitives.
    
    Signal is True when both input signals are True.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - fill_method (str): Method to handle NaN values ('ffill', 'bfill', or None)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'fill_method': None  # Default to not filling NaN values
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If fill_method is not a valid option
        """
        if self.params['fill_method'] not in [None, 'ffill', 'bfill']:
            raise ValueError("fill_method must be one of: None, 'ffill', 'bfill'")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate logical AND condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series to combine with AND
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True when both input signals are True
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("And requires exactly two input series")
        
        series_a, series_b = inputs
        
        # Convert to boolean Series and handle NaN values
        bool_a = self._prepare_series(series_a)
        bool_b = self._prepare_series(series_b)
        
        # Apply logical AND
        result = bool_a & bool_b
        
        # Handle specific date request
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
    
    def _prepare_series(self, series: pd.Series) -> pd.Series:
        """Prepare a Series for logical operation.
        
        Args:
            series (pd.Series): Input series
            
        Returns:
            pd.Series: Boolean series with NaN handling applied
        """
        prepared_series = series.copy() # Work on a copy
        fill_method = self.params.get('fill_method') # Use .get for safety

        # Apply fill method if specified
        if fill_method == 'ffill':
            prepared_series = prepared_series.ffill()
        elif fill_method == 'bfill':
            prepared_series = prepared_series.bfill()

        # Always fill any remaining NaNs (or original NaNs if no fill method) with False BEFORE boolean conversion
        prepared_series = prepared_series.fillna(False)

        # Now convert to boolean safely
        bool_series = prepared_series.astype(bool)

        return bool_series


class Or(BaseSignalPrimitive):
    """Logical OR operator for signal primitives.
    
    Signal is True when either input signal is True.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - fill_method (str): Method to handle NaN values ('ffill', 'bfill', or None)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'fill_method': None  # Default to not filling NaN values
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If fill_method is not a valid option
        """
        if self.params['fill_method'] not in [None, 'ffill', 'bfill']:
            raise ValueError("fill_method must be one of: None, 'ffill', 'bfill'")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate logical OR condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series to combine with OR
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True when either input signal is True
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("Or requires exactly two input series")
        
        series_a, series_b = inputs
        
        # Convert to boolean Series and handle NaN values
        bool_a = self._prepare_series(series_a)
        bool_b = self._prepare_series(series_b)
        
        # Apply logical OR
        result = bool_a | bool_b
        
        # Handle specific date request
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
    
    def _prepare_series(self, series: pd.Series) -> pd.Series:
        """Prepare a Series for logical operation.
        
        Args:
            series (pd.Series): Input series
            
        Returns:
            pd.Series: Boolean series with NaN handling applied
        """
        prepared_series = series.copy() # Work on a copy
        fill_method = self.params.get('fill_method') # Use .get for safety

        # Apply fill method if specified
        if fill_method == 'ffill':
            prepared_series = prepared_series.ffill()
        elif fill_method == 'bfill':
            prepared_series = prepared_series.bfill()

        # Always fill any remaining NaNs (or original NaNs if no fill method) with False BEFORE boolean conversion
        prepared_series = prepared_series.fillna(False)

        # Now convert to boolean safely
        bool_series = prepared_series.astype(bool)

        return bool_series


class Not(BaseSignalPrimitive):
    """Logical NOT operator for signal primitives.
    
    Signal is True when the input signal is False, and vice versa.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - fill_method (str): Method to handle NaN values ('ffill', 'bfill', or None)
            - preserve_na (bool): If True, NaN values remain NaN in the output
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'fill_method': None,  # Default to not filling NaN values
            'preserve_na': False  # Default to converting NaN to not(False) = True
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If fill_method is not a valid option
            ValueError: If preserve_na is not a boolean
        """
        if self.params['fill_method'] not in [None, 'ffill', 'bfill']:
            raise ValueError("fill_method must be one of: None, 'ffill', 'bfill'")
            
        if not isinstance(self.params['preserve_na'], bool):
            raise ValueError("preserve_na must be a boolean value")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate logical NOT condition.
        
        Args:
            *inputs (pd.Series): Exactly one Series to negate
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True when input signal is False, False when input signal is True
            
        Raises:
            ValueError: If not exactly one input provided
        """
        if len(inputs) != 1:
            raise ValueError("Not requires exactly one input series")
        
        series = inputs[0]
        
        # Save original NaN positions if needed
        if self.params['preserve_na']:
            na_mask = series.isna()
        
        # Convert to boolean Series and handle NaN values
        bool_series = self._prepare_series(series)
        
        # Apply logical NOT
        result = ~bool_series
        
        # Restore NaN values if requested
        if self.params['preserve_na']:
            # 将结果转换为object类型，然后才设置NaN值，避免bool和NaN的类型不兼容
            result = result.astype('object')
            result[na_mask] = np.nan
        
        # Handle specific date request
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
    
    def _prepare_series(self, series: pd.Series) -> pd.Series:
        """Prepare a Series for logical operation.
        
        Args:
            series (pd.Series): Input series
            
        Returns:
            pd.Series: Boolean series with NaN handling applied
        """
        # Important: We need to handle NaN values BEFORE converting to boolean
        # Otherwise NaNs are lost during the conversion to boolean
        
        # Handle NaN values according to fill_method
        fill_method = self.params['fill_method']
        if fill_method == 'ffill':
            # Forward fill NaN values first
            filled_series = series.ffill()
        elif fill_method == 'bfill':
            # Backward fill NaN values first
            filled_series = series.bfill()
        else:
            filled_series = series.copy()
        
        # Now convert to boolean after filling
        bool_series = filled_series.astype(bool)
        
        # If we don't want to preserve NaNs and they weren't filled, fill them with False
        if not self.params['preserve_na'] and not fill_method:
            bool_series = bool_series.fillna(False)
        
        return bool_series
