"""
Comparison signal primitives.

This module provides signal primitives for comparing series data,
including crossovers and threshold comparisons.
"""

import logging
import pandas as pd
from typing import Dict, Any, Union

from components.base.primitives import BaseSignalPrimitive

module_logger = logging.getLogger(__name__)

class Crossover(BaseSignalPrimitive):
    """Detects when one series crosses above another.

    Signal is True when series_a crosses above series_b.
    Supports two modes:
    - 'cross': Requires actual crossing (previous <= and current >)
    - 'simple': Simple comparison (current >), used to replicate DualMovingAverageStrategy
    """

    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.

        Returns:
            Dict[str, Any]: Default parameters with mode='cross'
        """
        return {
            'mode': 'cross'  # Default to standard crossover behavior
        }

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate crossover condition.

        Args:
            *inputs (pd.Series): Exactly two Series to compare
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: True based on selected comparison mode

        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("Crossover requires exactly two input series")

        series_a, series_b = inputs
        mode = self.params.get('mode', 'cross')

        module_logger.info(f"Evaluating Crossover with mode={mode} for symbol {self.symbol if hasattr(self, 'symbol') else 'unknown'}")

        if mode == 'simple':
            # Simple comparison mode - replicates DualMovingAverageStrategy behavior
            result = series_a > series_b
            result = result.fillna(False)
        else:
            # Standard crossover mode - requires an actual crossing
            curr_greater = series_a > series_b
            prev_less_eq = series_a.shift(1) <= series_b.shift(1)

            # For first point, we can't determine a crossover since we don't know previous value
            prev_less_eq.iloc[0] = False

            # Convert NaN values to False to prevent incorrect signals
            curr_greater = curr_greater.fillna(False)
            prev_less_eq = prev_less_eq.fillna(False)

            # Crossover occurs when current is greater and previous was less or equal
            result = curr_greater & prev_less_eq

            # Handle NaN values in input series
            result = result.mask(series_a.isna() | series_b.isna())

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result

class Crossunder(BaseSignalPrimitive):
    """Detects when one series crosses below another.

    Signal is True when series_a crosses below series_b.
    Supports two modes:
    - 'cross': Requires actual crossing (previous >= and current <)
    - 'simple': Simple comparison (current <), used to replicate DualMovingAverageStrategy
    """

    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.

        Returns:
            Dict[str, Any]: Default parameters with mode='cross'
        """
        return {
            'mode': 'cross'  # Default to standard crossunder behavior
        }

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate crossunder condition.

        Args:
            *inputs (pd.Series): Exactly two Series to compare
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: True based on selected comparison mode

        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("Crossunder requires exactly two input series")

        series_a, series_b = inputs
        mode = self.params.get('mode', 'cross')

        module_logger.info(f"Evaluating Crossunder with mode={mode} for symbol {self.symbol if hasattr(self, 'symbol') else 'unknown'}")

        if mode == 'simple':
            # Simple comparison mode - replicates DualMovingAverageStrategy behavior
            result = series_a < series_b
            result = result.fillna(False)
        else:
            # Standard crossunder mode - requires an actual crossing
            curr_less = series_a < series_b
            prev_greater_eq = series_a.shift(1) >= series_b.shift(1)

            # For first point, we can't determine a crossunder
            prev_greater_eq.iloc[0] = False

            # Convert NaN values to False
            curr_less = curr_less.fillna(False)
            prev_greater_eq = prev_greater_eq.fillna(False)

            # Crossunder occurs when current is less and previous was greater or equal
            result = curr_less & prev_greater_eq

            # Handle NaN values
            result = result.mask(series_a.isna() | series_b.isna())

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result

class LessThan(BaseSignalPrimitive):
    """Compares if one series is less than another.

    Signal is True when series_a is less than series_b.

    Parameters:
        epsilon (float, optional): Small value for floating point comparison tolerance.
            When comparing a < b, the actual comparison will be a < (b - epsilon).
            Default is 0 (exact comparison).
    """

    def __init__(self, **kwargs):
        """Initialize with optional epsilon parameter."""
        # Extract epsilon before passing kwargs to parent
        try:
            self.epsilon = float(kwargs.pop('epsilon', 0))
        except (ValueError, TypeError):
            module_logger.error(f"Invalid epsilon value provided for {self.__class__.__name__}: {kwargs.get('epsilon')}")
            self.epsilon = 0  # Default to 0 on error
        super().__init__(**kwargs)

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate less than condition.

        Args:
            *inputs (pd.Series): Exactly two Series to compare
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: True when first series is less than second

        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("LessThan requires exactly two input series")

        series_a, series_b = inputs
        # Apply epsilon for floating point comparison
        if self.epsilon > 0:
            result = series_a < (series_b - self.epsilon)
        else:
            result = series_a < series_b

        # Handle NaN values
        result = result.mask(series_a.isna() | series_b.isna())

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result


class GreaterThan(BaseSignalPrimitive):
    """Compares if one series is greater than another.

    Signal is True when series_a is greater than series_b.

    Parameters:
        epsilon (float, optional): Small value for floating point comparison tolerance.
            When comparing a > b, the actual comparison will be a > (b + epsilon).
            Default is 0 (exact comparison).
    """

    def __init__(self, **kwargs):
        """Initialize with optional epsilon parameter."""
        # Extract epsilon before passing kwargs to parent
        try:
            self.epsilon = float(kwargs.pop('epsilon', 0))
        except (ValueError, TypeError):
            module_logger.error(f"Invalid epsilon value provided for {self.__class__.__name__}: {kwargs.get('epsilon')}")
            self.epsilon = 0  # Default to 0 on error
        super().__init__(**kwargs)

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate greater than condition.

        Args:
            *inputs (pd.Series): Exactly two Series to compare
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: True when first series is greater than second

        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("GreaterThan requires exactly two input series")

        series_a, series_b = inputs
        # Apply epsilon for floating point comparison
        if self.epsilon > 0:
            result = series_a > (series_b + self.epsilon)
        else:
            result = series_a > series_b

        # Handle NaN values
        result = result.mask(series_a.isna() | series_b.isna())

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result


class Comparison(BaseSignalPrimitive):
    """General comparison signal primitive.

    Compares values using a specified comparison operator.
    Supports different comparison types and an optional threshold value.

    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - comparison (str): Type of comparison ('greater', 'less', 'equal', 'greater_equal', 'less_equal', 'not_equal')
            - threshold (float, optional): Threshold value for comparison when only one input is provided
            - epsilon (float, optional): Small value to avoid floating point precision issues in comparisons
    """

    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.

        Returns:
            Dict[str, Any]: Default parameters with comparison='greater' and epsilon=1e-10
        """
        return {
            'comparison': 'greater',
            'epsilon': 1e-10  # 添加一个小的精度值
        }

    def validate_params(self) -> None:
        """Validate parameters.

        Raises:
            ValueError: If comparison is not one of the supported types
        """
        valid_comparisons = ['greater', 'less', 'equal', 'greater_equal', 'less_equal', 'not_equal']
        if self.params['comparison'] not in valid_comparisons:
            raise ValueError(f"comparison must be one of: {', '.join(valid_comparisons)}")

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate comparison condition.

        Handles two cases:
        1. One input + threshold: Compares the input against the threshold value
        2. Two inputs: Compares first input against second input

        Args:
            *inputs (pd.Series): One or two Series to compare
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: Result of the comparison

        Raises:
            ValueError: If not one or two inputs provided, or threshold missing for single input
        """
        if len(inputs) < 1 or len(inputs) > 2:
            raise ValueError("Comparison requires one or two input series")

        comparison = self.params['comparison']

        # Case 1: One input + threshold parameter
        if len(inputs) == 1:
            if 'threshold' not in self.params:
                raise ValueError("Threshold parameter is required when using a single input")

            threshold = self.params['threshold']
            series_a = inputs[0]

            if comparison == 'greater':
                result = series_a > threshold
            elif comparison == 'less':
                result = series_a < threshold
            elif comparison == 'equal':
                result = series_a == threshold
            elif comparison == 'greater_equal':
                result = series_a >= threshold
            elif comparison == 'less_equal':
                result = series_a <= threshold
            elif comparison == 'not_equal':
                result = series_a != threshold

        # Case 2: Two inputs - directly compare series_a with series_b
        else:
            series_a, series_b = inputs

            epsilon = self.params.get('epsilon', 1e-10)

            if comparison == 'greater':
                result = series_a > (series_b + epsilon)  # 严格大于需要超过一个epsilon
            elif comparison == 'less':
                result = series_a < (series_b - epsilon)  # 严格小于需要小于一个epsilon
            elif comparison == 'equal':
                result = (series_a - series_b).abs() <= epsilon  # 相等允许差一个epsilon
            elif comparison == 'greater_equal':
                result = series_a >= (series_b - epsilon)
            elif comparison == 'less_equal':
                result = series_a <= (series_b + epsilon)
            elif comparison == 'not_equal':
                result = (series_a - series_b).abs() > epsilon

        # Handle NaN values
        if len(inputs) == 1:
            result = result.mask(inputs[0].isna())
        else:
            result = result.mask(inputs[0].isna() | inputs[1].isna())

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result


class InRange(BaseSignalPrimitive):
    """Check if a series is within a specified range.

    Signal is True when the value is between lower and upper bounds.
    Supports inclusive and exclusive range boundaries.

    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - include_lower (bool): Whether to include the lower bound (default: True)
            - include_upper (bool): Whether to include the upper bound (default: True)
    """

    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.

        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'include_lower': True,
            'include_upper': True
        }

    def validate_params(self) -> None:
        """Validate parameters.

        Raises:
            ValueError: If include_lower or include_upper is not a boolean
        """
        if not isinstance(self.params['include_lower'], bool):
            raise ValueError("include_lower must be a boolean value")

        if not isinstance(self.params['include_upper'], bool):
            raise ValueError("include_upper must be a boolean value")

    def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None) -> Union[bool, pd.Series]:
        """Evaluate if values are within the specified range.

        Args:
            *inputs (pd.Series): Three Series: target, lower_bound, upper_bound
            date (pd.Timestamp, optional): Specific date to evaluate

        Returns:
            Union[bool, pd.Series]: True when the target is within the range

        Raises:
            ValueError: If not exactly three inputs provided
        """
        if len(inputs) != 3:
            raise ValueError("InRange requires exactly three input series: target, lower_bound, upper_bound")

        target, lower_bound, upper_bound = inputs
        include_lower = self.params['include_lower']
        include_upper = self.params['include_upper']

        # Check lower boundary condition
        if include_lower:
            lower_check = target >= lower_bound
        else:
            lower_check = target > lower_bound

        # Check upper boundary condition
        if include_upper:
            upper_check = target <= upper_bound
        else:
            upper_check = target < upper_bound

        # Combine conditions
        result = lower_check & upper_check

        # Handle NaN values
        result = result.mask(target.isna() | lower_bound.isna() | upper_bound.isna())

        module_logger.debug(f"InRange evaluation with include_lower={include_lower}, include_upper={include_upper}")

        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
