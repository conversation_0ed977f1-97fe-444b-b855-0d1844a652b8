"""
Stock-Bond switching signal primitives.

This module provides signal primitives for implementing stock-bond switching strategies.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional
import inspect

from components.base.primitives import BaseSignalPrimitive

module_logger = logging.getLogger(__name__)


class StockBondSwitch(BaseSignalPrimitive):
    """Stock-Bond switching signal primitive.
    
    This primitive implements a stock-bond switching strategy where:
    - When condition is True: hold stock ETF (symbols[0])
    - When condition is False: hold bond ETF (symbols[1])
    
    The primitive returns appropriate signals based on the current symbol being evaluated
    and the market condition.
    
    Convention:
    - symbols[0] should be the stock ETF 
    - symbols[1] should be the bond ETF
    
    Parameters:
        default_to_stock (bool): Whether to default to stock when condition is unavailable (default: True)
    """
    
    # 设置该原语需要全局上下文
    REQUIRES_GLOBAL_CONTEXT: bool = True
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'default_to_stock': True
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If default_to_stock is not a boolean
        """
        if not isinstance(self.params['default_to_stock'], bool):
            raise ValueError("default_to_stock must be a boolean")
    
    def _get_current_symbol_context(self):
        """
        获取当前符号和符号池，以确定位置
        
        Returns:
            tuple: (current_symbol, all_symbols) 或 (None, None) 如果无法确定
        """
        try:
            frame = inspect.currentframe()
            
            current_symbol = None
            all_symbols = None
            
            # 遍历调用栈
            while frame:
                frame = frame.f_back
                if frame and 'self' in frame.f_locals:
                    obj = frame.f_locals['self']
                    
                    # 从SignalEvaluator获取当前符号
                    if hasattr(obj, '_evaluated_components') and '_symbol' in obj._evaluated_components:
                        current_symbol = obj._evaluated_components['_symbol']
                    
                    # 从CompositeStrategy获取符号池
                    if hasattr(obj, 'all_symbols') and obj.all_symbols:
                        all_symbols = obj.all_symbols
                    
                    # 如果两个都找到了，返回结果
                    if current_symbol and all_symbols:
                        return current_symbol, all_symbols
                         
            return None, None
        except Exception as e:
            module_logger.warning(f"Failed to get symbol context: {e}")
            return None, None
    
    def evaluate(self, condition_signal: pd.Series) -> pd.Series:
        """
        Evaluate the stock-bond switching logic.
        
        Args:
            condition_signal: Boolean series indicating market condition
                            (True = favorable for stocks, False = favorable for bonds)
        
        Returns:
            pd.Series: Boolean series indicating whether to buy current symbol
        """
        current_symbol, all_symbols = self._get_current_symbol_context()
        
        if not current_symbol or not all_symbols:
            module_logger.warning("Cannot determine symbol context, defaulting to False")
            return pd.Series(False, index=condition_signal.index)
        
        if len(all_symbols) < 2:
            module_logger.warning(f"Stock-bond switching requires at least 2 symbols, got {len(all_symbols)}")
            return pd.Series(False, index=condition_signal.index)
        
        # 按照约定：位置0 = 股票ETF, 位置1 = 债券ETF
        stock_symbol = all_symbols[0]
        bond_symbol = all_symbols[1]
        
        module_logger.info(f"StockBondSwitch: {current_symbol}, Stock={stock_symbol}, Bond={bond_symbol}")
        
        # 处理condition_signal中的NaN值
        filled_condition = condition_signal.fillna(self.params['default_to_stock'])
        
        # 根据当前symbol和市场条件返回信号
        if current_symbol == stock_symbol:
            # 对于股票ETF：当条件为True时买入
            return filled_condition
        elif current_symbol == bond_symbol:
            # 对于债券ETF：当条件为False时买入  
            return ~filled_condition
        else:
            # 对于其他symbol，不发出信号
            module_logger.warning(f"Symbol {current_symbol} is neither stock nor bond ETF")
            return pd.Series(False, index=condition_signal.index) 