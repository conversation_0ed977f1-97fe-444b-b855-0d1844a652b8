"""
Momentum signal primitives.

This module provides signal primitives for momentum-based trading strategies,
including rate of change and trend analysis.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional

from components.base.primitives import BaseSignalPrimitive

module_logger = logging.getLogger(__name__)


class PercentChange(BaseSignalPrimitive):
    """Calculate percent change signal over specified period.
    
    Signal is True when the percent change meets the threshold criteria.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - periods (int): Number of periods to calculate change over
            - threshold (float): Value to compare percent change against
            - comparison (str): Comparison type ('greater', 'less', 'equal')
            - is_absolute (bool): Whether to use absolute value of percent change
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'periods': 1,            # Default to 1-period change
            'threshold': 0,          # Default threshold of 0%
            'comparison': 'greater', # Default to 'greater than' comparison
            'is_absolute': False     # Default to use actual (not absolute) change
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If any parameters are invalid
        """
        if not isinstance(self.params['periods'], int) or self.params['periods'] < 1:
            raise ValueError("periods must be a positive integer")
            
        if not isinstance(self.params['threshold'], (int, float)):
            raise ValueError("threshold must be a number")
            
        if self.params['comparison'] not in ['greater', 'less', 'equal']:
            raise ValueError("comparison must be one of: 'greater', 'less', 'equal'")
            
        if not isinstance(self.params['is_absolute'], bool):
            raise ValueError("is_absolute must be a boolean value")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate percent change signal.
        
        Args:
            *inputs (pd.Series): Single Series to analyze for percent change
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True when the percent change meets the criteria
            
        Raises:
            ValueError: If not exactly one input provided
        """
        if len(inputs) != 1:
            raise ValueError("PercentChange requires exactly one input series")
        
        series = inputs[0]
        periods = self.params['periods']
        threshold = self.params['threshold']
        comparison = self.params['comparison']
        is_absolute = self.params['is_absolute']
        
        # Calculate percent change over specified period (returns in decimal form)
        # Use 'fill_method=None' to avoid FutureWarning and handle NaNs explicitly
        pct_change = series.pct_change(periods=periods, fill_method=None)
        
        # Convert to percentage form (multiply by 100)
        pct_change = pct_change * 100
        
        # Use absolute value if requested
        if is_absolute:
            pct_change = pct_change.abs()
        
        # Apply the specified comparison
        if comparison == 'greater':
            result = pct_change > threshold
        elif comparison == 'less':
            result = pct_change < threshold
        else:  # 'equal'
            # For floating point, exact equality is rare, use a small tolerance
            tolerance = 1e-10
            result = (pct_change - threshold).abs() < tolerance
        
        # Handle NaN values - in the test case all NaN positions should be False
        result = result.fillna(False)
        
        module_logger.debug(
            f"PercentChange evaluation with periods={periods}, threshold={threshold}, "
            f"comparison='{comparison}', is_absolute={is_absolute}"
        )
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
