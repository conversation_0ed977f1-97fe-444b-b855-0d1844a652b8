"""
Composite signal primitives.

This module provides more complex signal compositions, including:
- CrossAbove: Detects when one signal crosses above another
- CrossBelow: Detects when one signal crosses below another
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional

from components.base.primitives import BaseSignalPrimitive

module_logger = logging.getLogger(__name__)


class CrossAbove(BaseSignalPrimitive):
    """CrossAbove signal primitive.
    
    Detects when one signal crosses above another signal.
    True when signal_a was below signal_b in previous period and is now above signal_b.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - threshold (float): Additional threshold to require for crossing (default: 0.0)
            - strict (bool): If True, requires the prior period to be strictly less than
                            rather than less than or equal to (default: False)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'threshold': 0.0,  # Additional value that must be exceeded
            'strict': False    # If True, use < rather than <= for prior period
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If threshold is not a number
            ValueError: If strict is not a boolean
        """
        if not isinstance(self.params['threshold'], (int, float)):
            raise ValueError("threshold must be a number")
            
        if not isinstance(self.params['strict'], bool):
            raise ValueError("strict must be a boolean")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate CrossAbove condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series inputs
                - inputs[0]: The signal that may cross above
                - inputs[1]: The signal to cross above
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True at positions where CrossAbove condition is met
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("CrossAbove requires exactly two input series")
        
        signal_a, signal_b = inputs
        threshold = self.params['threshold']
        
        # Convert to numeric if not already
        if not pd.api.types.is_numeric_dtype(signal_a):
            signal_a = pd.to_numeric(signal_a, errors='coerce')
        if not pd.api.types.is_numeric_dtype(signal_b):
            signal_b = pd.to_numeric(signal_b, errors='coerce')
        
        # Calculate current condition: signal_a > signal_b + threshold
        current_above = signal_a > (signal_b + threshold)
        
        # Calculate previous condition
        prev_a = signal_a.shift(1)
        prev_b = signal_b.shift(1)
        
        if self.params['strict']:
            # Strictly less than
            prev_below = prev_a < prev_b
        else:
            # Less than or equal to
            prev_below = prev_a <= prev_b
        
        # CrossAbove occurs when current_above is True AND prev_below is True
        cross_above = current_above & prev_below
        
        # First element can't have crossed (no previous value)
        if len(cross_above) > 0:
            cross_above.iloc[0] = False
        
        # Handle specific date request
        if date is not None:
            if date not in cross_above.index:
                return False
            return bool(cross_above.loc[date])
        
        return cross_above


class CrossBelow(BaseSignalPrimitive):
    """CrossBelow signal primitive.
    
    Detects when one signal crosses below another signal.
    True when signal_a was above signal_b in previous period and is now below signal_b.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - threshold (float): Additional threshold to require for crossing (default: 0.0)
            - strict (bool): If True, requires the prior period to be strictly greater than
                            rather than greater than or equal to (default: False)
    """
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'threshold': 0.0,  # Additional value that must be exceeded
            'strict': False    # If True, use > rather than >= for prior period
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If threshold is not a number
            ValueError: If strict is not a boolean
        """
        if not isinstance(self.params['threshold'], (int, float)):
            raise ValueError("threshold must be a number")
            
        if not isinstance(self.params['strict'], bool):
            raise ValueError("strict must be a boolean")
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate CrossBelow condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series inputs
                - inputs[0]: The signal that may cross below
                - inputs[1]: The signal to cross below
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True at positions where CrossBelow condition is met
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("CrossBelow requires exactly two input series")
        
        signal_a, signal_b = inputs
        threshold = self.params['threshold']
        
        # Convert to numeric if not already
        if not pd.api.types.is_numeric_dtype(signal_a):
            signal_a = pd.to_numeric(signal_a, errors='coerce')
        if not pd.api.types.is_numeric_dtype(signal_b):
            signal_b = pd.to_numeric(signal_b, errors='coerce')
        
        # Calculate current condition: signal_a < signal_b - threshold
        current_below = signal_a < (signal_b - threshold)
        
        # Calculate previous condition
        prev_a = signal_a.shift(1)
        prev_b = signal_b.shift(1)
        
        if self.params['strict']:
            # Strictly greater than
            prev_above = prev_a > prev_b
        else:
            # Greater than or equal to
            prev_above = prev_a >= prev_b
        
        # CrossBelow occurs when current_below is True AND prev_above is True
        cross_below = current_below & prev_above
        
        # First element can't have crossed (no previous value)
        if len(cross_below) > 0:
            cross_below.iloc[0] = False
        
        # Handle specific date request
        if date is not None:
            if date not in cross_below.index:
                return False
            return bool(cross_below.loc[date])
        
        return cross_below
