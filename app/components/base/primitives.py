"""
Base primitive components for the strategy primitives framework.

This module defines the core abstract base classes for all primitive components
used in the strategy framework, including indicators and signals.
"""

from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, Any, Optional, Union


class BaseComponentPrimitive(ABC):
    """Base class for all primitive components.
    
    This class provides the foundational functionality for all primitive components,
    including parameter management and validation.
    
    Attributes:
        params (Dict[str, Any]): Dictionary of component parameters
    """
    
    def __init__(self, params: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the component with optional parameters.
        
        Args:
            params (Optional[Dict[str, Any]], optional): Parameters to override defaults.
        """
        self.params = self.get_default_params()
        if params:
            self.params.update(params)
        self.validate_params()
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for the component.
        
        Returns:
            Dict[str, Any]: Dictionary of default parameter values
        """
        return {}
    
    def validate_params(self) -> None:
        """Validate the current parameter values.
        
        Raises:
            ValueError: If any parameters are invalid
        """
        pass
    
    @property
    def name(self) -> str:
        """Get the component's name.
        
        Returns:
            str: The name of the component (default is class name)
        """
        return self.__class__.__name__


class BaseIndicatorPrimitive(BaseComponentPrimitive):
    """Base class for indicator primitives.
    
    This class defines the interface for all technical indicator implementations.
    Indicator primitives take price/volume data as input and produce indicator values.
    """
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the indicator values.
        
        Args:
            data (pd.DataFrame): Price/volume data with at least OHLCV columns
        
        Returns:
            pd.Series: The calculated indicator values
        
        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Indicator primitives must implement calculate method")


class BaseSignalPrimitive(BaseComponentPrimitive):
    """Base class for signal primitives.
    
    This class defines the interface for all signal generation components.
    Signal primitives evaluate conditions and generate boolean signals.
    """

    # 新增类属性，用于声明该原语是否需要全局上下文
    REQUIRES_GLOBAL_CONTEXT: bool = False
    
    @abstractmethod
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate the signal condition.
        
        Args:
            *inputs (pd.Series): Input series to evaluate
            date (Optional[pd.Timestamp], optional): Specific date to evaluate.
                If None, evaluates for all dates in the series.
        
        Returns:
            Union[bool, pd.Series]: If date is provided, returns bool for that date.
                Otherwise returns Series of boolean values.
        
        Raises:
            NotImplementedError: Must be implemented by subclasses
        """
        raise NotImplementedError("Signal primitives must implement evaluate method")
