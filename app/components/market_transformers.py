"""
Market Transformers for investStrategyService.

This module provides transformer classes for market indicators, allowing
for calculations and transformations such as moving averages, percentile
ranking, and relative strength.
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

class BaseMarketTransformer(ABC):
    """
    Base class for all market indicator transformers.

    Market transformers convert raw indicator data into derived signals
    through operations like smoothing, normalization, or comparison.

    Attributes:
        params (Dict[str, Any]): Configuration parameters for the transformer
    """

    def __init__(self, params: Dict[str, Any]):
        """
        Initialize the transformer with parameters.

        Args:
            params: Dictionary of configuration parameters
        """
        self.params = params
        self._validate_params()

    def _validate_params(self) -> None:
        """
        Validate the provided parameters.

        This method should be overridden by subclasses to validate
        their specific parameters.
        """
        pass

    @abstractmethod
    def transform(self, data: pd.DataFrame) -> Union[pd.Series, pd.DataFrame]:
        """
        Apply transformation to market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Transformed data (Series or DataFrame)
        """
        pass

    def _get_field(self, data: pd.DataFrame) -> pd.Series:
        """
        Get the field to transform from the data frame.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the field data
        """
        field = self.params.get('field', 'close')
        if field not in data.columns:
            logger.warning(f"Field '{field}' not found in data, falling back to 'close'")
            field = 'close'

        return data[field]

class MovingAverageTransformer(BaseMarketTransformer):
    """
    Transformer that calculates moving averages of market indicator data.

    Parameters:
        window (int): Size of the moving average window (required)
        field (str): Field to use from the indicator data (default: 'close')
        type (str): Type of moving average ('simple', 'exponential', or 'weighted') (default: 'simple')
    """

    def _validate_params(self) -> None:
        """Validate moving average parameters."""
        if 'window' not in self.params:
            raise ValueError("'window' parameter is required for MovingAverageTransformer")

        if not isinstance(self.params['window'], int) or self.params['window'] <= 0:
            raise ValueError("'window' must be a positive integer")

        # Validate MA type
        valid_types = ['simple', 'exponential', 'weighted']
        ma_type = self.params.get('type', 'simple')
        if ma_type not in valid_types:
            raise ValueError(f"'type' must be one of {valid_types}")

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate moving average of market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the moving average values
        """
        series = self._get_field(data)
        window = self.params['window']
        ma_type = self.params.get('type', 'simple')

        if ma_type == 'simple':
            result = series.rolling(window=window, min_periods=1).mean()
        elif ma_type == 'exponential':
            result = series.ewm(span=window, min_periods=1, adjust=False).mean()
        elif ma_type == 'weighted':
            # 使用 apply 函数时，我们需要处理动态长度的窗口
            def weighted_avg(x):
                # 确保权重与窗口长度匹配
                w = np.arange(1, len(x) + 1)
                return np.sum(w * x) / np.sum(w)

            result = series.rolling(window=window, min_periods=1).apply(
                weighted_avg, raw=True)

        # Name the series appropriately
        field = self.params.get('field', 'close')
        result.name = f"{field}_ma{window}_{ma_type[0]}"

        return result

class PercentileRankTransformer(BaseMarketTransformer):
    """
    Transformer that calculates the percentile rank of market indicator values.

    This is useful for normalizing indicators to a 0-100 scale based on
    historical distributions.

    Parameters:
        lookback (int): Historical period to use for percentile calculation (required)
        field (str): Field to use from the indicator data (default: 'close')
    """

    def _validate_params(self) -> None:
        """Validate percentile rank parameters."""
        if 'lookback' not in self.params:
            raise ValueError("'lookback' parameter is required for PercentileRankTransformer")

        if not isinstance(self.params['lookback'], int) or self.params['lookback'] <= 0:
            raise ValueError("'lookback' must be a positive integer")

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate percentile rank of market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the percentile rank values (0-100)
        """
        series = self._get_field(data)
        lookback = self.params['lookback']

        # Calculate percentile rank using rolling window
        result = series.rolling(window=lookback, min_periods=1).apply(
            lambda x: pd.Series(x).rank(pct=True).iloc[-1] * 100, raw=False)

        # Name the series appropriately
        field = self.params.get('field', 'close')
        result.name = f"{field}_pctrank_{lookback}"

        return result

class RelativeStrengthTransformer(BaseMarketTransformer):
    """
    Transformer that calculates the relative strength of a market indicator.

    Relative strength is calculated as the ratio of current value to a
    reference value (typically a moving average or historical value).

    Parameters:
        reference (str): Reference method ('ma', 'value', 'lookback') (required)
        window (int): Window size for 'ma' or 'lookback' reference (required)
        value (float): Fixed value for 'value' reference
        field (str): Field to use from the indicator data (default: 'close')
    """

    def _validate_params(self) -> None:
        """Validate relative strength parameters."""
        if 'reference' not in self.params:
            raise ValueError("'reference' parameter is required for RelativeStrengthTransformer")

        reference = self.params['reference']
        valid_references = ['ma', 'value', 'lookback']
        if reference not in valid_references:
            raise ValueError(f"'reference' must be one of {valid_references}")

        if reference in ['ma', 'lookback'] and (
            'window' not in self.params or
            not isinstance(self.params['window'], int) or
            self.params['window'] <= 0
        ):
            raise ValueError("'window' must be a positive integer for 'ma' or 'lookback' reference")

        if reference == 'value' and 'value' not in self.params:
            raise ValueError("'value' parameter is required for 'value' reference")

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate relative strength of market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the relative strength values
        """
        series = self._get_field(data)
        reference = self.params['reference']

        if reference == 'ma':
            window = self.params['window']
            reference_values = series.rolling(window=window, min_periods=1).mean()
            result = series / reference_values * 100
            suffix = f"_rs_ma{window}"

        elif reference == 'value':
            value = self.params['value']
            result = series / value * 100
            suffix = f"_rs_val{value}"

        elif reference == 'lookback':
            window = self.params['window']
            # Calculate the value from n periods ago
            reference_values = series.shift(window)
            result = series / reference_values * 100
            suffix = f"_rs_lb{window}"

        # Name the series appropriately
        field = self.params.get('field', 'close')
        result.name = f"{field}{suffix}"

        return result

class ZScoreTransformer(BaseMarketTransformer):
    """
    Transformer that calculates Z-scores of market indicator values.

    Z-score represents how many standard deviations a value is from the mean,
    useful for identifying extreme values.

    Parameters:
        window (int): Historical period to use for mean/std calculation (required)
        field (str): Field to use from the indicator data (default: 'close')
    """

    def _validate_params(self) -> None:
        """Validate Z-score parameters."""
        if 'window' not in self.params:
            raise ValueError("'window' parameter is required for ZScoreTransformer")

        if not isinstance(self.params['window'], int) or self.params['window'] <= 0:
            raise ValueError("'window' must be a positive integer")

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate Z-score of market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the Z-score values
        """
        series = self._get_field(data)
        window = self.params['window']

        # Calculate rolling mean and standard deviation
        rolling_mean = series.rolling(window=window, min_periods=1).mean()
        rolling_std = series.rolling(window=window, min_periods=1).std()

        # Prevent division by zero
        rolling_std = rolling_std.replace(0, np.nan)

        # Calculate Z-score
        result = (series - rolling_mean) / rolling_std

        # 保留 NaN 值，而不是填充为 0
        # NaN 值通常出现在序列开始处（窗口未满）或标准差为 0 的情况
        # 填充为 0 可能会误导，因为 Z-score 为 0 表示值恰好等于均值

        # Name the series appropriately
        field = self.params.get('field', 'close')
        result.name = f"{field}_zscore_{window}"

        return result

class RSITransformer(BaseMarketTransformer):
    """
    Transformer that calculates the Relative Strength Index (RSI) of market indicator values.

    RSI is a momentum oscillator that measures the speed and magnitude of recent
    price changes to evaluate overbought or oversold conditions.

    Parameters:
        window (int): Period for RSI calculation (default: 14)
        field (str): Field to use from the indicator data (default: 'Close')
    """

    def _validate_params(self) -> None:
        """Validate RSI parameters."""
        # window parameter is optional, default to 14
        window = self.params.get('window', 14)
        if not isinstance(window, int) or window <= 0:
            raise ValueError("'window' must be a positive integer")

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """
        Calculate RSI of market indicator data.

        Args:
            data: Market indicator data frame

        Returns:
            Series containing the RSI values (0-100)
        """
        series = self._get_field(data)
        window = self.params.get('window', 14)

        # Calculate price changes
        delta = series.diff()
        
        # Separate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # Calculate average gain and loss using Wilder's smoothing
        avg_gain = gain.ewm(alpha=1/window, adjust=False).mean()
        avg_loss = loss.ewm(alpha=1/window, adjust=False).mean()
        
        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        # Handle edge cases
        rsi = rsi.replace([np.inf, -np.inf], np.nan)
        # Fill NaN values with 50 (neutral RSI)
        rsi = rsi.fillna(50)

        # Name the series appropriately
        field = self.params.get('field', 'Close')
        rsi.name = f"{field}_rsi_{window}"

        return rsi

class IdentityTransformer(BaseMarketTransformer):
    """直接返回原始数据的转换器，不做变换处理但仍需满足一致的参数要求"""

    def _validate_params(self) -> None:
        """Validate transformer parameters.

        Args:
            None

        Returns:
            None

        Raises:
            ValueError: If required parameters are missing or invalid.
        """
        if 'indicator' not in self.params:
            raise ValueError("'indicator' parameter is required for IdentityTransformer")

        # Field parameter is optional, _get_field will default to 'close' if not provided

    def transform(self, data: pd.DataFrame) -> pd.Series:
        """Return the original data series for the specified field without transformation.

        Args:
            data: DataFrame containing market data.

        Returns:
            pd.Series: Original data for the specified field.
        """
        return self._get_field(data)
