"""
Market Indicator Manager for investStrategyService.

This module provides a manager for market indicators such as VIX, PE ratio, etc.
It handles loading, transforming, and accessing market indicator data.
"""

import logging
from typing import Dict, Optional, Union, Any, Callable, Tuple
import pandas as pd
from data_sources import PortfolioDataLoader
from utilities.helpers import get_today_str

logger = logging.getLogger(__name__)

class MarketIndicatorManager:
    """
    Manager for market indicator data.

    This class is responsible for:
    - Loading market indicator data
    - Providing access to indicator values by date and field
    - Applying transformers to indicator data

    Attributes:
        indicators (Dict): Dictionary mapping indicator names to DataFrames
        transformers (Dict): Dictionary of registered transformers
        transformed_data (Dict): Cache of transformed indicator data
    """

    def __init__(self, data_loader: Optional[PortfolioDataLoader] = None):
        """
        Initialize the market indicator manager.

        Args:
            data_loader: The data loader to use for fetching market data
        """
        self.data_loader = data_loader or PortfolioDataLoader.get_instance()
        self.indicators: Dict[str, pd.DataFrame] = {}
        self.transformers: Dict[str, Tuple[str, Dict[str, Any], Callable]] = {}
        self.transformed_data: Dict[str, pd.DataFrame] = {}

    def register_indicator(self, code: str, start_date: str, end_date: Optional[str] = None) -> None:
        """
        Register a market indicator and load its data.

        Args:
            code: Symbol code for the indicator (e.g., "VIX")
            start_date: Start date for data loading (YYYY-MM-DD)
            end_date: End date for data loading (YYYY-MM-DD), defaults to today
        """
        self.load_indicator(code, code, start_date, end_date)

    def load_indicator(self, indicator_name: str, code: str,
                     start_date: str, end_date: Optional[str] = None) -> None:
        """
        Load market indicator data from the data source.

        Args:
            indicator_name: Name to register the indicator under
            code: Symbol code for the indicator
            start_date: Start date for data loading (YYYY-MM-DD)
            end_date: End date for data loading (YYYY-MM-DD), defaults to today
        """
        end_date = end_date or get_today_str()
        logger.info(f"Loading market indicator {indicator_name} ({code}) from {start_date} to {end_date}")

        # 使用统一的get_market_data接口替代直接调用get_market_index
        # 这样可以同时支持市场指数和普通标的(ETF/股票)作为市场指标
        df = self.data_loader.get_market_data(code, start_date, end_date)
        logger.info(f"Fetched market data for {indicator_name} ({code}) with {len(df)} rows")

        self.indicators[indicator_name] = df
        logger.info(f"Successfully loaded {len(df)} rows of data for {indicator_name}")

    def get_indicator_value(self, indicator_name: str, date: Union[str, pd.Timestamp],
                          field: str = 'close') -> Optional[float]:
        """
        Get the value of an indicator for a specific date and field.

        Args:
            indicator_name: Name of the indicator
            date: The date to retrieve the value for
            field: The field to retrieve (default: 'close')

        Returns:
            float or None: The indicator value if available
        """
        if indicator_name not in self.indicators:
            logger.warning(f"Indicator {indicator_name} not found")
            return None

        df = self.indicators[indicator_name]

        # Convert date to pandas timestamp for reliable comparison
        if isinstance(date, str):
            date = pd.Timestamp(date)

        # Get the exact date if available
        if date in df.index:
            return df.loc[date, field]

        # Get the last available value before the requested date
        earlier_dates = df.index[df.index <= date]
        if len(earlier_dates) > 0:
            last_available_date = earlier_dates[-1]
            return df.loc[last_available_date, field]

        # If no earlier dates, check if the requested date is before the first date
        if len(df.index) > 0 and date < df.index[0]:
            logger.warning(f"Requested date {date} is before the start of data for {indicator_name}")
        else:
            logger.warning(f"No data available for {indicator_name} on or before {date}")
        return None

    def get_indicator_series(self, indicator_name: str, field: str = 'close') -> Optional[pd.Series]:
        """
        Get the full time series for an indicator field.

        Args:
            indicator_name: Name of the indicator
            field: The field to retrieve (default: 'close')

        Returns:
            pd.Series or None: The indicator time series if available
        """
        if indicator_name not in self.indicators:
            logger.warning(f"Indicator {indicator_name} not found")
            return None

        df = self.indicators[indicator_name]
        if field not in df.columns:
            logger.warning(f"Field {field} not found in indicator {indicator_name}")
            return None

        return df[field]

    def register_transformer(self, transformer_name: str, indicator_name: str,
                           transformer_class_name: str, params: Dict[str, Any]) -> None:
        """
        Register a transformer to be applied to an indicator.

        Args:
            transformer_name: Name to register the transformer under
            indicator_name: Name of the indicator to transform
            transformer_class_name: Name of the transformer class in the registry
            params: Parameters for the transformer
        """
        from components.registry import ComponentRegistry

        registry = ComponentRegistry.get_instance()
        transformer_class = registry.get_market_transformer(transformer_class_name)

        if not transformer_class:
            raise ValueError(f"Transformer class {transformer_class_name} not found in registry")

        transformer_instance = transformer_class(params=params)

        # Register the transformer with the source indicator and params
        self.transformers[transformer_name] = (indicator_name, params, transformer_instance)
        logger.info(f"Registered transformer {transformer_name} for indicator {indicator_name}")

        # Clear any cached transformed data for this transformer
        if transformer_name in self.transformed_data:
            del self.transformed_data[transformer_name]

    def get_transformed_indicator(self, transformer_name: str) -> Optional[pd.Series]:
        """
        Get a transformed indicator time series.

        Args:
            transformer_name: Name of the registered transformer

        Returns:
            pd.Series or None: The transformed indicator series if available
        """
        # Check if transformer exists
        if transformer_name not in self.transformers:
            logger.warning(f"Transformer {transformer_name} not found")
            return None

        # Use cached result if available
        if transformer_name in self.transformed_data:
            return self.transformed_data[transformer_name]

        # Get transformer configuration
        indicator_name, params, transformer = self.transformers[transformer_name]

        # Check if source indicator exists
        if indicator_name not in self.indicators:
            logger.warning(f"Source indicator {indicator_name} for transformer {transformer_name} not found")
            return None

        # Get source data
        source_data = self.indicators[indicator_name]

        try:
            # Apply transformation
            result = transformer.transform(source_data)

            # Cache result
            self.transformed_data[transformer_name] = result

            return result
        except ValueError as e:
            # 处理参数错误
            logger.error(f"Parameter error in transformer {transformer_name}: {e}")
            return None
        except KeyError as e:
            # 处理数据访问错误
            logger.error(f"Data access error in transformer {transformer_name}: {e}")
            return None
        except TypeError as e:
            # 处理类型错误
            logger.error(f"Type error in transformer {transformer_name}: {e}")
            return None
        except pd.errors.EmptyDataError as e:
            # 处理空数据错误
            logger.error(f"Empty data error in transformer {transformer_name}: {e}")
            return None
        except Exception as e:
            # 捕获其他未预见的错误，但提供详细日志
            logger.error(f"Unexpected error applying transformer {transformer_name}: {e}", exc_info=True)
            return None

    def align_to_data(self, data_index: pd.DatetimeIndex) -> Dict[str, pd.Series]:
        """
        Align all indicators to a given data index.

        This is useful for ensuring that all indicators are properly aligned
        with the main price data when used in signal evaluation.

        Args:
            data_index: DatetimeIndex to align to

        Returns:
            Dict[str, pd.Series]: Dictionary of aligned indicator series
        """
        aligned_indicators = {}

        # Align base indicators
        for name, df in self.indicators.items():
            # Create a series for the close price
            if 'close' in df.columns:
                series = df['close']
                # Reindex to align with the target index, using forward fill for missing values
                aligned = series.reindex(data_index, method='ffill')
                aligned_indicators[name] = aligned

        # Align transformed indicators
        for name in self.transformers:
            transformed = self.get_transformed_indicator(name)
            if transformed is not None:
                if isinstance(transformed, pd.Series):
                    aligned = transformed.reindex(data_index, method='ffill')
                    aligned_indicators[name] = aligned
                elif isinstance(transformed, pd.DataFrame) and 'value' in transformed.columns:
                    series = transformed['value']
                    aligned = series.reindex(data_index, method='ffill')
                    aligned_indicators[name] = aligned

        return aligned_indicators
