"""
Component Registry for investStrategyService.

This module provides a central registry for all strategy components, including:
- Trade strategies
- Capital strategies
- Indicators (future)
- Signals (future)
- Market transformers (future)

The registry uses a singleton pattern to ensure a single global instance.
"""

import logging
import importlib
import pkgutil
import traceback
from datetime import datetime
from typing import Dict, Type, Optional, List

logger = logging.getLogger(__name__)

class ComponentRegistry:
    """Component Registry for managing strategy components.
    
    This registry is responsible for managing all strategy components,
    including trade strategies, capital strategies, and future primitive components.
    
    It follows the Singleton pattern to ensure only one registry exists.
    
    Attributes:
        trade_strategies (Dict): Registry of trade strategy components
        capital_strategies (Dict): Registry of capital strategy components
        indicators (Dict): Registry of indicator components (future)
        signals (Dict): Registry of signal components (future)
        market_transformers (Dict): Registry of market transformer components (future)
        scan_failures (List): Record of scanning failures for diagnostics
    """
    _instance = None  # Singleton instance
    
    @classmethod
    def get_instance(cls) -> 'ComponentRegistry':
        """Get the singleton instance of the registry."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def __init__(self):
        """Initialize the registry."""
        # 策略组件注册表
        self.trade_strategies: Dict[str, Dict[str, str]] = {}
        self.capital_strategies: Dict[str, Dict[str, str]] = {}
        
        # 未来的原语组件注册表（暂未实现）
        self.indicators: Dict[str, Dict[str, str]] = {}
        self.signals: Dict[str, Dict[str, str]] = {}
        self.market_transformers: Dict[str, Dict[str, str]] = {}
        
        # 扫描失败记录，用于诊断问题
        self.scan_failures: List[Dict[str, str]] = []
        
        # 特殊命名映射，用于处理不符合命名约定的组件
        self._name_mappings = {
            # Market transformer mappings
            'components.market_transformers.moving_average': 'MovingAverageTransformer',
            'components.market_transformers.percentile_rank': 'PercentileRankTransformer',
            'components.market_transformers.relative_strength': 'RelativeStrengthTransformer',
            'components.market_transformers.zscore': 'ZScoreTransformer',
            'components.market_transformers.identity': 'IdentityTransformer',
            # Trade Strategy mappings
            'trade_strategies.chandelier_exit_ma_strategy': 'ChandelierExitMAStrategy',
            'trade_strategies.dual_moving_average_strategy': 'DualMovingAverageStrategy',
            'trade_strategies.tripple_moving_average_strategy': 'TrippleMovingAverageStrategy',
            'trade_strategies.rsi_strategy': 'RsiStrategy',
            'trade_strategies.buy_hold_strategy': 'BuyHoldStrategy',
            
            # Capital Strategy mappings
            'capital_strategies.percent_strategy': 'PercentCapitalStrategy',
            'capital_strategies.simple_percent_strategy': 'SimplePercentCapitalStrategy',
            'capital_strategies.fixed_investment_strategy': 'FixedInvestmentStrategy'
        }
        
        # 初始化注册表
        self._initialize_registry()
        
        # Manually register primitives
        # 手动注册常用指标组件
        self.register_component('indicators', 'SMA',
            'components.indicators.moving_averages', 'SMA')
        self.register_component('indicators', 'EMA',
            'components.indicators.moving_averages', 'EMA')
        self.register_component('indicators', 'RSI',
            'components.indicators.momentum', 'RSI')
        self.register_component('indicators', 'MACD',
            'components.indicators.momentum', 'MACD')
        self.register_component('indicators', 'ATR',
            'components.indicators.volatility', 'ATR')
        self.register_component('indicators', 'BollingerBands',
            'components.indicators.volatility', 'BollingerBands')
        # 注册极值指标
        self.register_component('indicators', 'HighestValue',
            'components.indicators.extremes', 'HighestValue')
        self.register_component('indicators', 'LowestValue',
            'components.indicators.extremes', 'LowestValue')
        self.register_component('indicators', 'PercentFromHighest',
            'components.indicators.extremes', 'PercentFromHighest')
        self.register_component('indicators', 'PercentFromLowest',
            'components.indicators.extremes', 'PercentFromLowest')
        self.register_component('indicators', 'Constant', 
            'components.indicators.constants', 'Constant')
            
        # 注册吊灯止损指标
        self.register_component('indicators', 'ChandelierExit',
            'components.indicators.chandelier', 'ChandelierExit')
            
        # 信号原语
        # 比较信号
        self.register_component('signals', 'Crossover',
            'components.signals.comparison', 'Crossover')
        self.register_component('signals', 'Crossunder',
            'components.signals.comparison', 'Crossunder')
        self.register_component('signals', 'LessThan',
            'components.signals.comparison', 'LessThan')
        self.register_component('signals', 'GreaterThan',
            'components.signals.comparison', 'GreaterThan')
        self.register_component('signals', 'InRange',
            'components.signals.comparison', 'InRange')
        self.register_component('signals', 'Comparison',
            'components.signals.comparison', 'Comparison')
            
        # 模式信号
        self.register_component('signals', 'Streak',
            'components.signals.pattern', 'Streak')
            
        # 逻辑信号
        self.register_component('signals', 'And',
            'components.signals.logical', 'And')
        self.register_component('signals', 'Or',
            'components.signals.logical', 'Or')
        self.register_component('signals', 'Not',
            'components.signals.logical', 'Not')
            
        # 复合信号
        self.register_component('signals', 'CrossAbove',
            'components.signals.composite', 'CrossAbove')
        self.register_component('signals', 'CrossBelow',
            'components.signals.composite', 'CrossBelow')
            
        # 动量信号
        self.register_component('signals', 'PercentChange',
            'components.signals.momentum', 'PercentChange')
            
        # 数学运算信号
        self.register_component('signals', 'Add',
            'components.signals.math', 'Add')
        self.register_component('signals', 'Subtract',
            'components.signals.math', 'Subtract')
        self.register_component('signals', 'Multiply',
            'components.signals.math', 'Multiply')
        self.register_component('signals', 'Divide',
            'components.signals.math', 'Divide')
            
        # 股债切换信号
        self.register_component('signals', 'StockBondSwitch',
            'components.signals.switching', 'StockBondSwitch')
            
        # 手动注册市场转换器
        logger.info("Manually registered market transformers component: MovingAverageTransformer")
        self.market_transformers['MovingAverageTransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'MovingAverageTransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
        
        logger.info("Manually registered market transformers component: PercentileRankTransformer")
        self.market_transformers['PercentileRankTransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'PercentileRankTransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
        
        logger.info("Manually registered market transformers component: RelativeStrengthTransformer")
        self.market_transformers['RelativeStrengthTransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'RelativeStrengthTransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
        
        logger.info("Manually registered market transformers component: ZScoreTransformer")
        self.market_transformers['ZScoreTransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'ZScoreTransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
        
        logger.info("Manually registered market transformers component: IdentityTransformer")
        self.market_transformers['IdentityTransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'IdentityTransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
        
        logger.info("Manually registered market transformers component: RSITransformer")
        self.market_transformers['RSITransformer'] = {
            'module_path': 'components.market_transformers',
            'class_name': 'RSITransformer',
            'metadata': {'registered_at': datetime.now().isoformat(), 'source': 'manual_registration'}
        }
    
    def _initialize_registry(self) -> None:
        """Initialize the registry by scanning all component packages."""
        logger.info("Initializing Component Registry...")
        
        # 动态扫描组件包
        self._scan_and_register('trade_strategies', self.trade_strategies)
        self._scan_and_register('capital_strategies', self.capital_strategies)
        
        # 原语组件扫描
        try:
            # 扫描指标组件
            num_indicators = self._scan_and_register('components.indicators', self.indicators)
            logger.info(f"Registered {num_indicators} indicator components")
            
            # 扫描信号组件
            num_signals = self._scan_and_register('components.signals', self.signals)
            logger.info(f"Registered {num_signals} signal components")
            
            # 扫描市场转换器组件
            num_transformers = self._scan_and_register('components.market_transformers', self.market_transformers)
            logger.info(f"Registered {num_transformers} market transformer components")
            
        except Exception as e:
            logger.error(f"Error scanning primitive components: {e}")
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        # 打印注册信息
        logger.info(f"Component Registry initialized with {len(self.trade_strategies)} trade strategies, "
                   f"{len(self.capital_strategies)} capital strategies, "
                   f"{len(self.indicators)} indicators, "
                   f"{len(self.signals)} signals, "
                   f"{len(self.market_transformers)} market transformers")
        
        # 详细组件日志（debug 级别）
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"Registered trade strategies: {list(self.trade_strategies.keys())}")
            logger.debug(f"Registered capital strategies: {list(self.capital_strategies.keys())}")
            logger.debug(f"Registered indicators: {list(self.indicators.keys())}")
            logger.debug(f"Registered signals: {list(self.signals.keys())}")
            logger.debug(f"Registered market transformers: {list(self.market_transformers.keys())}")
            
        # 如果有扫描失败，记录警告
        if self.scan_failures:
            logger.warning(f"Encountered {len(self.scan_failures)} failures during component scanning")
            for failure in self.scan_failures:
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(f"Scan failure: {failure}")
    
    def _scan_and_register(self, package, registry):
        """Scan a package for components and register them in the registry.
        
        Args:
            package (str): Package path to scan for components
            registry (Dict): Registry dictionary to add components to
            
        Returns:
            int: Number of components registered
        """
        registered_count = 0
        try:
            # 导入包
            package_obj = importlib.import_module(package)
            package_path = package_obj.__path__
            
            # 遍历包中的所有模块
            for _, name, is_pkg in pkgutil.iter_modules(package_path):
                if not is_pkg:  # 只处理模块，不处理子包
                    try:
                        # 构建完整模块路径
                        full_module_path = f"{package}.{name}"
                        
                        # 使用特殊映射或生成类名
                        if full_module_path in self._name_mappings:
                            class_name = self._name_mappings[full_module_path]
                        else:
                            class_name = self._generate_class_name(name, package)
                        
                        # 添加到注册表
                        registry[class_name] = {
                            'module_path': full_module_path,
                            'class_name': class_name,
                            'metadata': {
                                'discovered_at': datetime.now().isoformat(),
                                'source': 'auto_scan'
                            }
                        }
                        registered_count += 1
                        logger.debug(f"Registered component: {class_name} from {full_module_path}")
                        
                    except Exception as e:
                        error_details = {
                            'module': f"{package}.{name}",
                            'error': str(e),
                            'traceback': traceback.format_exc(),
                            'timestamp': datetime.now().isoformat()
                        }
                        self.scan_failures.append(error_details)
                        logger.warning(f"Error registering module {name}: {e}")
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug(f"Full traceback: {traceback.format_exc()}")
        except Exception as e:
            error_details = {
                'package': package,
                'error': str(e),
                'traceback': traceback.format_exc(),
                'timestamp': datetime.now().isoformat()
            }
            self.scan_failures.append(error_details)
            logger.error(f"Error scanning package {package}: {e}")
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        return registered_count
    
    def _generate_class_name(self, module_name: str, package_type: str) -> str:
        """Generate a standardized class name from a module name.
        
        Args:
            module_name (str): The module name to convert
            package_type (str): The package type (e.g. 'trade_strategies')
            
        Returns:
            str: A standardized class name
        """
        # 处理特殊缩写
        special_abbrs = {'ma': 'MA', 'rsi': 'RSI', 'atr': 'ATR', 'ema': 'EMA', 'vix': 'VIX', 
                          'macd': 'MACD', 'bbands': 'BBands', 'sma': 'SMA'}
        
        # 分割模块名
        words = module_name.split('_')
        capitalized_words = []
        
        # 处理每个单词
        for word in words:
            word_lower = word.lower()
            if word_lower in special_abbrs:
                capitalized_words.append(special_abbrs[word_lower])
            else:
                capitalized_words.append(word.capitalize())
        
        # 生成基本类名
        base_class_name = ''.join(capitalized_words)
        
        # 根据包类型添加后缀
        if package_type == 'capital_strategies' and not base_class_name.endswith('CapitalStrategy'):
            return f"{base_class_name}CapitalStrategy"
        
        # 确保交易策略有Strategy后缀
        if package_type == 'trade_strategies' and not base_class_name.endswith('Strategy'):
            return f"{base_class_name}Strategy"
            
        # 处理指标类名 - 通常保持原样，因为它们已经有有意义的名称（如SMA、RSI）
        if package_type == 'components.indicators':
            # 如果指标名称已经以Indicator结尾，则保持不变
            if base_class_name.endswith('Indicator'):
                return base_class_name
            # 否则检查是否是常见指标缩写，如果不是则添加Indicator后缀
            for abbr in special_abbrs.values():
                if base_class_name == abbr:
                    return base_class_name
            return f"{base_class_name}Indicator"
            
        # 处理信号类名 - 通常以Signal结尾，除非是特定的信号原语（如Crossover）
        if package_type == 'components.signals':
            special_signals = ['Crossover', 'Crossunder', 'LessThan', 'GreaterThan', 'And', 'Or', 'Not']
            if base_class_name.endswith('Signal') or base_class_name in special_signals:
                return base_class_name
            return f"{base_class_name}Signal"
            
        # 处理市场转换器类名 - 通常以Transformer结尾
        if package_type == 'components.market_transformers':
            if base_class_name.endswith('Transformer'):
                return base_class_name
            return f"{base_class_name}Transformer"
            
        return base_class_name
    
    def register_component(self, component_type: str, component_name: str, 
                          module_path: str, class_name: str) -> None:
        """Manually register a component in the registry.
        
        Args:
            component_type (str): Type of component ('trade_strategies', 'capital_strategies', etc.)
            component_name (str): Name to register the component under
            module_path (str): Import path to the module
            class_name (str): Name of the class within the module
        """
        registry = self._get_registry_for_type(component_type)
        if not registry:
            raise ValueError(f"Unknown component type: {component_type}")
            
        registry[component_name] = {
            'module_path': module_path,
            'class_name': class_name,
            'metadata': {
                'registered_at': datetime.now().isoformat(),
                'source': 'manual_registration'
            }
        }
        logger.info(f"Manually registered {component_type} component: {component_name}")
    
    def _get_registry_for_type(self, component_type: str) -> Optional[Dict]:
        """Get the appropriate registry dictionary for a component type.
        
        Args:
            component_type (str): Type of component
            
        Returns:
            Optional[Dict]: The registry dictionary or None if not found
        """
        registry_map = {
            'trade_strategies': self.trade_strategies,
            'capital_strategies': self.capital_strategies,
            'indicators': self.indicators,
            'signals': self.signals,
            'market_transformers': self.market_transformers
        }
        return registry_map.get(component_type)
    
    def _get_component(self, component_name: str, registry: Dict) -> Optional[Type]:
        """Get a component class from the registry.
        
        Args:
            component_name: Name of the component to retrieve
            registry: Registry dictionary to look in
            
        Returns:
            Optional[Type]: The component class if found, None otherwise
        """
        if component_name not in registry:
            # 判断注册表类型并显示正确的消息
            if registry is self.trade_strategies:
                registry_type = "Trade strategy"
            elif registry is self.capital_strategies:
                registry_type = "Capital strategy"
            elif registry is self.indicators:
                registry_type = "Indicator"
            elif registry is self.signals:
                registry_type = "Signal"
            elif registry is self.market_transformers:
                registry_type = "Market transformer"
            else:
                registry_type = "Component"
                
            logger.warning(f"{registry_type} '{component_name}' not found in registry")
            return None
        
        try:
            info = registry[component_name]
            module = importlib.import_module(info['module_path'])
            component_class = getattr(module, info['class_name'])
            
            # 记录成功加载
            if 'metadata' in info:
                info['metadata']['last_loaded'] = datetime.now().isoformat()
                
            return component_class
        except (ImportError, AttributeError) as e:
            logger.error(f"Error loading component {component_name}: {e}")
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"Full traceback: {traceback.format_exc()}")
            return None
    
    def get_trade_strategy(self, strategy_name: str) -> Optional[Type]:
        """Get a trade strategy class by name.
        
        Args:
            strategy_name: Name of the strategy to retrieve
            
        Returns:
            Optional[Type]: The strategy class if found, None otherwise
        """
        return self._get_component(strategy_name, self.trade_strategies)
    
    def get_capital_strategy(self, strategy_name: str) -> Optional[Type]:
        """Get a capital strategy class by name.
        
        Args:
            strategy_name: Name of the strategy to retrieve
            
        Returns:
            Optional[Type]: The strategy class if found, None otherwise
        """
        return self._get_component(strategy_name, self.capital_strategies)
    
    def get_component_info(self, component_type: str, component_name: str) -> Optional[Dict]:
        """Get information about a registered component.
        
        Args:
            component_type (str): Type of component ('trade_strategies', 'capital_strategies', etc.)
            component_name (str): Name of the component
            
        Returns:
            Optional[Dict]: Component information if found, None otherwise
        """
        registry = self._get_registry_for_type(component_type)
        if not registry or component_name not in registry:
            return None
            
        return registry[component_name]
    
    def get_indicator(self, name: str) -> Optional[Type]:
        """Get an indicator class by name.
        
        Args:
            name: Name of the indicator to retrieve
            
        Returns:
            Optional[Type]: The indicator class if found, None otherwise
        """
        return self._get_component(name, self.indicators)
    
    def get_signal(self, name: str) -> Optional[Type]:
        """Get a signal class by name.
        
        Args:
            name: Name of the signal to retrieve
            
        Returns:
            Optional[Type]: The signal class if found, None otherwise
        """
        return self._get_component(name, self.signals)
    
    def get_market_transformer(self, name: str) -> Optional[Type]:
        """Get a market transformer class by name.
        
        Args:
            name: Name of the market transformer to retrieve
            
        Returns:
            Optional[Type]: The market transformer class if found, None otherwise
        """
        return self._get_component(name, self.market_transformers)
        
    # 原语组件专用注册方法
    
    def register_indicator(self, indicator_name: str, module_path: str, class_name: str) -> None:
        """手动注册指标原语组件。
        
        Args:
            indicator_name (str): 注册的指标名称
            module_path (str): 模块导入路径
            class_name (str): 模块中的类名
        """
        self.register_component('indicators', indicator_name, module_path, class_name)
        
    def register_signal(self, signal_name: str, module_path: str, class_name: str) -> None:
        """手动注册信号原语组件。
        
        Args:
            signal_name (str): 注册的信号名称
            module_path (str): 模块导入路径
            class_name (str): 模块中的类名
        """
        self.register_component('signals', signal_name, module_path, class_name)
        
    def register_market_transformer(self, transformer_name: str, module_path: str, class_name: str) -> None:
        """手动注册市场转换器原语组件。
        
        Args:
            transformer_name (str): 注册的转换器名称
            module_path (str): 模块导入路径
            class_name (str): 模块中的类名
        """
        self.register_component('market_transformers', transformer_name, module_path, class_name)
