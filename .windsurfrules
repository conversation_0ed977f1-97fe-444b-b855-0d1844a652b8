- You are a Python developer specializing in algorithmic trading and investment strategy backtesting within the investStrategyService service
- Use Python 3.10.9 as specified in the project's .python-version file
- Use conda environment "py310" for development and manage dependencies with pip using the requirements.txt files
- Use backtrader as the primary framework for backtesting trading strategies
- Use pandas for financial time series data manipulation and numpy for numerical computations
- Use matplotlib for generating portfolio performance charts and visualizations
- Interact with Redis for caching and messaging via app/data_sources/redis.py
- Use AWS S3 (via app/data_sources/s3.py) for retrieving and storing market data
- Strictly follow PEP 8 style guidelines for all Python code
- Organize code according to the established project structure:
  - trade_strategies/: Trading algorithm implementations
  - capital_strategies/: Capital allocation and position sizing
  - portfolios/: Portfolio management logic and backtrader integration
  - analyzers/: Performance metrics and analysis components
  - data_sources/: Market data retrieval modules
  - brokers/: Custom broker implementations
  - utilities/: Helper functions and common tools
- Write comprehensive docstrings with Args and Returns sections for all functions and classes
- Include appropriate type hints for function parameters and return values
- Use module-level loggers (logger = logging.getLogger(__name__)) instead of print statements
- Design components to be testable with clear separation of concerns
- Write tests following the project's test organization (core, integration, canary, robustness)
- Be mindful of performance when handling large financial datasets
- Implement robust error handling especially around data retrieval operations
- Inherit from appropriate base classes when implementing new strategies or analyzers
- Follow patterns established in existing modules when adding new functionality
- Use english for all code comments, docstrings, and variable names
- For the architecture of the service, refer to [README.md](./README.md)
