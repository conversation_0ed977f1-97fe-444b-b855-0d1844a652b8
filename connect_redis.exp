#!/usr/bin/expect -f
# exp_internal 1 ;# Uncomment for debugging
set timeout 20

spawn fly redis connect
expect {
    timeout { send_user "Timeout waiting for prompt\n"; exit 1 }
    "Select a database to connect to"
}
send "\r"

# Wait for proxy confirmation
expect {
    timeout { send_user "Timeout waiting for proxy confirmation\n"; exit 1 }
    "Proxying local port"
}
send_user "Redis proxy connected.\n"

# Keep running until terminated externally
while {1} {
    expect {
        timeout { continue }
        eof { exit 0 }
    }
}
