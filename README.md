# investStrategyService

investStrategyService 是一个可以通过配置文件或远程消息自动回测基于日线级别的交易策略的投资组合的服务，并能生成基于此策略的交易信号及邮件通知。

## 技术栈概览

### 核心技术
- **Python 3.10** - 主要开发语言
- **Backtrader** - 交易策略回测框架
- **Pandas/NumPy** - 数据处理和分析
- **Matplotlib** - 图表生成和可视化
- **SQLite** - 本地数据存储
- **原语引擎** - 声明式策略构建系统

### 外部服务和基础设施
- **Fly.io** - 应用托管和计算资源
- **Upstash Redis** - 缓存和任务队列
- ~~**Upstash Kafka** - 事件流和消息处理~~
- **AWS S3** - 文件存储和分发
- **GitHub Actions** - CI/CD和自动部署
- **Slack** - 任务日志及错误告警通知

### 开发和运行环境
- **Docker** - 容器化和隔离环境
- **Conda** - 环境和依赖管理
- **Flask** - API服务和Web接口

## 架构

### 系统架构概览

系统由核心服务组件、任务调度器和工作机器组成，通过消息队列和API进行交互。下图展示了系统的整体架构：

```mermaid
flowchart TD
    subgraph "System Architecture Overview"
        CLIENT[Client] -->|API Requests/Kafka Messages| EVENT_POLLER[EventPoller]
        EVENT_POLLER -->|Process Messages| TASK_RUNNER[TaskRunner]
        TASK_RUNNER -->|Create Backtest Task| PORTFOLIO_FACTORY[PortfolioFactory]
        PORTFOLIO_FACTORY -->|Instantiate| PORTFOLIO_MANAGER[PortfolioManager]

        subgraph "Strategy Components"
            PORTFOLIO_MANAGER -->|Use| TRADE_STRATEGY[TradeStrategy]
            PORTFOLIO_MANAGER -->|Use| CAPITAL_STRATEGY[CapitalStrategy]
            PORTFOLIO_MANAGER -->|Use| DATA_LOADER[DataLoader]

            subgraph "Primitive Engine"
                TRADE_STRATEGY -->|Use| COMP_REG[ComponentRegistry]
                TRADE_STRATEGY -->|Use| SIGNAL_EVAL[SignalEvaluator]
                COMP_REG -->|Register| IND_PRIM[Indicator Primitives]
                COMP_REG -->|Register| SIG_PRIM[Signal Primitives]
                SIGNAL_EVAL -->|Use| MARKET_IND[MarketIndicatorManager]
            end
        end

        subgraph "Backtest Engine"
            PORTFOLIO_MANAGER -->|Configure| CEREBRO[Backtrader Cerebro]
            CEREBRO -->|Use| CUSTOM_BROKER[CustomBroker]
            CEREBRO -->|Add| BT_STRATEGY_ADAPTER[BacktraderStrategyAdapter]
            CEREBRO -->|Add| ANALYZERS[Analyzers]
        end

        subgraph "Data Sources"
            DATA_LOADER -->|Read| REDIS[(Redis Cache)]
            DATA_LOADER -->|Read| S3[(S3 Storage)]
            DATA_LOADER -->|Request| OHLC_PROXY[OHLC Proxy]
        end

        subgraph "Result Processing"
            PORTFOLIO_MANAGER -->|Collect Results| DATA_COLLECTOR[PortfolioDataCollector]
            PORTFOLIO_MANAGER -->|Collect Signals| SIGNAL_COLLECTOR[PortfolioTradeSignalCollector]
            DATA_COLLECTOR -->|Generate| PORTFOLIO_DB[(Portfolio DB)]
            SIGNAL_COLLECTOR -->|Generate| SIGNALS_DB[(Signals DB)]
            PORTFOLIO_MANAGER -->|Export| NOTIFICATION[Notifications/Email]
            PORTFOLIO_MANAGER -->|Generate| CHARTS[Charts]
        end

        MACHINE_MANAGER[MachineManager] -->|Manage| WORKER_MACHINES[Worker Machines]
        WORKER_MACHINES -->|Process| TASK_RUNNER
    end

    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef data fill:#ff9,stroke:#333,stroke-width:1px;
    classDef component fill:#9cf,stroke:#333,stroke-width:1px;

    class PORTFOLIO_MANAGER,CEREBRO primary;
    class TRADE_STRATEGY,CAPITAL_STRATEGY,BT_STRATEGY_ADAPTER,ANALYZERS secondary;
    class REDIS,S3,PORTFOLIO_DB,SIGNALS_DB data;
    class DATA_LOADER,DATA_COLLECTOR,SIGNAL_COLLECTOR,TASK_RUNNER component;
```

### 核心组件交互

主服务内部组件之间的交互关系如下图所示：

```mermaid
flowchart LR
    subgraph "Main Service Components"
        MAIN_V2[main_v2.py] -->|Create| PORTFOLIO_FACTORY[PortfolioFactory]
        MAIN_V2 -->|Create| TASK_RUNNER[TaskRunner]
        MAIN_V2 -->|Create| EVENT_POLLER[EventPoller]

        TASK_RUNNER -->|Use| PORTFOLIO_FACTORY
        EVENT_POLLER -->|Use| TASK_RUNNER

        PORTFOLIO_FACTORY -->|Create| PORTFOLIO_MANAGER[PortfolioManager]
        PORTFOLIO_MANAGER -->|Execute Backtest| CEREBRO[Backtrader Cerebro]
        PORTFOLIO_MANAGER -->|Collect Data| DATA_COLLECTOR[PortfolioDataCollector]
        PORTFOLIO_MANAGER -->|Collect Signals| SIGNAL_COLLECTOR[PortfolioTradeSignalCollector]

        DATA_COLLECTOR -->|Process| ANALYZERS[Analyzer Results]
        SIGNAL_COLLECTOR -->|Process| TRADE_SIGNALS[Trade Signals]
    end

    subgraph "Strategy Implementations"
        TRADE_STRAT[TradeStrategy] -->|Derive| CHANDELIER[ChandelierExitMAStrategy]
        TRADE_STRAT -->|Derive| DMA[DualMovingAverageStrategy]
        TRADE_STRAT -->|Derive| TMA[TrippleMovingAverageStrategy]
        TRADE_STRAT -->|Derive| RSI[RsiStrategy]
        TRADE_STRAT -->|Derive| BUY_HOLD[BuyHoldStrategy]
        TRADE_STRAT -->|Derive| PRIMITIVE[PrimitiveStrategy]

        subgraph "Primitive Components"
            PRIMITIVE -->|Use| IND_PRIMITIVES[Indicator Primitives]
            PRIMITIVE -->|Use| SIG_PRIMITIVES[Signal Primitives]
            IND_PRIMITIVES -->|Include| SMA[SMA]
            IND_PRIMITIVES -->|Include| RSI_IND[RSI]
            IND_PRIMITIVES -->|Include| ATR[ATR]
            SIG_PRIMITIVES -->|Include| CROSSOVER[Crossover]
            SIG_PRIMITIVES -->|Include| COMPARISON[Comparison]
            SIG_PRIMITIVES -->|Include| LOGIC[Logic Operators]
        end

        CAP_STRAT[CapitalStrategy] -->|Derive| PERCENT[PercentCapitalStrategy]
        CAP_STRAT -->|Derive| SIMPLE_PERCENT[SimplePercentCapitalStrategy]
        CAP_STRAT -->|Derive| FIXED_INVEST[FixedInvestmentStrategy]
    end

    subgraph "Adapters and Data Sources"
        BT_ADAPTER[BacktraderStrategyAdapter] -->|Use| CEREBRO
        SIZER_ADAPTER[CapitalStrategySizerAdapter] -->|Use| CEREBRO
        DATA_ADAPTER[CustomPandasData] -->|Provide Data| CEREBRO

        DATA_LOADER[PortfolioDataLoader] -->|Load| DATA_SOURCE[Data Sources]
        DATA_SOURCE -->|Include| REDIS[(Redis)]
        DATA_SOURCE -->|Include| S3[(S3 Storage)]
        DATA_SOURCE -->|Include| PROXY[OHLC Proxy]
    end

    subgraph "Analyzer Components"
        ANALYZERS -->|Include| RISK[RiskMetricsAnalyzer]
        ANALYZERS -->|Include| EQUAL_WEIGHT[EqualWeightBuyHoldAnalyzer]
        ANALYZERS -->|Include| RETURN_ATTR[ReturnAttributionAnalyzer]
        ANALYZERS -->|Include| XIRR[XIRRAnalyzer]
    end

    PORTFOLIO_FACTORY -.->|Configure| TRADE_STRAT
    PORTFOLIO_FACTORY -.->|Configure| CAP_STRAT
    PORTFOLIO_MANAGER -.->|Use| BT_ADAPTER
    PORTFOLIO_MANAGER -.->|Use| SIZER_ADAPTER
    PORTFOLIO_MANAGER -.->|Use| DATA_ADAPTER
    PORTFOLIO_MANAGER -.->|Use| DATA_LOADER

    classDef core fill:#f9f,stroke:#333,stroke-width:2px;
    classDef strategy fill:#bbf,stroke:#333,stroke-width:1px;
    classDef adapter fill:#9cf,stroke:#333,stroke-width:1px;
    classDef data fill:#ff9,stroke:#333,stroke-width:1px;

    class PORTFOLIO_MANAGER,CEREBRO,PORTFOLIO_FACTORY,TASK_RUNNER core;
    class TRADE_STRAT,CAP_STRAT,CHANDELIER,DMA,TMA,RSI,BUY_HOLD,PERCENT,SIMPLE_PERCENT,FIXED_INVEST strategy;
    class BT_ADAPTER,SIZER_ADAPTER,DATA_ADAPTER adapter;
    class DATA_LOADER,DATA_SOURCE,REDIS,S3,PROXY data;
```

### 回测流程

单个投资组合回测执行的流程如下：

```mermaid
sequenceDiagram
    participant U as User/System Event
    participant TR as TaskRunner
    participant PF as PortfolioFactory
    participant PM as PortfolioManager
    participant DL as DataLoader
    participant BT as Backtrader Engine
    participant DC as DataCollector
    participant SC as SignalCollector
    participant DB as Database
    participant S3 as S3 Storage

    U->>TR: Trigger Backtest Request
    TR->>PF: Get Portfolio Config
    alt Traditional Strategy
        PF->>PM: Create PortfolioManager with Traditional Strategy
    else Primitive Strategy
        PF->>DL: Request Market Data
        DL->>DL: Fetch Data from Cache/API
        DL-->>PF: Return Market Data

        PF->>PF: Load Primitive Strategy Config
        PF->>PF: Create ComponentRegistry
        PF->>PF: Create SignalEvaluator
        PF->>PF: Evaluate Strategy Components
        PF->>PM: Create PortfolioManager with Primitive Strategy
    end

    PM->>DL: Request Market Data (if needed)
    DL->>DL: Fetch Data from Cache/API
    DL-->>PM: Return Market Data

    par Preparation Phase
        PM->>SC: Collect Trade Signals
        SC->>SC: Generate Historical Signals
        SC-->>PM: Return Signal Data

        PM->>BT: Configure Cerebro Engine
        PM->>BT: Add Trading Strategy
        PM->>BT: Add Capital Strategy
        PM->>BT: Add Market Data
        PM->>BT: Add Analyzers
    end

    PM->>BT: Execute Backtest
    BT-->>PM: Return Backtest Results

    PM->>DC: Transform and Collect Result Data
    DC->>DC: Process Net Value Data
    DC->>DC: Process Trade Records
    DC->>DC: Process Position Records
    DC->>DC: Process Risk Metrics
    DC->>DC: Generate Portfolio Status

    par Data Persistence
        DC->>DB: Store Results to SQLite
        SC->>DB: Store Signals to SQLite

        PM->>PM: Generate Charts
        PM->>PM: Generate Notifications/Emails

        PM->>S3: Sync Results to S3
    end

    PM-->>TR: Complete Backtest
    TR-->>U: Return Processing Status

    Note over PM,BT: Backtest process uses CustomBroker
    Note over DC,DB: Results include net value, trades, positions, capital records
    Note over PM,S3: Optional sync results to S3 storage
```

### 部署架构

系统的部署架构如下：

```mermaid
flowchart TD
    subgraph "Fly.io Platform"
        subgraph "invest-strategy-service"
            MAIN_APP[Main Service Container] -->|Consume Tasks| REDIS_QUEUE[(Redis Queue)]
            MAIN_APP -->|Manage| PORTFOLIO_DB[(Portfolio Database)]
            MAIN_APP -->|Manage| SIGNALS_DB[(Signals Database)]
        end

        subgraph "machine-manager"
            MACHINE_MANAGER[Machine Manager] -->|Monitor| REDIS_QUEUE
            MACHINE_MANAGER -->|Call| FLY_API[Fly.io API]
        end

        subgraph "Worker Machines"
            WORKER1[Worker 1]
            WORKER2[Worker 2]
            WORKER3[Worker 3]
            WORKERN[Worker n]
        end

        subgraph "Dependent Services"
            REDIS_UPSTASH[(Upstash Redis)]
            KAFKA[(Upstash Kafka)]
            INVEST_OHLC[invest-ohlc-proxy]
        end

        FLY_API -->|Manage| WORKER1
        FLY_API -->|Manage| WORKER2
        FLY_API -->|Manage| WORKER3
        FLY_API -->|Manage| WORKERN

        MACHINE_MANAGER -->|Control| MACHINE_MANAGER_DB[(State Database)]

        MAIN_APP -->|Use| REDIS_UPSTASH
        MAIN_APP -->|Subscribe| KAFKA
        MAIN_APP -->|Request Data| INVEST_OHLC

        WORKER1 -->|Consume Tasks| REDIS_QUEUE
        WORKER2 -->|Consume Tasks| REDIS_QUEUE
        WORKER3 -->|Consume Tasks| REDIS_QUEUE
        WORKERN -->|Consume Tasks| REDIS_QUEUE
    end

    subgraph "External Services"
        S3[(AWS S3 Storage)]
        EMAIL[Email Service]
        SLACK[Slack Service]
    end

    MAIN_APP -->|Store Results| S3
    MAIN_APP -->|Send Notifications| EMAIL
    MAIN_APP -->|Send Notifications| SLACK

    subgraph "GitHub Actions"
        GITHUB_ACTIONS[CI/CD Workflow]
    end

    GITHUB_ACTIONS -->|Deploy| MAIN_APP
    GITHUB_ACTIONS -->|Deploy| MACHINE_MANAGER
    GITHUB_ACTIONS -->|Auto-scale| FLY_API

    CLIENT[Client] -->|API Request| MAIN_APP
    PRODUCER[Message Producer] -->|Generate Message| KAFKA

    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#ff9,stroke:#333,stroke-width:1px;
    classDef external fill:#9cf,stroke:#333,stroke-width:1px;

    class MAIN_APP,MACHINE_MANAGER primary;
    class WORKER1,WORKER2,WORKER3,WORKERN secondary;
    class REDIS_QUEUE,REDIS_UPSTASH,PORTFOLIO_DB,SIGNALS_DB,MACHINE_MANAGER_DB,S3 storage;
    class EMAIL,SLACK,GITHUB_ACTIONS,CLIENT,PRODUCER external;
```

## 服务组件

### app

主服务，负责执行策略回测和生成交易信号。包含以下功能：

- 策略回测引擎
- 投资组合管理
- 交易信号生成
- 原语引擎系统
  - 组件注册表
  - 信号评估器
  - 指标和信号原语
  - 市场指标管理器
- 邮件通知

### machine-manager

轻量级任务调度器，负责管理 Fly Machines 资源，优化计算资源利用和成本。特点：

- 基于 Redis 队列长度动态管理工作机器数量
- 启动/停止 Worker 机器而非销毁，减少冷启动时间
- 健康监控和任务追踪
- 优化运行成本，非活跃期间自动停机

```mermaid
flowchart TD
    subgraph "Machine Manager App"
        MM[Machine Manager] --> RM[Redis Monitor]
        MM --> WT[Worker Tracker]
        MM <--> FA[Fly API Client]
    end

    subgraph "External Services"
        R[(Redis Queue)] <--> RM
        R <--> WT
        FA <--> FA_EXT[Fly.io API]
    end

    subgraph "Worker Machines"
        W1[Worker 1]
        W2[Worker 2]
        W3[Worker 3 - n]
    end

    FA_EXT --> W1
    FA_EXT --> W2
    FA_EXT --> W3

    R <--> W1
    R <--> W2
    R <--> W3

    W1 --> WT
    W2 --> WT
    W3 --> WT

    classDef managerStyle fill:#f9f,stroke:#333,stroke-width:2px;
    classDef redisStyle fill:#f96,stroke:#333,stroke-width:2px;
    classDef workerStyle fill:#9cf,stroke:#333,stroke-width:2px;
    classDef apiStyle fill:#ff9,stroke:#333,stroke-width:2px;

    class MM,RM,WT managerStyle;
    class R redisStyle;
    class W1,W2,W3 workerStyle;
    class FA,FA_EXT apiStyle;
```

## 部署模式

- **开发模式**：单机运行，通过命令行直接启动
- **生产模式**：在 Fly.io 上部署，使用 machine-manager 进行资源管理

## Limitation

- 交易策略
  - 不支持做空
    - 做空会增加策略的复杂度，而且并不适合普通投资者
  - 日线级别
    - 高频交易不适合日线级别，而且也不适合普通投资者
- 投资组合
  - 不支持多市场标的的组合
    - 多市场标的的组合会增加策略的复杂度，而且并不适合普通投资者

## 使用方法

### 本地开发

基本使用方式：

```bash
python main_v2.py --mode local --code <portfolio_code>
```

更多选项：

```bash
# 回测后上传结果到S3
python main_v2.py --mode local --s3 --code <portfolio_code>

# 生成投资组合表现视频
python main_v2.py --mode local --video --code <portfolio_code>

# 指定开始和结束日期
python main_v2.py --mode local --code <portfolio_code> --start-date "2020-01-01" --end-date "2023-12-31"
```

详细配置和命令选项请参考 [app/README.md](app/README.md)。

### 生产部署

主应用和任务调度器分别部署：

```bash
# 部署主服务
fly deploy -a invest-strategy-service

# 部署任务调度器
cd machine-manager
fly deploy -a machine-manager
```

自动部署通过 GitHub Actions 实现，详见 [.github/workflows/deploy.yml](.github/workflows/deploy.yml)。

### 扩展资源

- 投资组合配置：参考 [app/portfolio_config.json](app/portfolio_config.json)
- 交易策略实现：参考 [app/trade_strategies/](app/trade_strategies/) 目录
- 资金策略实现：参考 [app/capital_strategies/](app/capital_strategies/) 目录
- 原语组件：
  - 指标原语：参考 [app/components/indicators/](app/components/indicators/) 目录
  - 信号原语：参考 [app/components/signals/](app/components/signals/) 目录
  - 原语策略配置：参考 [app/config/primitive_strategies/](app/config/primitive_strategies/) 目录
- 架构文档：参考 [doc/design/arch/v4.md](doc/design/arch/v4.md)
- 测试架构：参考 [app/tests/README.md](app/tests/README.md)
