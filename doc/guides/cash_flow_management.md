# 现金流管理指南

本指南详细介绍了 investStrategyService 中的现金流管理系统，包括统一现金流接口、净值调整机制、收益率计算等核心功能。

## 目录

- [概述](#概述)
- [统一现金流接口](#统一现金流接口)
- [现金流类型](#现金流类型)
- [实现自定义现金流策略](#实现自定义现金流策略)
- [净值调整机制](#净值调整机制)
- [收益率计算](#收益率计算)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 概述

现金流管理系统是 investStrategyService 的核心功能之一，专门处理投资过程中的资金进出。该系统特别适合：

- **定投策略**：定期定额投资
- **分红再投资**：自动将分红收益再次投入
- **动态调仓**：根据市场条件调整资金配置
- **风险管理**：止损撤资、紧急撤资等

### 核心特性

1. **统一接口**：所有现金流类型使用相同的API接口
2. **自动净值调整**：使用时间加权收益率确保净值计算准确性
3. **多种收益率计算**：提供策略表现和投资者实际收益两种视角
4. **风控管理**：内置风险控制和限额管理
5. **完整记录**：详细的现金流历史记录和分析

## 统一现金流接口

### CashFlowInterface

所有需要处理现金流的策略都应该实现 `CashFlowInterface` 接口：

```python
from capital_strategies.cash_flow_interface import CashFlowInterface, CashFlowTypes
from datetime import date
from typing import Dict

class MyCustomStrategy(CashFlowInterface):
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        """
        获取当前日期的所有现金流操作
        
        Args:
            current_date: 当前交易日期
            current_cash: 当前可用现金
            current_value: 当前投资组合总价值
            
        Returns:
            Dict[str, float]: 现金流字典，格式为 {flow_type: amount}
                - amount > 0: 资金流入
                - amount < 0: 资金流出
        """
        cash_flows = {}
        
        # 实现具体的现金流逻辑
        return cash_flows
```

### CashFlowManager

系统提供 `CashFlowManager` 类来处理现金流的通用逻辑：

```python
from capital_strategies.cash_flow_interface import CashFlowManager

class MyStrategy(CashFlowInterface):
    def __init__(self):
        self.cash_flow_manager = CashFlowManager()
        
    def get_cash_flows(self, current_date, current_cash, current_value):
        cash_flows = self._calculate_cash_flows(current_date, current_cash, current_value)
        
        # 使用管理器处理现金流（包含风控和记录）
        return self.cash_flow_manager.process_cash_flows(cash_flows, current_date)
```

## 现金流类型

系统定义了标准化的现金流类型常量：

### 基础投资类型

```python
from capital_strategies.cash_flow_interface import CashFlowTypes

# 定期投资
CashFlowTypes.ANNUAL_INVESTMENT      # 年度定投
CashFlowTypes.MONTHLY_INVESTMENT     # 月度定投
CashFlowTypes.BONUS_INVESTMENT       # 奖金投资

# 分红管理
CashFlowTypes.DIVIDEND_REINVESTMENT  # 分红再投资
```

### 风险管理类型

```python
# 风险控制
CashFlowTypes.STOP_LOSS_WITHDRAWAL   # 止损撤资
CashFlowTypes.EMERGENCY_WITHDRAWAL   # 紧急撤资

# 动态调整
CashFlowTypes.REBALANCING_ADJUSTMENT # 再平衡调整
```

### 自定义类型

```python
# 自定义现金流
CashFlowTypes.CUSTOM                 # 用户自定义类型
```

### 获取所有支持的类型

```python
# 获取所有支持的现金流类型
all_types = CashFlowTypes.get_all_types()
print(all_types)
# ['annual_investment', 'monthly_investment', 'dividend_reinvestment', ...]
```

## 实现自定义现金流策略

### 示例1：动态投资策略

```python
from capital_strategies.cash_flow_interface import AdvancedCashFlowStrategy, CashFlowTypes
from datetime import date, timedelta

class DynamicInvestmentStrategy(AdvancedCashFlowStrategy):
    def __init__(self, params=None):
        super().__init__()
        
        # 策略参数
        self.set_strategy_params({
            'monthly_investment': 1000,      # 月度定投金额
            'dip_buy_threshold': -0.05,      # 下跌5%时加仓
            'dip_buy_multiplier': 2.0,       # 加仓倍数
            'max_monthly_investment': 5000,   # 单月最大投资限额
        })
        
        self.last_investment_month = None
        self.last_portfolio_value = None
    
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        cash_flows = {}
        
        # 1. 月度定投
        monthly_flow = self._calculate_monthly_investment(current_date)
        if monthly_flow > 0:
            cash_flows[CashFlowTypes.MONTHLY_INVESTMENT] = monthly_flow
        
        # 2. 市场下跌加仓
        dip_buy_flow = self._calculate_dip_buying(current_value)
        if dip_buy_flow > 0:
            cash_flows[CashFlowTypes.BONUS_INVESTMENT] = dip_buy_flow
        
        # 3. 应用月度限额
        cash_flows = self._apply_monthly_limits(cash_flows)
        
        # 更新状态
        self.last_portfolio_value = current_value
        
        return self.cash_flow_manager.process_cash_flows(cash_flows, current_date)
    
    def _calculate_monthly_investment(self, current_date: date) -> float:
        """计算月度定投金额"""
        current_month = current_date.strftime('%Y-%m')
        
        if self.last_investment_month == current_month:
            return 0  # 本月已投资
        
        # 检查是否是月初（前5个工作日）
        if current_date.day <= 5:
            self.last_investment_month = current_month
            return self.get_strategy_param('monthly_investment', 1000)
        
        return 0
    
    def _calculate_dip_buying(self, current_value: float) -> float:
        """计算市场下跌时的加仓金额"""
        if self.last_portfolio_value is None:
            return 0
        
        # 计算跌幅
        decline_ratio = (current_value - self.last_portfolio_value) / self.last_portfolio_value
        threshold = self.get_strategy_param('dip_buy_threshold', -0.05)
        
        if decline_ratio <= threshold:
            base_amount = self.get_strategy_param('monthly_investment', 1000)
            multiplier = self.get_strategy_param('dip_buy_multiplier', 2.0)
            
            # 跌幅越大，加仓越多
            intensity = abs(decline_ratio) / abs(threshold)
            return base_amount * multiplier * intensity
        
        return 0
    
    def _apply_monthly_limits(self, cash_flows: Dict[str, float]) -> Dict[str, float]:
        """应用月度投资限额"""
        max_monthly = self.get_strategy_param('max_monthly_investment', 5000)
        
        # 计算本月总流入
        total_inflow = sum(amount for amount in cash_flows.values() if amount > 0)
        
        if total_inflow > max_monthly:
            # 按比例缩减
            scale_factor = max_monthly / total_inflow
            for flow_type, amount in cash_flows.items():
                if amount > 0:
                    cash_flows[flow_type] = amount * scale_factor
        
        return cash_flows
```

### 示例2：止损策略

```python
class StopLossStrategy(AdvancedCashFlowStrategy):
    def __init__(self, params=None):
        super().__init__()
        
        self.set_strategy_params({
            'stop_loss_threshold': -0.15,    # 15%止损线
            'emergency_threshold': -0.25,    # 25%紧急撤资线
            'partial_stop_ratio': 0.5,       # 部分止损比例
            'emergency_stop_ratio': 0.8,     # 紧急止损比例
        })
        
        self.initial_value = None
        self.stop_loss_triggered = False
    
    def get_cash_flows(self, current_date: date, current_cash: float, current_value: float) -> Dict[str, float]:
        if self.initial_value is None:
            self.initial_value = current_value
            return {}
        
        cash_flows = {}
        
        # 计算当前损失比例
        loss_ratio = (current_value - self.initial_value) / self.initial_value
        
        stop_loss_threshold = self.get_strategy_param('stop_loss_threshold', -0.15)
        emergency_threshold = self.get_strategy_param('emergency_threshold', -0.25)
        
        if loss_ratio <= emergency_threshold and not self.stop_loss_triggered:
            # 紧急止损
            emergency_ratio = self.get_strategy_param('emergency_stop_ratio', 0.8)
            amount = current_value * emergency_ratio
            cash_flows[CashFlowTypes.EMERGENCY_WITHDRAWAL] = -amount
            self.stop_loss_triggered = True
            
        elif loss_ratio <= stop_loss_threshold and not self.stop_loss_triggered:
            # 部分止损
            partial_ratio = self.get_strategy_param('partial_stop_ratio', 0.5)
            amount = current_value * partial_ratio
            cash_flows[CashFlowTypes.STOP_LOSS_WITHDRAWAL] = -amount
            self.stop_loss_triggered = True
        
        return self.cash_flow_manager.process_cash_flows(cash_flows, current_date)
```

## 净值调整机制

### 时间加权收益率

系统使用时间加权收益率（Time-Weighted Return）自动调整净值计算，确保现金流不会影响策略表现的评估。

**调整公式**：
```
NAV_t = NAV_{t-1} × (V_t / (V_{t-1} + F_t))
```

其中：
- `NAV_t`：当期净值
- `NAV_{t-1}`：前期净值  
- `V_t`：当期账户价值
- `V_{t-1}`：前期账户价值
- `F_t`：当期现金流入（正数）或流出（负数）

### CashFlowAdjustedNAVObserver

系统通过 `CashFlowAdjustedNAVObserver` 自动处理净值调整：

```python
# 观察者会自动：
# 1. 监听现金流事件
# 2. 应用时间加权收益率公式
# 3. 生成调整后的净值数据
# 4. 提供给数据收集器使用
```

### 调整效果

- **排除现金流影响**：净值计算不受资金流入流出的影响
- **反映真实表现**：净值曲线真实反映投资策略的表现  
- **便于比较**：不同现金流模式的策略可以直接比较净值表现

## 收益率计算

系统提供两种收益率计算方式，满足不同的分析需求：

### 1. 净值法收益率（策略表现）

**用途**：评估投资策略本身的表现

**计算方式**：
```
年化收益率 = (最终净值 / 初始净值)^(1/年数) - 1
```

**特点**：
- 反映投资策略本身的表现
- 排除现金流时间因素的影响
- 适合策略比较和评估

### 2. XIRR收益率（投资者实际收益）

**用途**：评估投资者的实际收益率

**计算方式**：基于现金流的内部收益率
```python
# 现金流示例
cash_flows = [
    {'date': '2020-01-01', 'amount': -10000},  # 初始投资
    {'date': '2020-02-01', 'amount': -1000},   # 月度定投
    {'date': '2020-03-01', 'amount': -1000},   # 月度定投
    # ...
    {'date': '2023-12-31', 'amount': 50000}    # 最终价值
]
xirr = calculate_xirr(dates, amounts)
```

**特点**：
- 反映投资者的实际收益率
- 考虑现金流的时间价值
- 适合投资者个人收益评估

### 收益率对比示例

```
美股定投策略示例：
投资期间：2015-2022年（7年）
总投入：$70,000（年度定投$10,000）
最终价值：$212,209

净值法年化收益率：15.68%（策略表现）
XIRR收益率：10.03%（投资者实际收益）

差异原因：定投策略的资金是分批投入的，
XIRR考虑了资金投入的时间价值。
```

## 最佳实践

### 1. 策略设计

- **明确现金流类型**：使用标准化的现金流类型常量
- **合理设置限额**：避免单次现金流过大影响策略
- **考虑市场条件**：根据市场状况调整现金流策略

### 2. 风险控制

- **设置最大限额**：防止现金流过度集中
- **分散时间**：避免在同一时间点大量现金流
- **监控比例**：现金流占总资产的比例要合理

### 3. 测试验证

- **回测验证**：充分回测现金流策略的有效性
- **边界测试**：测试极端市场条件下的现金流表现
- **收益率分析**：同时关注净值法和XIRR两种收益率

## 故障排除

### 常见问题

1. **现金流未被识别**
   - 检查现金流类型是否使用了正确的常量
   - 确认策略实现了 `CashFlowInterface` 接口
   - 查看日志中的现金流记录

2. **净值计算异常**
   - 检查 `CashFlowAdjustedNAVObserver` 是否正确注册
   - 确认现金流金额和日期的正确性
   - 查看净值调整的日志记录

3. **XIRR计算错误**
   - 检查现金流数据的完整性
   - 确认日期格式的正确性
   - 验证现金流金额的符号（流入为负，流出为正）

### 调试方法

1. **查看日志**：
```bash
grep -A 10 -B 5 "cash flow" data/portfolio_name/portfolio_name_*.log
```

2. **检查数据库**：
```sql
SELECT * FROM capital_records WHERE trade_type LIKE '%investment%';
SELECT * FROM net_values ORDER BY date DESC LIMIT 10;
```

3. **验证计算**：
```python
# 手动验证净值调整计算
nav_prev = 1.0
value_prev = 100000
value_curr = 105000
cash_flow = 1000

nav_curr = nav_prev * (value_curr / (value_prev + cash_flow))
print(f"Adjusted NAV: {nav_curr}")
```

更多详细的故障排除方法，请参考[高级故障排除指南](./primitives_guide/08_advanced_troubleshooting.md)。
