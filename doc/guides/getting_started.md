# investStrategyService 入门指南

本指南将帮助你快速上手 investStrategyService，了解如何安装、配置和使用这个服务来进行投资策略回测和信号生成。

## 目录

- [系统概述](#系统概述)
- [环境准备](#环境准备)
- [安装步骤](#安装步骤)
- [基本使用](#基本使用)
- [配置投资组合](#配置投资组合)
- [运行回测](#运行回测)
- [查看结果](#查看结果)
- [常见问题](#常见问题)

## 系统概述

investStrategyService 是一个可以通过配置文件或远程消息自动回测基于日线级别的交易策略的投资组合的服务，并能生成基于此策略的交易信号及邮件通知。

主要功能包括：

- 基于 Backtrader 的策略回测
- 投资组合管理和优化
- 交易信号生成和通知
- 结果可视化和报告生成
- 分布式任务处理

## 环境准备

### 系统要求

- Python 3.10 或更高版本
- Redis (用于缓存和任务队列)
- 可选：S3 兼容存储 (用于结果存储)

### 依赖服务

在本地开发模式下，你需要连接到以下服务：

1. **Redis 服务**：用于数据缓存和任务队列
2. **invest-ohlc-proxy 服务**：提供市场数据

## 安装步骤

### 1. 克隆代码库

```bash
git clone <repository-url>
cd investStrategyService
```

### 2. 设置环境变量

创建 `.env` 文件并设置必要的环境变量：

```bash
cp .env.example .env
```

然后编辑 `.env` 文件，设置必要的配置项。

### 3. 连接依赖服务

如果使用 Fly.io 托管的服务，需要创建隧道连接：

```bash
# 连接到 Redis
fly redis connect

# 连接到 OHLC 代理服务
flyctl proxy 8000 -a invest-ohlc-proxy
```

## 基本使用

### 本地模式运行

最基本的使用方式是在本地模式下运行特定的投资组合回测：

```bash
python main_v2.py --mode local --code <portfolio_code>
```

例如，运行美股1号投资组合：

```bash
python main_v2.py --mode local --code myinvestpilot_us_1
```

### 其他常用选项

```bash
# 回测后上传结果到 S3
python main_v2.py --mode local --s3 --code <portfolio_code>

# 生成投资组合表现视频
python main_v2.py --mode local --video --code <portfolio_code>

# 指定开始和结束日期
python main_v2.py --mode local --code <portfolio_code> --start-date "2020-01-01" --end-date "2023-12-31"
```

### 服务器模式运行

在服务器环境中，可以启动服务模式，监听来自 Redis 队列的任务：

```bash
python main_v2.py --mode server
```

## 配置投资组合

投资组合配置文件位于 `app/config/portfolio_config.json`，使用 JSON 格式定义不同的投资组合。

### 配置结构

投资组合配置包含以下主要部分：

```json
{
  "portfolios": [
    {
      "name": "投资组合名称",
      "code": "唯一标识符",
      "description": "描述信息",
      "strategy": {
        "name": "策略类名",
        "params": {
          "参数1": 值1,
          "参数2": 值2
        }
      },
      "capital_strategy": {
        "name": "资金策略类名",
        "params": {
          "initial_capital": 初始资金,
          "percents": 资金分配百分比
        }
      },
      "symbols": [
        {"symbol": "股票代码1", "name": "股票名称1"},
        {"symbol": "股票代码2", "name": "股票名称2"}
      ],
      "start_date": "开始日期",
      "currency": "货币类型",
      "market": "市场类型",
      "commission": 佣金率,
      "update_time": "更新时间"
    }
  ]
}
```

### 示例配置

以下是一个简单的投资组合配置示例：

```json
{
  "name": "美股1号",
  "code": "myinvestpilot_us_1",
  "description": "基于特定止损与均线策略的美股杠杆ETF组合",
  "strategy": {
    "name": "ChandelierExitMAStrategy",
    "params": {
      "n_atr": 60,
      "atr_multiplier": 4,
      "n_ma": 250
    }
  },
  "capital_strategy": {
    "name": "PercentCapitalStrategy",
    "params": {
      "initial_capital": 100000,
      "percents": 20
    }
  },
  "symbols": [
    {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"},
    {"symbol": "SOXL", "name": "Semiconductor 3x Bull"}
  ],
  "start_date": "2018-01-01",
  "currency": "USD",
  "market": "US",
  "commission": 0.0001,
  "update_time": "08:00"
}
```

## 运行回测

### 执行回测

配置好投资组合后，可以使用以下命令执行回测：

```bash
python main_v2.py --mode local --code myinvestpilot_us_1
```

### 指定日期范围

可以通过 `--start-date` 和 `--end-date` 参数指定回测的日期范围：

```bash
python main_v2.py --mode local --code myinvestpilot_us_1 --start-date "2020-01-01" --end-date "2023-12-31"
```

## 查看结果

回测结果将保存在 `app/data/<portfolio_code>` 目录下，包括：

- 净值数据
- 交易记录
- 持仓记录
- 风险指标
- 图表

## 常见问题

### 1. 数据加载失败

**问题**: 回测过程中出现数据加载错误。

**解决方案**:
- 确认 Redis 连接是否正常
- 确认 OHLC 代理服务是否可访问
- 检查股票代码是否正确
- 检查日期范围是否有效

### 2. 策略参数调整

**问题**: 如何调整策略参数以获得更好的回测结果？

**解决方案**:
- 修改 `portfolio_config.json` 中相应投资组合的策略参数
- 运行多次回测，比较不同参数下的结果
- 参考 [策略配置指南](./strategy_configuration.md) 了解更多参数调整建议

### 3. 自定义策略开发

**问题**: 如何开发自定义交易策略？

**解决方案**:
- 传统方式：在 `app/trade_strategies` 目录下创建新的策略类
- 原语系统：使用原语组合创建策略，参考 [原语系统指南](./primitives_guide/README.md)

### 4. 服务部署问题

**问题**: 如何在生产环境中部署服务？

**解决方案**:
- 使用 Fly.io 部署：`fly deploy -a invest-strategy-service`
- 部署任务调度器：`cd machine-manager && fly deploy -a machine-manager`
- 确保设置了正确的环境变量和服务连接
