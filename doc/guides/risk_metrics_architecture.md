# 风险指标计算架构

本文档详细说明了 investStrategyService 中风险指标计算的架构设计，特别是现金流调整对风险指标计算的重要影响。

## 📋 目录

- [概述](#概述)
- [问题背景](#问题背景)
- [解决方案：混合计算模式](#解决方案混合计算模式)
- [技术实现](#技术实现)
- [开发者指南](#开发者指南)
- [故障排除](#故障排除)

## 概述

### 核心问题

在有现金流的投资组合（如定投策略）中，存在一个重要的数据一致性问题：

- **净值数据**：基于现金流调整后的净值（正确反映策略表现）
- **风险指标**：基于原始 portfolio value 计算（未考虑现金流影响）

这种不一致会导致净值曲线和风险指标（CAGR、回撤等）基于不同的数据源，影响分析的准确性。

### 解决方案

系统采用**混合计算模式**，根据组合是否有现金流自动选择合适的计算方式：

- **无现金流组合**：使用标准的 `RiskMetricsAnalyzer`
- **有现金流组合**：使用混合模式，分离交易指标和价值指标

## 问题背景

### 时序问题

```
Backtrader 执行时序：
1. Strategy 执行 → 生成交易信号
2. RiskMetricsAnalyzer.notify_cashvalue() → 基于原始 portfolio value 计算
3. CashFlowAdjustedNAVObserver → 计算调整后净值
4. 回测结束

问题：RiskMetricsAnalyzer 在步骤 2 就完成了计算，
但调整后净值要到步骤 3 才完整可用！
```

### 数据不一致的影响

#### 定投策略示例

```
错误的计算结果：
- 净值：基于调整后数据，显示合理的策略表现
- CAGR：基于原始数据，可能显示 -6369% 等异常值
- 年度收益率：基于原始数据，2015年显示 -100%

正确的计算结果：
- 净值：基于调整后数据，显示合理的策略表现
- CAGR：基于调整后数据，显示 9.76%，接近 XIRR 的 9.78%
- 年度收益率：基于调整后数据，显示合理的年度表现
```

## 解决方案：混合计算模式

### 架构设计

#### 无现金流组合

```
RiskMetricsAnalyzer → PortfolioManager
     ↓                      ↓
基于原始 portfolio value    直接使用分析器结果
计算所有风险指标
```

#### 有现金流组合

```
RiskMetricsAnalyzer → RiskMetricsCalculator → PortfolioManager
     ↓                        ↓                    ↓
交易相关指标              价值相关指标           合并两套结果
(胜率、盈亏比等)         (基于调整后净值)
```

### 指标分类

#### 交易相关指标（来自 RiskMetricsAnalyzer）

这些指标不受现金流影响，继续使用原始分析器的结果：

- `win_rate`: 胜率
- `profit_loss_ratio`: 盈亏比
- `total_trades`: 总交易次数
- `won_trades`: 盈利交易次数
- `lost_trades`: 亏损交易次数

#### 价值相关指标（基于调整后净值重新计算）

这些指标受现金流影响，需要基于调整后净值重新计算：

- `cagr`: 复合年化收益率
- `current_drawdown`: 当前回撤
- `max_drawdown`: 最大回撤
- `max_drawdown_duration`: 最大回撤持续时间
- `annual_returns`: 年度收益率
- `calmar`: Calmar比率
- `daily_returns`: 日收益率

## 技术实现

### 核心组件

#### 1. RiskMetricsCalculator

独立的风险指标计算引擎，提供纯函数式的计算方法：

```python
class RiskMetricsCalculator:
    @staticmethod
    def calculate_value_metrics(values, dates):
        """基于价值序列计算价值相关的风险指标"""
        # 计算 CAGR、回撤、年度收益率等
        
    @staticmethod
    def get_trade_related_metrics(analyzer_result):
        """从 RiskMetricsAnalyzer 结果中提取交易相关指标"""
        # 提取胜率、盈亏比等交易指标
```

#### 2. PortfolioManager 混合逻辑

```python
def _calculate_risk_metrics_with_cash_flow_adjustment(self, strategy, base_risk_metrics):
    # 检测是否有现金流调整数据
    adjusted_nav_data = self._get_adjusted_nav_data(strategy)
    
    if adjusted_nav_data is not None:
        # 混合模式：分离交易指标和价值指标
        trade_metrics = RiskMetricsCalculator.get_trade_related_metrics(base_risk_metrics)
        value_metrics = RiskMetricsCalculator.calculate_value_metrics(
            adjusted_nav_data['net_value'], 
            adjusted_nav_data.index
        )
        return {**base_risk_metrics, **trade_metrics, **value_metrics}
    else:
        # 原始模式：直接使用分析器结果
        return base_risk_metrics
```

### 自动检测机制

系统自动检测组合是否有现金流调整：

```python
# 查找 CashFlowAdjustedNAVObserver
for observer in strategy.observers:
    if isinstance(observer, CashFlowAdjustedNAVObserver):
        if hasattr(observer, 'get_analysis'):
            cash_flow_adjusted_nav = observer.get_analysis()
            # 发现现金流调整数据，启用混合模式
```

### 数据一致性验证

```python
# 验证现金流调整是否正确
if portfolio_has_cash_flow:
    assert abs(cagr - xirr) < 0.02  # CAGR 和 XIRR 应该接近
    assert net_value_based_on_adjusted_data  # 净值基于调整后数据
    assert risk_metrics_based_on_adjusted_data  # 风险指标基于调整后数据
```

## 开发者指南

### 修改风险指标时

#### 添加新的价值相关指标

如果要添加新的价值相关指标（如夏普比率、索提诺比率等）：

1. 在 `RiskMetricsCalculator.calculate_value_metrics()` 中添加计算逻辑
2. 在 `RiskMetricsAnalyzer` 中添加相应的计算（保持兼容性）
3. 确保两处计算逻辑一致

#### 添加新的交易相关指标

如果要添加新的交易相关指标：

1. 在 `RiskMetricsAnalyzer` 中添加计算逻辑
2. 在 `RiskMetricsCalculator.get_trade_related_metrics()` 中添加提取逻辑
3. 确保指标不受现金流影响

### 测试验证

#### 单元测试

```python
def test_risk_metrics_calculator():
    # 测试价值指标计算
    values = [100, 105, 110, 108, 115]
    dates = pd.date_range('2020-01-01', periods=5)
    
    metrics = RiskMetricsCalculator.calculate_value_metrics(values, dates)
    
    assert 'cagr' in metrics
    assert 'max_drawdown' in metrics
    assert 'annual_returns' in metrics
```

#### 集成测试

```python
def test_hybrid_mode_integration():
    # 运行有现金流的组合
    portfolio_result = run_portfolio('myinvestpilot_us_dip_1')
    
    # 验证数据一致性
    cagr = portfolio_result['cagr']
    xirr = portfolio_result['xirr']
    
    assert abs(cagr - xirr) < 0.02  # CAGR 和 XIRR 应该接近
```

### 日志监控

系统会记录详细的计算过程日志：

```
INFO: No cash flow adjustment detected, using original analyzer results
INFO: Using hybrid mode: combining trade metrics from analyzer with value metrics from adjusted NAV
INFO: Hybrid metrics: Trade metrics from analyzer, Value metrics from adjusted NAV
```

## 故障排除

### 常见问题

#### 1. 风险指标异常

**症状**：CAGR 过高或过低，年度收益率异常

**排查步骤**：
1. 检查日志中是否有 "Using hybrid mode" 消息
2. 确认 `CashFlowAdjustedNAVObserver` 是否正常工作
3. 验证调整后净值数据是否完整

```bash
# 搜索混合模式日志
grep -i "hybrid mode\|cash flow adjustment" data/portfolio_name/portfolio_name_*.log
```

#### 2. 数据一致性问题

**症状**：CAGR 与 XIRR 差异过大，净值与风险指标不匹配

**排查步骤**：
1. 验证现金流调整计算是否正确
2. 检查 `CashFlowAdjustedNAVObserver` 工作状态
3. 确认使用了正确的计算流程

```python
# 手动验证 CAGR 计算
import sqlite3
import pandas as pd

conn = sqlite3.connect('data/portfolio_name/portfolio_name_portfolio.db')
nav_data = pd.read_sql("SELECT * FROM cash_flow_adjusted_nav ORDER BY date", conn)

if not nav_data.empty:
    start_nav = nav_data['net_value'].iloc[0]
    end_nav = nav_data['net_value'].iloc[-1]
    years = (pd.to_datetime(nav_data['date'].iloc[-1]) - pd.to_datetime(nav_data['date'].iloc[0])).days / 365.25
    calculated_cagr = (end_nav / start_nav) ** (1/years) - 1
    print(f"手动计算 CAGR: {calculated_cagr:.4f}")
```

#### 3. 混合模式未启用

**症状**：有现金流的组合仍然显示异常的风险指标

**排查步骤**：
1. 确认组合配置中有现金流策略
2. 检查 `CashFlowAdjustedNAVObserver` 是否正确注册
3. 验证现金流数据是否生成

```sql
-- 检查现金流数据
SELECT * FROM capital_records WHERE trade_type LIKE '%investment%';
SELECT * FROM cash_flow_adjusted_nav ORDER BY date DESC LIMIT 10;
```

### 最佳实践

1. **开发新功能时**：始终考虑现金流的影响
2. **修改风险指标时**：确保在两个地方同步更新
3. **测试时**：同时测试有无现金流的场景
4. **部署前**：验证数据一致性

---

更多详细信息，请参考：
- [现金流管理指南](./cash_flow_management.md)
- [高级故障排除指南](./primitives_guide/08_advanced_troubleshooting.md)
