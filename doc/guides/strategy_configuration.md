# 传统策略配置指南

本指南详细说明了如何配置和使用 investStrategyService 中的传统交易策略和资金策略。

## 目录

- [配置概述](#配置概述)
- [交易策略](#交易策略)
  - [ChandelierExitMAStrategy](#chandelierexitmastrategy)
  - [DualMovingAverageStrategy](#dualmovingaveragestrategy)
  - [BuyHoldStrategy](#buyholdstrategy)
  - [其他策略](#其他策略)
- [资金策略](#资金策略)
  - [PercentCapitalStrategy](#percentcapitalstrategy)
  - [SimplePercentCapitalStrategy](#simplepercentcapitalstrategy)
  - [FixedInvestmentStrategy](#fixedinvestmentstrategy)
- [投资组合配置](#投资组合配置)
- [参数优化建议](#参数优化建议)

## 配置概述

investStrategyService 的传统策略配置基于 JSON 格式，主要包含两个核心组件：

1. **交易策略（Trade Strategy）**：决定何时买入和卖出，生成交易信号
2. **资金策略（Capital Strategy）**：决定每次交易的资金分配方式

这两个组件在投资组合配置中分别定义，并通过参数进行调整。

## 交易策略

### ChandelierExitMAStrategy

吊灯止损与移动平均线结合的策略，适合趋势跟踪。

**策略逻辑**：
- 当价格高于吊灯止损线且高于移动平均线时买入
- 当价格低于吊灯止损线或低于移动平均线时卖出

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `n_atr` | ATR计算周期 | 60 | 20-100 |
| `atr_multiplier` | ATR乘数 | 4 | 2-6 |
| `n_ma` | 移动平均线周期 | 250 | 50-300 |

**配置示例**：

```json
"strategy": {
  "name": "ChandelierExitMAStrategy",
  "params": {
    "n_atr": 60,
    "atr_multiplier": 4,
    "n_ma": 250
  }
}
```

**适用场景**：
- 强趋势市场
- 波动较大的资产（如杠杆ETF）
- 中长期投资

### DualMovingAverageStrategy

双均线交叉策略，是经典的趋势跟踪策略。

**策略逻辑**：
- 当短期均线上穿长期均线时买入
- 当短期均线下穿长期均线时卖出

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `short_window` | 短期均线周期 | 11 | 5-50 |
| `long_window` | 长期均线周期 | 22 | 20-200 |

**配置示例**：

```json
"strategy": {
  "name": "DualMovingAverageStrategy",
  "params": {
    "short_window": 11,
    "long_window": 22
  }
}
```

**适用场景**：
- 趋势明显的市场
- 指数ETF
- 中期投资

### BuyHoldStrategy

买入并持有策略，适合长期投资和定投。

**策略逻辑**：
- 始终保持买入信号
- 不产生卖出信号

**参数**：
- 无需特殊参数

**配置示例**：

```json
"strategy": {
  "name": "BuyHoldStrategy"
}
```

**适用场景**：
- 长期投资
- 定期定额投资
- 指数基金投资

### 其他策略

系统还支持其他几种交易策略：

- **TrippleMovingAverageStrategy**：三均线策略，增加了中期均线作为确认
- **RsiStrategy**：基于RSI指标的超买超卖策略

## 资金策略

### PercentCapitalStrategy

按百分比分配资金的策略，适合多资产组合。

**策略逻辑**：
- 为每个交易信号分配总资产的固定百分比
- 可以设置最大持仓数量限制

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `percents` | 每个标的分配的资金百分比 | 20 | 5-100 |
| `max_positions` | 最大持仓数量 | 无限制 | 根据组合需求设置 |

**配置示例**：

```json
"capital_strategy": {
  "name": "PercentCapitalStrategy",
  "params": {
    "initial_capital": 100000,
    "percents": 20
  }
}
```

**适用场景**：
- 多资产组合
- 需要分散投资的策略
- 风险控制较严格的投资

### SimplePercentCapitalStrategy

简化版的百分比资金策略，适合单一资产或高集中度投资。

**策略逻辑**：
- 为每个交易信号分配总资产的固定百分比
- 简化版本，计算逻辑更直接

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `percents` | 每个标的分配的资金百分比 | 95 | 50-100 |

**配置示例**：

```json
"capital_strategy": {
  "name": "SimplePercentCapitalStrategy",
  "params": {
    "initial_capital": 100000,
    "percents": 95
  }
}
```

**适用场景**：
- 单一资产投资
- 高集中度投资策略
- 简单的资金管理需求

### FixedInvestmentStrategy

定期定额投资策略，适合长期投资和定投计划。

**策略逻辑**：
- 定期向投资组合注入固定金额
- 支持月度和年度定投频率

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `investment_amount` | 每次定投金额 | 10000 | 根据实际情况设置 |
| `investment_frequency` | 定投频率，'m'为月度，'y'为年度 | 'y' | 'm'或'y' |
| `percents` | 每个标的分配的资金百分比 | 20 | 5-100 |
| `fund_val_start` | 初始净值 | 100.0 | 通常保持默认值 |

**配置示例**：

```json
"capital_strategy": {
  "name": "FixedInvestmentStrategy",
  "params": {
    "initial_capital": 100000,
    "investment_amount": 1000,
    "investment_frequency": "m",
    "percents": 95,
    "fund_val_start": 100.0
  }
}
```

**适用场景**：
- 长期定投计划
- 指数基金投资
- 退休金积累

## 投资组合配置

完整的投资组合配置示例：

```json
{
  "name": "美股定投2号",
  "code": "myinvestpilot_us_dip_2",
  "description": "基于买入持有月度定投策略的 QQQ ETF组合",
  "strategy": {
    "name": "BuyHoldStrategy"
  },
  "capital_strategy": {
    "name": "FixedInvestmentStrategy",
    "params": {
      "initial_capital": 100000,
      "investment_amount": 1000,
      "investment_frequency": "m",
      "percents": 95,
      "fund_val_start": 100.0
    }
  },
  "symbols": [
    {"symbol": "QQQ", "name": "Nasdaq ETF"}
  ],
  "start_date": "2015-01-01",
  "currency": "USD",
  "market": "US",
  "commission": 0.0001,
  "update_time": "08:00"
}
```

## 参数优化建议

### ChandelierExitMAStrategy 参数优化

- **n_atr**：较小的值对市场变化更敏感，较大的值提供更平滑的止损线
  - 波动大的市场：使用较大的值（60-100）
  - 波动小的市场：使用较小的值（20-40）

- **atr_multiplier**：控制止损线的宽松程度
  - 较小的值（2-3）：止损更紧，可能导致更频繁的交易
  - 较大的值（4-6）：止损更宽松，减少假信号但可能增加单次亏损

- **n_ma**：移动平均线周期
  - 短期（50-100）：对趋势变化反应更快，但可能产生更多假信号
  - 长期（200-300）：提供更稳定的趋势确认，减少假信号

### DualMovingAverageStrategy 参数优化

- **短期与长期均线比例**：通常建议长期均线周期是短期均线的2-3倍
  - 经典组合：5/20、10/30、20/60、50/200

- **市场适应性**：
  - 波动大的市场：使用较长的周期（如20/60）
  - 波动小的市场：使用较短的周期（如5/20）

### 资金策略优化

- **分散投资**：对于多资产组合，建议percents设置为10-25，确保资金分散
- **集中投资**：对于高确信度的单一资产，可以将percents设置为80-100
- **定投金额**：定投金额应根据初始资金的比例设置，通常为初始资金的1%-5%
