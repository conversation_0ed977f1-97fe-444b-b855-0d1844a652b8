# 传统策略配置指南

本指南详细说明了如何配置和使用 investStrategyService 中的传统交易策略和资金策略。

## 目录

- [配置概述](#配置概述)
- [交易策略](#交易策略)
  - [ChandelierExitMAStrategy](#chandelierexitmastrategy)
  - [DualMovingAverageStrategy](#dualmovingaveragestrategy)
  - [BuyHoldStrategy](#buyholdstrategy)
  - [其他策略](#其他策略)
- [资金策略](#资金策略)
  - [PercentCapitalStrategy](#percentcapitalstrategy)
  - [SimplePercentCapitalStrategy](#simplepercentcapitalstrategy)
  - [FixedInvestmentStrategy](#fixedinvestmentstrategy)
- [现金流管理](#现金流管理)
  - [统一现金流接口](#统一现金流接口)
  - [现金流类型](#现金流类型)
  - [净值调整机制](#净值调整机制)
  - [收益率计算](#收益率计算)
- [投资组合配置](#投资组合配置)
- [参数优化建议](#参数优化建议)

## 配置概述

investStrategyService 的传统策略配置基于 JSON 格式，主要包含两个核心组件：

1. **交易策略（Trade Strategy）**：决定何时买入和卖出，生成交易信号
2. **资金策略（Capital Strategy）**：决定每次交易的资金分配方式

这两个组件在投资组合配置中分别定义，并通过参数进行调整。

## 交易策略

### ChandelierExitMAStrategy

吊灯止损与移动平均线结合的策略，适合趋势跟踪。

**策略逻辑**：
- 当价格高于吊灯止损线且高于移动平均线时买入
- 当价格低于吊灯止损线或低于移动平均线时卖出

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `n_atr` | ATR计算周期 | 60 | 20-100 |
| `atr_multiplier` | ATR乘数 | 4 | 2-6 |
| `n_ma` | 移动平均线周期 | 250 | 50-300 |

**配置示例**：

```json
"strategy": {
  "name": "ChandelierExitMAStrategy",
  "params": {
    "n_atr": 60,
    "atr_multiplier": 4,
    "n_ma": 250
  }
}
```

**适用场景**：
- 强趋势市场
- 波动较大的资产（如杠杆ETF）
- 中长期投资

### DualMovingAverageStrategy

双均线交叉策略，是经典的趋势跟踪策略。

**策略逻辑**：
- 当短期均线上穿长期均线时买入
- 当短期均线下穿长期均线时卖出

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `short_window` | 短期均线周期 | 11 | 5-50 |
| `long_window` | 长期均线周期 | 22 | 20-200 |

**配置示例**：

```json
"strategy": {
  "name": "DualMovingAverageStrategy",
  "params": {
    "short_window": 11,
    "long_window": 22
  }
}
```

**适用场景**：
- 趋势明显的市场
- 指数ETF
- 中期投资

### BuyHoldStrategy

买入并持有策略，适合长期投资和定投。

**策略逻辑**：
- 始终保持买入信号
- 不产生卖出信号

**参数**：
- 无需特殊参数

**配置示例**：

```json
"strategy": {
  "name": "BuyHoldStrategy"
}
```

**适用场景**：
- 长期投资
- 定期定额投资
- 指数基金投资

### 其他策略

系统还支持其他几种交易策略：

- **TrippleMovingAverageStrategy**：三均线策略，增加了中期均线作为确认
- **RsiStrategy**：基于RSI指标的超买超卖策略

## 资金策略

### PercentCapitalStrategy

按百分比分配资金的策略，适合多资产组合。

**策略逻辑**：
- 为每个交易信号分配总资产的固定百分比
- 可以设置最大持仓数量限制

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `percents` | 每个标的分配的资金百分比 | 20 | 5-100 |
| `max_positions` | 最大持仓数量 | 无限制 | 根据组合需求设置 |

**配置示例**：

```json
"capital_strategy": {
  "name": "PercentCapitalStrategy",
  "params": {
    "initial_capital": 100000,
    "percents": 20
  }
}
```

**适用场景**：
- 多资产组合
- 需要分散投资的策略
- 风险控制较严格的投资

### SimplePercentCapitalStrategy

简化版的百分比资金策略，适合单一资产或高集中度投资。

**策略逻辑**：
- 为每个交易信号分配总资产的固定百分比
- 简化版本，计算逻辑更直接

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `percents` | 每个标的分配的资金百分比 | 95 | 50-100 |

**配置示例**：

```json
"capital_strategy": {
  "name": "SimplePercentCapitalStrategy",
  "params": {
    "initial_capital": 100000,
    "percents": 95
  }
}
```

**适用场景**：
- 单一资产投资
- 高集中度投资策略
- 简单的资金管理需求

### FixedInvestmentStrategy

定期定额投资策略，适合长期投资和定投计划。现已支持统一现金流管理接口。

**策略逻辑**：
- 定期向投资组合注入固定金额
- 支持月度和年度定投频率
- 自动调整净值计算，排除现金流入对收益率的影响
- 提供准确的XIRR（内部收益率）计算

**参数**：

| 参数 | 说明 | 默认值 | 建议范围 |
|------|------|--------|----------|
| `initial_capital` | 初始资金 | 100000 | 根据实际情况设置 |
| `investment_amount` | 每次定投金额 | 10000 | 根据实际情况设置 |
| `investment_frequency` | 定投频率，'m'为月度，'y'为年度 | 'y' | 'm'或'y' |
| `percents` | 每个标的分配的资金百分比 | 20 | 5-100 |
| `fund_val_start` | 初始净值 | 100.0 | 通常保持默认值 |

**配置示例**：

```json
"capital_strategy": {
  "name": "FixedInvestmentStrategy",
  "params": {
    "initial_capital": 100000,
    "investment_amount": 1000,
    "investment_frequency": "m",
    "percents": 95,
    "fund_val_start": 100.0
  }
}
```

**现金流特性**：
- **净值调整**：系统自动使用时间加权收益率计算净值，排除定投资金流入的影响
- **XIRR计算**：提供考虑现金流时间价值的真实收益率
- **收益率对比**：
  - **净值法收益率**：反映投资策略本身的表现
  - **XIRR收益率**：反映投资者的实际收益率

**适用场景**：
- 长期定投计划
- 指数基金投资
- 退休金积累
- 需要准确收益率分析的投资策略

## 现金流管理

investStrategyService 提供了统一的现金流管理接口，支持多种资金进出场景。这个系统特别适合定投策略、分红再投资、动态调仓等需要处理现金流的投资策略。

### 统一现金流接口

系统使用 `CashFlowInterface` 提供统一的现金流管理：

```python
from capital_strategies.cash_flow_interface import CashFlowInterface, CashFlowTypes

class MyCustomStrategy(CashFlowInterface):
    def get_cash_flows(self, current_date, current_cash, current_value):
        """返回当前日期的所有现金流操作"""
        cash_flows = {}

        # 示例：月度定投
        if self._should_invest_monthly(current_date):
            cash_flows[CashFlowTypes.MONTHLY_INVESTMENT] = 1000

        # 示例：止损撤资
        if self._should_stop_loss(current_value):
            cash_flows[CashFlowTypes.STOP_LOSS_WITHDRAWAL] = -5000

        return cash_flows
```

### 现金流类型

系统支持以下标准化的现金流类型：

| 现金流类型 | 常量 | 说明 | 用途 |
|-----------|------|------|------|
| 年度定投 | `ANNUAL_INVESTMENT` | 每年固定金额投入 | 长期定投计划 |
| 月度定投 | `MONTHLY_INVESTMENT` | 每月固定金额投入 | 定期定额投资 |
| 分红再投资 | `DIVIDEND_REINVESTMENT` | 分红收益再次投入 | 复利增长策略 |
| 止损撤资 | `STOP_LOSS_WITHDRAWAL` | 达到止损条件时撤资 | 风险控制 |
| 再平衡调整 | `REBALANCING_ADJUSTMENT` | 组合再平衡时的资金调整 | 动态调仓 |
| 紧急撤资 | `EMERGENCY_WITHDRAWAL` | 紧急情况下的资金撤出 | 风险管理 |
| 奖金投资 | `BONUS_INVESTMENT` | 额外收入的投资 | 灵活投资 |
| 自定义现金流 | `CUSTOM` | 用户自定义的现金流类型 | 特殊需求 |

### 净值调整机制

系统使用时间加权收益率（Time-Weighted Return）自动调整净值计算：

**调整公式**：
```
NAV_t = NAV_{t-1} × (V_t / (V_{t-1} + F_t))
```

其中：
- `NAV_t`：当期净值
- `NAV_{t-1}`：前期净值
- `V_t`：当期账户价值
- `V_{t-1}`：前期账户价值
- `F_t`：当期现金流入（正数）或流出（负数）

**调整效果**：
- **排除现金流影响**：净值计算不受资金流入流出的影响
- **反映真实表现**：净值曲线真实反映投资策略的表现
- **便于比较**：不同现金流模式的策略可以直接比较净值表现

### 收益率计算

系统提供两种收益率计算方式：

#### 1. 净值法收益率（策略表现）

**计算方式**：基于调整后的净值曲线
```
年化收益率 = (最终净值 / 初始净值)^(1/年数) - 1
```

**特点**：
- 反映投资策略本身的表现
- 排除现金流时间因素的影响
- 适合策略比较和评估

#### 2. XIRR收益率（投资者实际收益）

**计算方式**：基于现金流的内部收益率
```python
# 现金流示例
cash_flows = [
    {'date': '2020-01-01', 'amount': -10000},  # 初始投资
    {'date': '2020-02-01', 'amount': -1000},   # 月度定投
    {'date': '2020-03-01', 'amount': -1000},   # 月度定投
    # ...
    {'date': '2023-12-31', 'amount': 50000}    # 最终价值
]
xirr = calculate_xirr(dates, amounts)
```

**特点**：
- 反映投资者的实际收益率
- 考虑现金流的时间价值
- 适合投资者个人收益评估

**收益率对比示例**：
```
美股定投策略示例：
- 净值法年化收益率：16.70%（策略表现）
- XIRR收益率：6.37%（投资者实际收益）
```

## 投资组合配置

完整的投资组合配置示例：

```json
{
  "name": "美股定投2号",
  "code": "myinvestpilot_us_dip_2",
  "description": "基于买入持有月度定投策略的 QQQ ETF组合",
  "strategy": {
    "name": "BuyHoldStrategy"
  },
  "capital_strategy": {
    "name": "FixedInvestmentStrategy",
    "params": {
      "initial_capital": 100000,
      "investment_amount": 1000,
      "investment_frequency": "m",
      "percents": 95,
      "fund_val_start": 100.0
    }
  },
  "symbols": [
    {"symbol": "QQQ", "name": "Nasdaq ETF"}
  ],
  "start_date": "2015-01-01",
  "currency": "USD",
  "market": "US",
  "commission": 0.0001,
  "update_time": "08:00"
}
```

## 参数优化建议

### ChandelierExitMAStrategy 参数优化

- **n_atr**：较小的值对市场变化更敏感，较大的值提供更平滑的止损线
  - 波动大的市场：使用较大的值（60-100）
  - 波动小的市场：使用较小的值（20-40）

- **atr_multiplier**：控制止损线的宽松程度
  - 较小的值（2-3）：止损更紧，可能导致更频繁的交易
  - 较大的值（4-6）：止损更宽松，减少假信号但可能增加单次亏损

- **n_ma**：移动平均线周期
  - 短期（50-100）：对趋势变化反应更快，但可能产生更多假信号
  - 长期（200-300）：提供更稳定的趋势确认，减少假信号

### DualMovingAverageStrategy 参数优化

- **短期与长期均线比例**：通常建议长期均线周期是短期均线的2-3倍
  - 经典组合：5/20、10/30、20/60、50/200

- **市场适应性**：
  - 波动大的市场：使用较长的周期（如20/60）
  - 波动小的市场：使用较短的周期（如5/20）

### 资金策略优化

- **分散投资**：对于多资产组合，建议percents设置为10-25，确保资金分散
- **集中投资**：对于高确信度的单一资产，可以将percents设置为80-100
- **定投金额**：定投金额应根据初始资金的比例设置，通常为初始资金的1%-5%
