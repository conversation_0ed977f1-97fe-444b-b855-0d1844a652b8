# 策略原语系统故障排除指南

本文档提供了使用策略原语系统时可能遇到的常见问题及其解决方案。通过检查这些常见问题，您可以更快地诊断和修复策略配置中的错误。

## 配置错误

### 1. 引用不存在的组件

**症状**：回测失败，错误消息提示找不到特定ID的组件。

**示例错误**：
```
KeyError: 'Component ID "rsi_indicator" not found in registry'
```

**解决方案**：
- 检查指标和信号ID的拼写与大小写
- 确保先定义组件，然后再引用它们
- 使用相对路径处理策略配置中组件ID引用

### 2. 参数类型不匹配

**症状**：组件初始化失败，通常伴随类型错误消息。

**示例错误**：
```
TypeError: Expected 'period' parameter to be an integer, got string
```

**解决方案**：
- 检查所有数值参数没有被引号包围
- 确保布尔值使用true/false（不带引号）
- 检查数组和对象的格式正确

### 3. 缺少必需参数

**症状**：组件初始化失败，错误提示缺少参数。

**示例错误**：
```
ValueError: Missing required parameter 'comparison' for Comparison signal
```

**解决方案**：
- 查阅每个组件的文档，确保提供所有必需参数
- 检查参数名称是否正确（例如，`period`而不是`periods`）

### 4. 输入数量不匹配

**症状**：信号评估失败，错误消息提示输入数量错误。

**示例错误**：
```
ValueError: Crossover requires exactly 2 inputs, got 1
```

**解决方案**：

- 检查每个信号原语所需的输入数量
- 确保每个输入都有有效的引用或列名

## 数据和信号问题

### 1. 信号不触发

**症状**：回测显示没有交易或交易次数远少于预期。

**可能原因**：
- 条件过于严格
- 使用了错误的信号类型
- 数据预处理问题

**解决方案**：
- 输出中间指标和信号，检查计算值是否符合预期
- 放宽阈值或条件
- 确保使用正确的信号类型（如Crossover而非GreaterThan）
- 检查指标是否正确计算（查看原始值与指标值）

### 2. 信号过于频繁

**症状**：策略产生过多交易，可能导致过度交易和高滑点/手续费。

**可能原因**：
- 条件过于宽松
- 误用状态比较作为信号（如GreaterThan而非Crossover）
- 阈值设置不合理

**解决方案**：
- 加入额外的过滤条件（如趋势过滤）
- 使用Crossover/Crossunder代替GreaterThan/LessThan
- 添加Streak组件要求连续确认
- 调整阈值更极端

### 3. 信号延迟问题

**症状**：交易信号总是在价格已经大幅走势后才触发。

**可能原因**：
- 使用过长周期的指标
- 多层级信号组合导致延迟累加

**解决方案**：
- 减少指标周期（如使用较短的移动平均）
- 简化信号链，减少不必要的组合层级
- 考虑添加预测性指标，如价格动量

### 4. NaN值处理问题

**症状**：策略在初始阶段不产生信号，或出现不一致的行为。

**可能原因**：
- 指标计算初期产生NaN值
- 未正确处理NaN传播

**解决方案**：
- 确保回测期足够长，涵盖指标预热期
- 检查指标原语是否正确处理NaN（如RSI通常用50填充初始NaN）
- 添加明确的NaN检查和处理步骤

## 策略行为异常

### 1. 不符合预期的持仓状态转换

**症状**：策略在不应该买入或卖出时生成信号，或未能在应该交易时生成信号。

**可能原因**：
- 状态机逻辑问题
- 信号时机不准确
- 条件组合逻辑错误

**解决方案**：
- 仔细检查SignalEvaluator中的状态转换逻辑
- 验证买入/卖出条件是否互斥
- 使用日志和信号表分析状态转换

### 2. 回测结果不一致

**症状**：相同配置的多次回测产生显著不同的结果。

**可能原因**：
- 随机性组件
- 数据加载问题
- 精度或浮点比较问题

**解决方案**：
- 确保策略中没有随机元素
- 验证数据源加载一致性
- 在GreaterThan/LessThan中使用epsilon处理浮点比较

### 3. 信号抖动

**症状**：策略在短时间内频繁切换买入/卖出状态。

**可能原因**：
- 使用价格或指标的绝对值比较
- 缺少状态持续性要求
- 条件太接近临界值

**解决方案**：
- 使用交叉事件而非状态比较
- 添加Streak要求确认持续一定时间
- 增加"滞后带"或双阈值（如RSI使用30/70而非单一50值）

## 性能优化问题

### 1. 过度拟合

**症状**：策略在历史回测中表现极佳，但在新数据上表现差。

**可能原因**：
- 参数过度优化
- 策略复杂度过高
- 回测样本选择偏差

**解决方案**：
- 使用更简单的策略
- 减少调整的参数数量
- 采用时间外样本验证
- 测试多个市场环境

### 2. 高回撤

**症状**：策略在某些市场环境下经历显著的资金损失。

**可能原因**：
- 缺少止损机制
- 缺乏适应市场变化的能力
- 仓位规模过大

**解决方案**：
- 添加移动止损原语（如ChandelierExit）
- 使用动态仓位大小（如基于波动性）
- 增加市场环境过滤器（如趋势强度）

### 3. 低收益率

**症状**：策略交易频率合理，但整体收益率不高。

**可能原因**：
- 获利太早
- 止损太近
- 信号质量不高

**解决方案**：
- 使用跟踪止盈
- 优化买入/卖出时机
- 增加额外的信号确认要求
- 测试不同的资金管理策略

## 系统和环境问题

### 1. 回测执行缓慢

**症状**：回测花费异常长的时间。

**可能原因**：
- 配置不当导致的计算冗余
- 大量无用的中间指标计算
- 使用过于复杂的原语组合

**解决方案**：
- 消除未使用的指标计算
- 简化信号链条
- 确保复杂计算不会重复执行

### 2. 内存消耗过高

**症状**：回测过程中内存使用激增，可能导致系统崩溃。

**可能原因**：
- 处理大量股票或超长时间序列
- 输出太多调试信息
- 存储了不必要的中间结果

**解决方案**：
- 减少输出的指标数量
- 限制回测的时间范围或股票数量
- 确保不保存不必要的中间计算结果

## 调试技巧

### 1. 输出关键指标

在策略的"outputs"部分配置中间指标输出：

```json
"outputs": {
  "buy_signal": "buy_signal",
  "sell_signal": "sell_signal",
  "indicators": [
    { "id": "rsi_indicator", "output_name": "rsi" },
    { "id": "is_below_lower", "output_name": "is_oversold" }
  ]
}
```

### 2. 分解复杂信号

将复杂组合分解为多个中间信号，并输出每个组件：

```json
"signals": [
  { "id": "condition1", "type": "..." },
  { "id": "condition2", "type": "..." },
  { "id": "combined", "type": "And", "inputs": [
    { "ref": "condition1" },
    { "ref": "condition2" }
  ]}
]
```

### 3. 单元测试技巧

为测试特定组件，创建最小化测试配置：

```json
{
  "indicators": [
    {
      "id": "test_indicator",
      "type": "RSI",
      "params": { "period": 14, "field": "Close" }
    },
    {
      "id": "threshold",
      "type": "Constant",
      "params": { "value": 30 }
    }
  ],
  "signals": [
    {
      "id": "test_signal",
      "type": "Crossunder",
      "params": { "mode": "simple" },
      "inputs": [
        { "ref": "test_indicator" },
        { "ref": "threshold" }
      ]
    }
  ],
  "outputs": {
    "buy_signal": "test_signal",
    "sell_signal": "false_signal",
    "indicators": [
      { "id": "test_indicator", "output_name": "indicator" },
      { "id": "test_signal", "output_name": "signal" }
    ]
  }
}
```

### 4. 验证回测数据

使用原始回测数据检查信号生成正确性：

```python
# 手动验证信号生成
signal_data = pd.read_csv('signal_data.csv')
dates_with_buy = signal_data[signal_data['buy_signal'] == True].index
for date in dates_with_buy:
    prev_rsi = signal_data.loc[date-1, 'rsi']
    curr_rsi = signal_data.loc[date, 'rsi']
    threshold = 30
    print(f"Date: {date}, RSI: {prev_rsi}->{curr_rsi}, Threshold: {threshold}")
    # 检查是否确实穿越了阈值
    assert prev_rsi > threshold and curr_rsi <= threshold
```

## 常见组件错误模式

### 1. RSI使用陷阱

- **误区**：将RSI < 30作为买入信号，而不是RSI下穿30
- **结果**：只要RSI在30以下就持续发出买入信号
- **修正**：使用Crossunder而非LessThan

### 2. 移动平均线交叉问题

- **误区**：误解Crossover的输入顺序
- **结果**：买入卖出信号反转
- **修正**：确保正确的输入顺序（第一个输入应该上穿第二个输入）

### 3. 逻辑运算符混淆

- **误区**：混淆And/Or的语义
- **结果**：生成错误的组合信号
- **修正**：明确理解每个运算符的作用（And要求所有条件满足，Or只需一个满足）

### 4. 信号与状态混淆

- **误区**：使用状态指标作为交易触发
- **结果**：产生过度交易
- **修正**：区分状态指标与触发信号的不同用途

## 兼容性和升级问题

### 1. 版本兼容性

如果系统升级后策略不再工作：

- 检查原语组件名称是否变更
- 验证参数命名是否一致
- 查看输入/输出格式是否变更

### 2. 新原语集成

集成新添加的原语时：

- 确保了解新原语的确切行为和参数
- 开始时使用简单配置测试
- 逐步集成到复杂策略中

### 3. 配置迁移

将策略从旧格式迁移到新格式：

- 创建详细的映射表
- 保留原始配置作为参考
- 分阶段迁移并测试每个阶段

## 寻求帮助

如遇到无法解决的问题：

1. 查阅原语系统文档（本指南）
2. 检查示例策略配置
3. 阅读特定原语的实现代码和测试
4. 与团队其他成员讨论问题
5. 添加日志和调试输出以获取更多信息
