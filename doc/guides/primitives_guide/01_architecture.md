# 策略原语系统架构

## 系统概述

策略原语系统是一个声明式交易策略构建框架，允许通过配置而非代码编写来创建复杂的交易策略。该系统的核心理念是将交易策略分解为小型、可组合的构建块（原语），这些构建块可以组合成任意复杂度的交易策略。

> **注意**：完整的系统架构图和详细设计可以在 [架构设计 v4](../../design/arch/v4.md) 文档中找到。

## 核心概念

### 基础组件

原语系统建立在两种基本组件类型之上：

1. **指标原语（BaseIndicatorPrimitive）**：从OHLCV价格数据计算技术指标
2. **信号原语（BaseSignalPrimitive）**：评估条件并生成布尔信号

这两类原语是所有交易策略的基础构建块。系统使用组件注册表来管理所有可用的原语，并在运行时动态加载它们。

### 配置结构

策略定义采用JSON格式，具有以下主要部分：

```json
{
  "trade_strategy": {
    "indicators": [...],  // 指标定义
    "signals": [...],     // 信号定义
    "outputs": {          // 输出映射
      "buy_signal": "signal_id",
      "sell_signal": "signal_id",
      "indicators": [...]
    }
  },
  "capital_strategy": {...}  // 资金管理策略
}
```

### 数据流

原语系统的数据流如下：

1. 市场数据（OHLCV）输入到指标原语
2. 指标原语生成时间序列指标值
3. 信号原语接收指标值（或原始价格数据）作为输入
4. 信号原语生成布尔信号序列（true/false值）
5. 复合信号通过组合基本信号生成
6. 最终的买入/卖出信号映射到特定的信号ID
7. 信号评估器将布尔信号转换为实际交易信号（买入、卖出、持有、空仓）

## 状态机逻辑

策略执行遵循严格的状态机逻辑，确保交易信号的一致性：

- **空仓(EMPTY)** → 买入信号 → **买入(BUY)** → **持有(HOLD)**
- **持有(HOLD)** → 卖出信号 → **卖出(SELL)** → **空仓(EMPTY)**

这种状态机设计确保策略遵循合理的交易逻辑，不会出现信号抖动或不一致的状态转换。

## 组件注册与发现

系统使用组件注册表（ComponentRegistry）自动发现和注册所有可用的原语。这允许添加新原语而无需修改核心系统代码。

注册表将类名映射到实际的类实现，允许在策略配置中通过简单的字符串名称引用原语，如"RSI"、"Comparison"等。

## 指标与信号的区别

**指标原语**：
- 输入：OHLCV价格数据
- 输出：时间序列（pandas.Series）
- 示例：RSI, SMA, MACD

**信号原语**：
- 输入：一个或多个时间序列
- 输出：布尔时间序列（true/false值）
- 示例：CrossAbove, Comparison, And

## 扩展性

系统被设计为可扩展的，可以通过以下方式添加新功能：

1. 添加新的指标原语（继承BaseIndicatorPrimitive）
2. 添加新的信号原语（继承BaseSignalPrimitive）
3. 创建新的预定义策略模式

所有新原语将被自动发现并注册到系统中，无需修改核心代码。
