# 原语组件高级故障排除指南

本文档提供了原语组件系统的高级故障排除技巧，特别关注数据分析和调试方法。通过这些技巧，您可以更有效地识别和解决复杂的配置问题。

## 使用SQLite分析信号和指标

原语系统将计算结果存储在SQLite数据库中，这为故障排除提供了强大的工具。

### 1. 查询信号表

信号表包含了所有计算的指标和信号，是故障排除的首要资源：

```bash
# 查询特定日期范围内的信号
sqlite3 data/portfolio_name/portfolio_name_signals.db \
  "SELECT date, symbol, signal, indicator1, indicator2, buy_condition, sell_condition \
   FROM trade_signals WHERE date >= '2025-05-01' AND date <= '2025-05-16' \
   ORDER BY date"
```

### 2. 验证指标计算

检查特定指标的计算结果，确认其值是否符合预期：

```bash
# 检查RSI指标的计算结果
sqlite3 data/portfolio_name/portfolio_name_signals.db \
  "SELECT date, symbol, close, rsi_indicator \
   FROM trade_signals WHERE symbol = 'AAPL' \
   ORDER BY date DESC LIMIT 30"
```

### 3. 分析买卖信号

检查买入和卖出条件的触发情况，以及最终的交易信号：

```bash
# 分析买卖信号的触发
sqlite3 data/portfolio_name/portfolio_name_signals.db \
  "SELECT date, symbol, signal, buy_condition, sell_condition \
   FROM trade_signals WHERE buy_condition = 1 OR sell_condition = 1 \
   ORDER BY date"
```

### 4. 检查市场指标

验证市场指标（如VIX）的计算和使用是否正确：

```bash
# 检查VIX相关指标
sqlite3 data/portfolio_name/portfolio_name_signals.db \
  "SELECT date, vix_raw, vix_percentile, vix_ma, vix_percentile_low, vix_declining \
   FROM trade_signals WHERE symbol = 'SPY' \
   ORDER BY date DESC LIMIT 30"
```

## 添加日志进行调试

在关键组件中添加日志可以帮助理解组件的行为和数据流。

### 1. 在组件中添加日志

修改组件的evaluate方法，添加详细的日志输出：

```python
def evaluate(self, *inputs: pd.Series, date: pd.Timestamp = None):
    """Evaluate component."""
    if len(inputs) != 2:
        raise ValueError("Component requires exactly two input series")
    
    series_a, series_b = inputs
    
    # 添加调试日志
    module_logger.info(f"Component evaluation - ID: {self.id if hasattr(self, 'id') else 'unknown'}")
    module_logger.info(f"Latest values - series_a: {series_a.iloc[-1]}, series_b: {series_b.iloc[-1]}")
    
    # 特定日期的日志
    if date in series_a.index and date in series_b.index:
        module_logger.info(f"Values on {date} - series_a: {series_a.loc[date]}, series_b: {series_b.loc[date]}")
    
    # 组件逻辑
    result = series_a < series_b
    
    # 记录结果
    module_logger.info(f"Result for latest date: {result.iloc[-1]}")
    
    return result
```

### 2. 分析日志输出

日志输出可以揭示组件的实际行为和数据流：

```
2025-05-17 19:04:25,813 - components.signals.comparison - INFO - Special date 2025-05-12 00:00:00: series_a: 61.84738955823293, series_b: 1.0
2025-05-17 19:04:25,813 - components.signals.comparison - INFO - Comparison on 2025-05-12 00:00:00: 61.84738955823293 < 1.0 = False
```

从这个日志中，我们可以看到LessThan组件在比较VIX百分位（61.85）与1.0，而不是预期的80，这揭示了配置问题。

## 案例研究：市场过滤策略调试

以下是一个实际案例，展示如何使用SQLite和日志调试复杂的原语配置问题。

### 问题描述

在一个基于VIX指标过滤的市场策略中，我们遇到了两个问题：

1. VIX百分位低信号（vix_percentile_low）始终为0，即使VIX百分位已经降至阈值以下
2. 买入和卖出信号同时出现，导致策略无法正确执行交易

### 问题分析步骤

#### 1. 检查信号表中的VIX百分位和相关信号

```bash
sqlite3 data/myinvestpilot_market_filtered/myinvestpilot_market_filtered_signals.db \
  "SELECT date, symbol, vix_percentile, vix_percentile_low \
   FROM trade_signals WHERE date = '2025-05-12'"
```

结果显示，即使VIX百分位为61.85（低于阈值80），vix_percentile_low仍为0：

```
2025-05-12|QQQ|61.84738955823293|0
```

#### 2. 添加日志到LessThan组件

在LessThan组件中添加日志，查看实际的比较值：

```python
# 日志输出
module_logger.info(f"Latest date: {latest_date}, series_a: {latest_a}, series_b: {latest_b}")
module_logger.info(f"Comparison: {latest_a} < {latest_b} = {latest_a < latest_b}")
```

日志结果揭示了问题所在：

```
2025-05-17 19:04:25,813 - components.signals.comparison - INFO - Special date 2025-05-12 00:00:00: series_a: 61.84738955823293, series_b: 1.0
2025-05-17 19:04:25,813 - components.signals.comparison - INFO - Comparison on 2025-05-12 00:00:00: 61.84738955823293 < 1.0 = False
```

VIX百分位（61.85）正在与1.0进行比较，而不是与80比较！

#### 3. 检查买入和卖出信号冲突

查询信号表，发现买入和卖出信号同时为1：

```bash
sqlite3 data/myinvestpilot_market_filtered/myinvestpilot_market_filtered_signals.db \
  "SELECT date, symbol, buy_condition, sell_condition \
   FROM trade_signals WHERE date >= '2025-05-01' AND date <= '2025-05-16'"
```

结果：
```
2025-05-02|QQQ|1|1
2025-05-08|QQQ|1|1
2025-05-09|QQQ|1|1
2025-05-12|QQQ|1|1
```

### 解决方案

#### 1. 修复VIX百分位低信号

问题：在配置中，Constant组件的使用方式不正确。

错误配置：
```json
{
  "id": "market_volatility_low",
  "type": "LessThan",
  "inputs": [
    { "market": "VIX", "transformer": "vix_percentile" },
    { "type": "Constant", "value": 80 }  // 错误：直接在signals部分使用Constant
  ]
}
```

正确配置：
```json
// 在indicators部分定义常量
{
  "id": "constant_80",
  "type": "Constant",
  "params": {
    "value": 80
  }
}

// 在signals部分引用常量
{
  "id": "market_volatility_low",
  "type": "LessThan",
  "epsilon": 0.5,
  "inputs": [
    { "market": "VIX", "transformer": "vix_percentile" },
    { "ref": "constant_80" }  // 正确：引用indicators部分定义的Constant
  ]
}
```

#### 2. 修复买入和卖出信号冲突

问题：sell_condition的定义可能导致与buy_condition重叠。

错误配置：
```json
{
  "id": "sell_signal_condition",
  "type": "Or",
  "inputs": [
    { "ref": "price_lt_ma" },
    { "ref": "price_lt_ce" }
  ]
}
```

正确配置：
```json
// 定义价格条件
{
  "id": "price_conditions_sell",
  "type": "And",
  "inputs": [
    { "ref": "price_lt_ma" },
    { "ref": "price_lt_ce" }
  ]
}

// 定义卖出条件，确保与买入条件互斥
{
  "id": "sell_signal_condition",
  "type": "And",
  "inputs": [
    { "ref": "price_conditions_sell" },
    { 
      "type": "Not",
      "inputs": [{ "ref": "buy_signal_condition" }]
    }
  ]
}
```

### 验证结果

修复后，再次查询信号表：

```bash
sqlite3 data/myinvestpilot_market_filtered/myinvestpilot_market_filtered_signals.db \
  "SELECT date, symbol, signal, vix_percentile, vix_percentile_low, buy_condition, sell_condition \
   FROM trade_signals WHERE date >= '2025-05-01' AND date <= '2025-05-16'"
```

结果：
```
2025-05-12|QQQ|H|61.84738955823293|1|1|0
```

可以看到：
- VIX百分位低信号（vix_percentile_low）现在正确地为1
- 买入条件（buy_condition）为1，卖出条件（sell_condition）为0，不再冲突
- 信号（signal）为H（持有），表明策略正确执行了交易

## 常见陷阱与解决方案

### 1. Constant组件的正确使用

**陷阱**：在signals部分直接使用Constant组件。

**问题**：Constant组件必须在indicators部分定义，然后在signals部分通过ref引用。

**解决方案**：
```json
// 在indicators部分定义
{
  "id": "threshold_value",
  "type": "Constant",
  "params": {
    "value": 30
  }
}

// 在signals部分引用
{
  "id": "below_threshold",
  "type": "LessThan",
  "inputs": [
    { "ref": "some_indicator" },
    { "ref": "threshold_value" }
  ]
}
```

### 2. 浮点数比较问题

**陷阱**：直接比较浮点数可能因精度问题导致意外结果。

**问题**：Python的浮点数比较可能不准确，如`0.1 + 0.2 == 0.3`返回False。

**解决方案**：使用epsilon参数处理浮点数比较：
```json
{
  "id": "price_equals_target",
  "type": "Comparison",
  "params": {
    "comparison": "equal",
    "epsilon": 0.0001
  },
  "inputs": [
    { "ref": "price_indicator" },
    { "ref": "target_value" }
  ]
}
```

### 3. 信号冲突处理

**陷阱**：买入和卖出信号可能同时触发。

**问题**：当买入和卖出条件的定义存在重叠时，可能导致策略行为不确定。

**解决方案**：使用Not组件确保信号互斥：
```json
{
  "id": "sell_signal",
  "type": "And",
  "inputs": [
    { "ref": "sell_condition" },
    { 
      "type": "Not",
      "inputs": [{ "ref": "buy_signal" }]
    }
  ]
}
```

## 高级调试技巧

### 1. 使用SQLite进行数据分析

除了基本查询外，SQLite还支持更复杂的分析：

```bash
# 查找信号变化的日期
sqlite3 data/portfolio_name/portfolio_name_signals.db \
  "SELECT t1.date, t1.symbol, t1.signal, t2.signal AS prev_signal \
   FROM trade_signals t1 \
   JOIN trade_signals t2 ON t1.symbol = t2.symbol AND t1.date = date(t2.date, '+1 day') \
   WHERE t1.signal != t2.signal \
   ORDER BY t1.date"
```

### 2. 比较不同配置的结果

通过比较不同配置的结果，可以识别问题所在：

```bash
# 比较两个不同配置的信号
sqlite3 data/config1/config1_signals.db \
  "SELECT date, symbol, signal AS config1_signal \
   FROM trade_signals" > config1_signals.txt

sqlite3 data/config2/config2_signals.db \
  "SELECT date, symbol, signal AS config2_signal \
   FROM trade_signals" > config2_signals.txt

diff config1_signals.txt config2_signals.txt
```

### 3. 创建临时测试配置

为了隔离测试特定组件，可以创建简化的配置：

```json
{
  "indicators": [
    {
      "id": "test_constant",
      "type": "Constant",
      "params": { "value": 80 }
    }
  ],
  "signals": [
    {
      "id": "test_comparison",
      "type": "LessThan",
      "inputs": [
        { "market": "VIX", "transformer": "vix_percentile" },
        { "ref": "test_constant" }
      ]
    }
  ],
  "outputs": {
    "buy_signal": "test_comparison",
    "sell_signal": "false_signal",
    "indicators": [
      { "id": "test_comparison", "output_name": "test_result" }
    ]
  }
}
```

## 总结

高级故障排除需要综合运用多种工具和技术：

1. **数据分析**：使用SQLite查询分析信号和指标数据
2. **日志调试**：在关键组件中添加日志，了解数据流和计算过程
3. **配置验证**：确保组件的正确使用和参数设置
4. **隔离测试**：创建简化配置，隔离测试特定组件
5. **结果验证**：修复问题后，验证结果是否符合预期

通过这些技巧，您可以更有效地识别和解决原语系统中的复杂问题。
