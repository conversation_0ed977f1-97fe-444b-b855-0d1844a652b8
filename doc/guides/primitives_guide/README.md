# 交易策略原语系统使用指南

## 简介

本文档提供了策略原语系统的完整指南，旨在帮助用户和AI代理正确构建和优化交易策略配置，无需修改代码。通过组合这些基础构建块（原语），可以创建复杂的交易策略并轻松调整参数。

## 目录

0. [入门指南](./00_getting_started_with_primitives.md) - 原语系统快速入门和基础概念
1. [系统架构](./01_architecture.md) - 原语系统的整体架构和工作原理
2. [指标原语](./02_indicator_primitives.md) - 用于计算技术指标的原语组件
3. [信号原语](./03_signal_primitives.md) - 用于生成交易信号的原语组件
4. [组合使用模式](./04_composition_patterns.md) - 常见的原语组合模式和最佳实践
5. [完整策略示例](./05_strategy_examples.md) - 实际策略示例和参数优化指南
6. [故障排除指南](./06_troubleshooting.md) - 常见问题和解决方案
7. [市场指标原语](./07_market_indicators.md) - 引用和使用外部市场指标的原语组件
8. [高级故障排除](./08_advanced_troubleshooting.md) - 使用SQLite和日志进行高级调试
9. [策略优化指南](./09_strategy_optimization.md) - 详细的原语策略参数优化和风险收益平衡
10. [架构局限性](./10_architecture_limitations.md) - 复杂策略分析与系统架构局限性
11. [股债轮动策略优化实战](./11_stock_bond_rotation_optimization_case.md) - 从困境到突破的完整策略优化案例

## 快速入门

原语系统由两种主要组件类型组成：

1. **指标原语（Indicator Primitives）**：从价格/交易量数据计算技术指标（如RSI、移动平均线等）
2. **信号原语（Signal Primitives）**：基于一个或多个输入生成布尔信号（如交叉、比较等）

构建策略的基本流程是：

1. 定义所需的技术指标（如RSI、移动平均线等）
2. 定义信号条件（如RSI低于某值、价格高于移动平均线等）
3. 组合多个信号形成买入和卖出条件
4. 配置输出映射，指定哪些信号和指标应该被暴露

查看[完整策略示例](./05_strategy_examples.md)以了解更多详情。
