# investStrategyService 文档中心

欢迎使用 investStrategyService 文档中心。本文档提供了项目的全面指南，包括安装、配置、使用和开发等方面的详细说明。

## 📚 文档目录

### 📖 入门指南

- [快速入门](./guides/getting_started.md) - 系统概述、安装步骤和基本使用方法
- [传统策略配置](./guides/strategy_configuration.md) - 详细说明如何配置和使用传统交易策略
- [现金流管理指南](./guides/cash_flow_management.md) - 统一现金流接口、净值调整和收益率计算的完整指南
- [风险指标计算架构](./guides/risk_metrics_architecture.md) - 现金流调整对风险指标计算的影响和混合计算模式详解
- [实战案例分析](./guides/case_studies.md) - 不同市场和资产类别的策略应用案例

### 🧩 原语系统指南

- [原语系统概述](./guides/primitives_guide/README.md) - 原语系统的基本概念和使用方法
- [入门指南](./guides/primitives_guide/00_getting_started_with_primitives.md) - 原语系统快速入门和基础概念
- [系统架构](./guides/primitives_guide/01_architecture.md) - 原语系统的整体架构和工作原理
- [指标原语](./guides/primitives_guide/02_indicator_primitives.md) - 用于计算技术指标的原语组件
- [信号原语](./guides/primitives_guide/03_signal_primitives.md) - 用于生成交易信号的原语组件
- [组合使用模式](./guides/primitives_guide/04_composition_patterns.md) - 常见的原语组合模式和最佳实践
- [完整策略示例](./guides/primitives_guide/05_strategy_examples.md) - 实际策略示例和参数优化指南
- [故障排除指南](./guides/primitives_guide/06_troubleshooting.md) - 常见问题和解决方案
- [市场指标原语](./guides/primitives_guide/07_market_indicators.md) - 引用和使用外部市场指标的原语组件
- [高级故障排除](./guides/primitives_guide/08_advanced_troubleshooting.md) - 使用SQLite和日志进行高级调试
- [策略优化指南](./guides/primitives_guide/09_strategy_optimization.md) - 详细的原语策略参数优化和风险收益平衡
- [架构限制详细说明](./guides/primitives_guide/10_architecture_limitations.md) - 更多策略类型和框架的架构限制
- [股债轮动策略优化实战](./guides/primitives_guide/11_stock_bond_rotation_optimization_case.md) - 从困境到突破的完整策略优化案例

### 🤖 AI代理指导

- [AI代理文档概述](./ai_agents/README.md) - 专为AI代理设计的指导文档说明
- [投资组合创建指导](./ai_agents/portfolio_creation_prompt.md) - AI代理创建投资组合的结构化指令
- [投资组合优化指导](./ai_agents/portfolio_optimization_prompt.md) - AI代理优化投资组合的系统性方法
- [问题诊断和故障排除](./ai_agents/troubleshooting_prompt.md) - AI代理诊断和解决策略问题的指导框架
- [基础策略模板](./ai_agents/templates/basic_strategy_templates.json) - 可直接使用的策略配置模板
- [优化检查清单](./ai_agents/templates/optimization_checklist.md) - 系统化的优化工作流程
- [验证规则](./ai_agents/templates/validation_rules.md) - 配置验证的具体标准和规则

### 🏗️ 设计文档

- [架构设计 v2](./design/arch/v2.md) - 系统架构设计文档 v2 版本
- [架构设计 v3](./design/arch/v3.md) - 系统架构设计文档 v3 版本
- [架构设计 v4](./design/arch/v4.md) - 系统架构设计文档 v4 版本（包含原语引擎）

### 👨‍💻 开发指南

- [策略原语开发](./development/strategy_primitives/README.md) - 策略原语系统的开发指南
- [CDDR](./development/CDDR/app.md) - CDDR 开发记录

## 🔑 核心概念

### 📊 投资组合

投资组合（Portfolio）是 investStrategyService 的核心概念，它包含以下主要组件：

- **交易策略（Trade Strategy）**：决定何时买入和卖出，生成交易信号
- **资金策略（Capital Strategy）**：决定每次交易的资金分配方式
- **标的列表（Symbols）**：投资组合包含的股票或其他金融工具
- **回测参数（Backtest Parameters）**：初始资金、开始日期、结束日期等

### 📈 交易策略

系统支持两种类型的交易策略：

1. **传统策略（Traditional Strategy）**：预定义的策略类，如 `DualMovingAverageStrategy`、`TrippleMovingAverageStrategy` 等，通过编写 Python 代码实现。详见[传统策略配置指南](./guides/strategy_configuration.md)。

2. **原语策略（Primitives Strategy）**：使用原语系统构建的声明式策略，通过 JSON 配置定义，无需编写代码。详见[原语系统指南](./guides/primitives_guide/README.md)。

> **传统策略与原语策略的比较**：传统策略适合有编程能力的用户，可以实现更复杂的逻辑和自定义功能；原语策略适合无编程背景的用户，通过可视化配置和声明式定义快速创建策略。原语策略在可维护性和可测试性方面有优势。

### 📋 支持的策略类型

系统目前主要支持基于OHLC价格数据的量化策略，实际支持的策略类型包括：

1. **基础技术指标策略**：
   - **均线策略**：如双均线、三均线交叉策略
   - **震荡指标策略**：基于KDJ、RSI、MACD等技术指标的策略
   - **价格通道策略**：如布林带、唐奇安通道等基于价格区间的策略

2. **轮动策略**（通过框架扩展支持）：
   - **单资产动量轮动**：基于单个资产动量指标的多周期轮动
   - **多资产相对强弱轮动**：在固定资产池中根据相对强弱进行轮动

3. **回归均值策略**：
   - **价格回归策略**：基于价格偏离移动平均的回归交易
   - **技术指标超买超卖策略**：基于指标在极值区域的反转交易

系统**不支持**以下类型的策略：

1. **事件驱动策略**：系统不支持基于财报、新闻、公告等事件的策略
2. **多因子策略**：不支持基于基本面、宏观、情绪等多因子的综合分析策略
3. **高频/分钟级别策略**：系统仅支持日线级别的回测和交易
4. **机器学习策略**：不直接支持基于AI/ML的策略模型
5. **动态仓位管理策略**：不支持根据市场状况动态调整仓位的策略，如股债平衡
6. **跨市场策略**：不支持跨交易所、跨市场的套利策略

这些限制主要源于系统设计上对数据类型、回测频率和策略接口的约束。更详细的架构限制，请参考[架构限制详细说明](./guides/primitives_guide/10_architecture_limitations.md)。

### 💰 资金策略

资金策略决定了如何分配资金到不同的交易标的上：

- **PercentCapitalStrategy**：按百分比分配资金
- **SimplePercentCapitalStrategy**：简化版的百分比资金策略
- **FixedInvestmentStrategy**：定期定额投资策略，支持统一现金流管理

### 💸 现金流管理

系统提供了统一的现金流管理接口，支持多种资金进出场景：

**支持的现金流类型**：
- **定期投资**：年度定投、月度定投
- **分红再投资**：自动将分红收益再次投入
- **风险管理**：止损撤资、紧急撤资
- **动态调整**：再平衡调整、奖金投资
- **自定义现金流**：支持用户自定义的现金流类型

**核心特性**：
- **统一接口**：所有现金流类型使用相同的API接口
- **自动净值调整**：现金流自动调整净值计算，确保收益率准确性
- **混合风险指标计算**：自动处理现金流对风险指标的影响，确保数据一致性
- **风控管理**：内置风险控制和限额管理
- **历史记录**：完整的现金流历史记录和分析

⚠️ **重要**：现金流调整对风险指标计算有重要影响。系统采用混合计算模式：
- **无现金流组合**：使用标准的风险指标计算
- **有现金流组合**：交易指标来自分析器，价值指标基于调整后净值重新计算
- **数据一致性**：确保净值和风险指标都基于相同的数据源

详细使用方法请参考[现金流管理指南](./guides/cash_flow_management.md)。

## 🚀 使用流程

### 1. 配置投资组合

在 `app/config/` 中定义投资组合。系统支持两种类型的策略配置：

**原语策略配置示例**：
```json
{
  "portfolios": [
    {
      "name": "简化RSI测试组合",
      "code": "test_simple_rsi",
      "description": "简化的RSI信号测试组合，用于验证信号处理",
      "strategy_definition": {
        "trade_strategy": {
          "indicators": [
            { "id": "rsi", "type": "RSI", "params": { "period": 14, "column": "Close" } },
            { "id": "buy_threshold", "type": "Constant", "params": { "value": 30 } },
            { "id": "sell_threshold", "type": "Constant", "params": { "value": 70 } }
          ],
          "signals": [
            {
              "id": "rsi_oversold",
              "type": "LessThan",
              "inputs": [{ "ref": "rsi" }, { "ref": "buy_threshold" }]
            },
            {
              "id": "rsi_overbought",
              "type": "GreaterThan",
              "inputs": [{ "ref": "rsi" }, { "ref": "sell_threshold" }]
            }
          ],
          "outputs": {
            "buy_signal": "rsi_oversold",
            "sell_signal": "rsi_overbought"
          }
        },
        "capital_strategy": {
          "name": "PercentCapitalStrategy",
          "params": {
            "initial_capital": 100000,
            "percents": 25
          }
        }
      },
      "symbols": [
        {"symbol": "QQQ", "name": "Nasdaq ETF"}
      ],
      "start_date": "2018-01-01",
      "currency": "USD",
      "market": "US",
      "commission": 0.0001,
      "update_time": "08:00"
    }
  ]
}
```

**传统策略配置示例**（app/config/portfolio_config.json中可找到）：
```json
{
  "portfolios": [
    {
      "name": "双均线策略测试",
      "code": "dual_ma_test",
      "description": "双均线交叉策略测试",
      "strategy": {
        "name": "DualMovingAverageStrategy",
        "params": {
          "short_window": 11,
          "long_window": 22
        }
      },
      "capital_strategy": {
        "name": "SimplePercentCapitalStrategy",
        "params": {
          "percent": 90
        }
      },
      "symbols": [
        {"symbol": "000001", "name": "平安银行"},
        {"symbol": "600036", "name": "招商银行"}
      ],
      "currency": "CNY",
      "market": "CN",
      "start_date": "2019-01-01",
      "commission": 0.0003,
      "update_time": "15:30"
    }
  ]
}
```

### 2. 运行回测

按照根目录README.md中的说明运行回测：

```bash
# 进入app目录
cd app

# 使用local模式回测特定投资组合
python main_v2.py --mode local --code test_simple_rsi
```

更多运行选项请参考项目根目录的README.md文件。

### 3. 分析结果

回测完成后，结果将存储在 `app/data/<portfolio_code>` 目录下，包含以下文件：

- `<portfolio_code>_portfolio.db` - SQLite数据库，包含投资组合交易历史和表现数据
- `<portfolio_code>_signals.db` - SQLite数据库，包含策略生成的交易信号
- `<portfolio_code>_portfolio_thumbnail.png` - 投资组合表现缩略图
- `<portfolio_code>_portfolio_fullsize.png` - 投资组合表现详细图
- `<portfolio_code>_signals_<date>.json` - 最新交易信号JSON
- `<portfolio_code>_<date>.log` - 回测日志文件

### 4. 优化策略

根据回测结果调整策略参数，然后重新运行回测。原语策略可以使用[策略优化指南](./guides/primitives_guide/09_strategy_optimization.md)中的方法。

### 5. 部署应用

系统使用GitHub Actions管道进行自动部署。当代码推送到主分支时，CI/CD流程将自动构建和部署应用到生产环境。详细的部署配置可在项目的GitHub Actions工作流程文件中查看。

## ❓ 常见问题

### 如何添加新的交易策略？

- **传统方式**：在 `app/trade_strategies` 目录下创建新的策略类，继承 `BaseTradeStrategy` 接口
- **原语系统**：使用[原语组合](./guides/primitives_guide/04_composition_patterns.md)创建策略，无需编写代码

### 如何优化策略参数？

参考 [策略配置指南](./guides/strategy_configuration.md) 和 [策略优化指南](./guides/primitives_guide/09_strategy_optimization.md) 中的参数优化建议。

### 如何处理数据源问题？

如果遇到数据加载失败，请检查：

- Redis 连接是否正常
- OHLC 代理服务是否可访问
- 股票代码是否正确
- 日期范围是否有效

更多故障排除信息，参见[故障排除指南](./guides/primitives_guide/06_troubleshooting.md)。

## ⚠️ 限制

### 当前限制

1. **单一策略限制**：目前框架设计仅支持在一个投资组合中使用一个策略，该策略会应用到投资组合中的所有标的上。

2. **单标的信号处理**：现有架构中，策略一次只能处理一个标的的信号生成，这对需要综合考虑多个标的的策略（如轮动策略、配对交易）带来挑战。

3. **外部数据源集成**：原语策略已支持使用外部数据源作为信号生成依据，但基于代码的自定义策略尚不支持此功能。

4. **日线级别限制**：系统目前仅支持日线级别的行情回测，不支持分钟级别或高频交易策略。

5. **满仓交易**：当前架构只支持满仓交易模式，不支持仓位动态调整策略（如股债平衡、风险平价等根据市场情况调整持仓比例的策略）。

6. **多市场组合**：系统目前仅支持单一市场组合，不支持多市场组合。多市场组合涉及币种、市场、交易规则等复杂因素，暂无计划支持。

### 计划改进

1. **多资产协同决策支持**：我们计划扩展框架，添加对全局策略视角的支持，允许策略一次性处理所有标的的信号生成。这将特别有利于实现资产轮动、配对交易等复杂策略。

2. **外部数据源扩展**：计划扩展代码策略以支持外部数据源，允许策略使用基本面数据、宏观经济数据或替代数据源。

## 📝 版本历史

- **v2.1**：更新系统架构至V4，完善原语引擎集成
- **v2.0**：引入原语系统，支持声明式策略配置
- **v1.0**：基础版本，支持传统策略和资金策略
