# AI代理指导文档

## 概述

本目录包含专门为AI代理（如GitHub Copilot、Claude、GPT等）设计的指导文档和模板，用于帮助AI系统理解和操作投资策略原语系统。

## 目录结构

```
ai_agents/
├── README.md                           # 本文件：AI代理文档概述
├── portfolio_creation_prompt.md        # 投资组合创建指导提示词
├── portfolio_optimization_prompt.md    # 投资组合优化指导提示词
├── troubleshooting_prompt.md          # 问题诊断和解决指导
└── templates/                          # 模板和参考资料
    ├── basic_strategy_templates.json   # 基础策略模板集合
    ├── optimization_checklist.md       # 优化检查清单
    ├── common_patterns.md             # 常用原语组合模式
    └── validation_rules.md            # 配置验证规则
```

## 与人类文档的区别

### 人类文档特点 (`doc/guides/`)
- 📖 详细的概念解释和背景知识
- 💡 投资理念和策略思考
- 📊 案例分析和历史回顾
- 🎯 学习导向的渐进式内容

### AI代理文档特点 (`doc/ai_agents/`)
- ⚡ 直接的操作指令和决策树
- 🔧 结构化的模板和配置示例
- ✅ 明确的验证规则和约束条件
- 🚨 具体的错误处理流程

## 使用方式

### 对于AI开发者
将这些提示词集成到AI系统的prompt中，作为系统指令或上下文参考。

### 对于AI代理
按照文档中的结构化指令执行投资组合的创建、优化和问题诊断任务。

### 对于用户
了解AI代理的能力边界和操作流程，更好地与AI协作。

## 更新原则

1. **简洁明确**：避免冗长解释，聚焦可执行指令
2. **结构化**：使用清晰的标题、列表和决策树
3. **模板驱动**：提供可直接使用的配置模板
4. **验证导向**：包含明确的检查点和验证规则
5. **实用优先**：基于实际使用场景和常见问题

## 维护说明

- 当原语系统有重大更新时，需要同步更新AI指导文档
- 定期根据AI代理的实际使用反馈优化指令内容
- 保持与人类文档的一致性，但避免重复详细概念解释 