# 投资组合优化检查清单

## 📋 优化前准备工作

### ✅ 基础信息收集
- [ ] 获取当前策略完整配置
- [ ] 确认回测时间范围和数据质量
- [ ] 识别用户风险偏好（保守/平衡/激进）
- [ ] 明确优化目标优先级
- [ ] 建立性能基准（如指数表现、无风险收益率）

### ✅ 现状分析
- [ ] 计算关键性能指标
  - [ ] 年化收益率
  - [ ] 最大回撤
  - [ ] 夏普比率
  - [ ] 卡玛比率
  - [ ] 胜率和盈亏比
- [ ] 对比基准表现
- [ ] 识别主要问题领域
- [ ] 分析不同市场环境下的表现

## 🔍 问题诊断清单

### ✅ 收益率问题诊断
- [ ] **滞后性检查**
  - [ ] 均线周期是否过长？
  - [ ] 确认机制是否过于保守？
  - [ ] 是否错失主要趋势？
- [ ] **参与度检查**
  - [ ] 入场条件是否过严？
  - [ ] 仓位设置是否过于保守？
  - [ ] 是否频繁空仓？
- [ ] **信号质量检查**
  - [ ] 买入信号是否有效？
  - [ ] 是否存在假信号？
  - [ ] 信号确认机制是否充分？

### ✅ 风险控制问题诊断
- [ ] **回撤分析**
  - [ ] 最大回撤是否超过-20%？
  - [ ] 是否缺乏止损机制？
  - [ ] 仓位是否过于集中？
- [ ] **波动性分析**
  - [ ] 策略波动率是否过高？
  - [ ] 是否缺乏平滑机制？
  - [ ] 下行保护是否充分？

### ✅ 交易效率问题诊断
- [ ] **频率分析**
  - [ ] 交易是否过于频繁（>100次/年）？
  - [ ] 交易是否不足（<5次/年）？
  - [ ] 是否存在无效切换？
- [ ] **持仓分析**
  - [ ] 平均持仓时间是否合理？
  - [ ] 是否存在短暂持仓？
  - [ ] 持仓切换逻辑是否清晰？

## 🛠️ 优化方案设计

### ✅ 参数优化方案
- [ ] **均线参数**
  - [ ] 当前周期：___ 天
  - [ ] 建议范围：保守(+20%) / 激进(-20%)
  - [ ] 测试候选：[___, ___, ___]
- [ ] **确认机制**
  - [ ] 当前确认：___ 天
  - [ ] 建议范围：敏感(1-2) / 平衡(3-5) / 保守(5-7)
  - [ ] 强度阈值：当前___% → 建议___%
- [ ] **仓位管理**
  - [ ] 当前仓位：___%
  - [ ] 建议范围：保守(30-50) / 平衡(50-80) / 激进(80-99)

### ✅ 逻辑优化方案
- [ ] **信号改进**
  - [ ] 是否需要增加技术指标？
  - [ ] 是否需要多重确认？
  - [ ] 是否需要止损机制？
- [ ] **结构优化**
  - [ ] 买入/卖出逻辑是否合理？
  - [ ] 是否需要分层决策？
  - [ ] 是否需要全局上下文？

## 📊 优化版本生成

### ✅ 版本A：响应速度优化
- [ ] **目标**：减少滞后性，提高趋势跟踪
- [ ] **主要改动**：
  - [ ] 均线周期：___ → ___
  - [ ] 确认天数：___ → ___
  - [ ] 其他调整：___
- [ ] **预期效果**：
  - [ ] 年化收益：+1-2%
  - [ ] 交易频率：适度增加
  - [ ] 最大回撤：可能略增

### ✅ 版本B：风险控制优化
- [ ] **目标**：加强风险控制，提高稳定性
- [ ] **主要改动**：
  - [ ] 增加止损：ATR/Chandelier
  - [ ] 延长确认：___ → ___天
  - [ ] 降低仓位：___% → ___%
- [ ] **预期效果**：
  - [ ] 最大回撤：-3 to -5%
  - [ ] 夏普比率：+0.1-0.2
  - [ ] 年化收益：可能略降

### ✅ 版本C：突破性重设计
- [ ] **目标**：质量优先，减少无效交易
- [ ] **主要改动**：
  - [ ] 引入新原语：___
  - [ ] 逻辑重构：___
  - [ ] 确认机制：___
- [ ] **预期效果**：
  - [ ] 胜率：+20-40%
  - [ ] 交易频率：大幅减少
  - [ ] 盈亏比：显著提升

## ✅ 配置验证检查

### ✅ 完整性验证
- [ ] **必需字段检查**
  - [ ] name, code, description 已填写
  - [ ] symbols 数组不为空
  - [ ] start_date 格式正确
  - [ ] strategy_definition 结构完整
- [ ] **引用一致性检查**
  - [ ] 所有signal引用的indicators存在
  - [ ] market_indicators引用正确
  - [ ] transformer名称匹配
  - [ ] outputs映射完整

### ✅ 逻辑验证
- [ ] **信号逻辑**
  - [ ] buy_signal 和 sell_signal 互补
  - [ ] 没有循环引用
  - [ ] 逻辑层次清晰
- [ ] **参数验证**
  - [ ] 数值参数在合理范围内
  - [ ] 百分比参数在0-100之间
  - [ ] 天数参数为正整数

### ✅ 经济逻辑验证
- [ ] **策略合理性**
  - [ ] 投资逻辑有理论基础
  - [ ] 参数设置符合市场常识
  - [ ] 风险收益匹配用户偏好
- [ ] **实用性检查**
  - [ ] 交易频率现实可行
  - [ ] 考虑交易成本影响
  - [ ] 策略可持续执行

## 📈 效果评估标准

### ✅ 主要指标改善目标
- [ ] **收益指标**
  - [ ] 年化收益率提升：目标+____%
  - [ ] 累计收益改善：目标+____%
  - [ ] 超额收益提高：目标+____%
- [ ] **风险指标**
  - [ ] 最大回撤控制：目标<-___%
  - [ ] 波动率管理：目标<___%
  - [ ] 下行偏差降低：目标-___%
- [ ] **效率指标**
  - [ ] 夏普比率：目标>___
  - [ ] 卡玛比率：目标>___
  - [ ] 胜率提升：目标>___%

### ✅ 交易特征目标
- [ ] **频率控制**
  - [ ] 年交易次数：目标___-___次
  - [ ] 平均持仓天数：目标>___天
  - [ ] 无效交易减少：目标-___%
- [ ] **质量提升**
  - [ ] 盈亏比改善：目标>___
  - [ ] 连续亏损控制：目标<___次
  - [ ] 单笔最大亏损：目标<-___%

## 🚀 实施检查清单

### ✅ 配置生成
- [ ] 基于模板生成完整配置
- [ ] 应用优化参数调整
- [ ] 验证JSON格式正确性
- [ ] 确认所有必需字段完整

### ✅ 说明文档
- [ ] 生成策略逻辑解释
- [ ] 列出关键参数变化
- [ ] 提供预期表现估计
- [ ] 说明优化理由和目标

### ✅ 后续建议
- [ ] 提供回测验证建议
- [ ] 给出监控重点指标
- [ ] 建议进一步优化方向
- [ ] 提醒实盘注意事项

## 📝 优化记录模板

```
优化日期：____
优化目标：____
当前表现：
- 年化收益：___%
- 最大回撤：-___%
- 夏普比率：___

主要问题：
1. ____
2. ____

优化方案：
- 方案A：____
- 方案B：____
- 方案C：____

推荐版本：____
推荐理由：____

预期改善：
- 年化收益：+___%
- 最大回撤：-___%
- 其他：____
```

使用这个检查清单，确保每次优化都系统、全面、有据可依。 