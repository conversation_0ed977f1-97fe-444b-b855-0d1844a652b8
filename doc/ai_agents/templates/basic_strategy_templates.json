{"templates": {"stock_bond_rotation": {"name": "股债轮动策略模板", "description": "基于市场指标的股票债券轮动策略", "config": {"name": "股债轮动策略", "code": "stock_bond_rotation_template", "description": "基于市场趋势的股债轮动策略", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}, {"symbol": "511260", "name": "10年期国债ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"market_indicators": {"indicators": [{"code": "000300.SH"}], "transformers": [{"name": "index_raw", "type": "IdentityTransformer", "params": {"indicator": "000300.SH", "field": "Close"}}, {"name": "index_ma", "type": "MovingAverageTransformer", "params": {"indicator": "000300.SH", "window": 50, "method": "simple", "field": "Close"}}]}, "trade_strategy": {"indicators": [], "signals": [{"id": "trend_up", "type": "GreaterThan", "inputs": [{"market": "000300.SH", "transformer": "index_raw"}, {"market": "000300.SH", "transformer": "index_ma"}]}, {"id": "buy_signal", "type": "StockBondSwitch", "params": {"default_to_stock": false}, "inputs": [{"ref": "trend_up"}]}, {"id": "sell_signal", "type": "Not", "inputs": [{"ref": "buy_signal"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal", "indicators": [{"id": "trend_up", "output_name": "trend_signal"}], "market_indicators": [{"market": "000300.SH", "transformer": "index_raw", "output_name": "index_price"}, {"market": "000300.SH", "transformer": "index_ma", "output_name": "index_ma"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 99}}}}, "customization_points": ["market_indicator: 可更换为其他指数", "ma_window: 调整均线周期(20-200)", "default_to_stock: 调整默认仓位偏向", "symbols: 更换交易标的"]}, "dual_moving_average": {"name": "双均线策略模板", "description": "基于双移动平均线交叉的趋势跟踪策略", "config": {"name": "双均线策略", "code": "dual_ma_template", "description": "基于短期和长期均线交叉的趋势跟踪策略", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "ma_short", "type": "SMA", "params": {"window": 20, "field": "Close"}}, {"id": "ma_long", "type": "SMA", "params": {"window": 50, "field": "Close"}}], "signals": [{"id": "golden_cross", "type": "GreaterThan", "inputs": [{"ref": "ma_short"}, {"ref": "ma_long"}]}, {"id": "price_above_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_short"}]}, {"id": "buy_signal", "type": "And", "inputs": [{"ref": "golden_cross"}, {"ref": "price_above_ma"}]}, {"id": "sell_signal", "type": "Not", "inputs": [{"ref": "buy_signal"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal", "indicators": [{"id": "ma_short", "output_name": "ma_short"}, {"id": "ma_long", "output_name": "ma_long"}, {"id": "golden_cross", "output_name": "golden_cross"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 95}}}}, "customization_points": ["ma_short_window: 短期均线周期(5-30)", "ma_long_window: 长期均线周期(30-100)", "confirmation: 可添加Streak确认机制", "position_size: 调整仓位比例"]}, "rsi_mean_reversion": {"name": "RSI均值回归策略模板", "description": "基于RSI指标的超买超卖策略", "config": {"name": "RSI均值回归策略", "code": "rsi_mean_reversion_template", "description": "基于RSI指标识别超买超卖机会的均值回归策略", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "rsi_indicator", "type": "RSI", "params": {"window": 14, "field": "Close"}}, {"id": "oversold_threshold", "type": "Constant", "params": {"value": 30}}, {"id": "overbought_threshold", "type": "Constant", "params": {"value": 70}}], "signals": [{"id": "oversold_condition", "type": "<PERSON><PERSON><PERSON>", "inputs": [{"ref": "rsi_indicator"}, {"ref": "oversold_threshold"}]}, {"id": "overbought_condition", "type": "GreaterThan", "inputs": [{"ref": "rsi_indicator"}, {"ref": "overbought_threshold"}]}, {"id": "buy_signal", "type": "And", "inputs": [{"ref": "oversold_condition"}]}, {"id": "sell_signal", "type": "And", "inputs": [{"ref": "overbought_condition"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal", "indicators": [{"id": "rsi_indicator", "output_name": "rsi"}, {"id": "oversold_condition", "output_name": "oversold"}, {"id": "overbought_condition", "output_name": "overbought"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 50}}}}, "customization_points": ["rsi_window: RSI计算周期(10-20)", "oversold_threshold: 超卖阈值(20-30)", "overbought_threshold: 超买阈值(70-80)", "confirmation: 可添加价格确认机制"]}, "multi_factor": {"name": "多因子策略模板", "description": "结合多个技术指标的综合策略", "config": {"name": "多因子综合策略", "code": "multi_factor_template", "description": "结合移动平均线、RSI和价格动量的多因子策略", "symbols": [{"symbol": "510300", "name": "沪深300ETF"}], "start_date": "2018-01-01", "currency": "CNY", "market": "China", "commission": 0.0003, "update_time": "01:00", "strategy_definition": {"trade_strategy": {"indicators": [{"id": "ma_indicator", "type": "SMA", "params": {"window": 50, "field": "Close"}}, {"id": "rsi_indicator", "type": "RSI", "params": {"window": 14, "field": "Close"}}, {"id": "rsi_bullish_threshold", "type": "Constant", "params": {"value": 50}}], "signals": [{"id": "price_above_ma", "type": "GreaterThan", "inputs": [{"column": "Close"}, {"ref": "ma_indicator"}]}, {"id": "rsi_bullish", "type": "GreaterThan", "inputs": [{"ref": "rsi_indicator"}, {"ref": "rsi_bullish_threshold"}]}, {"id": "buy_conditions", "type": "And", "inputs": [{"ref": "price_above_ma"}, {"ref": "rsi_bullish"}]}, {"id": "buy_signal", "type": "Streak", "params": {"condition": "true", "min_length": 3}, "inputs": [{"ref": "buy_conditions"}]}, {"id": "sell_signal", "type": "Not", "inputs": [{"ref": "buy_signal"}]}], "outputs": {"buy_signal": "buy_signal", "sell_signal": "sell_signal", "indicators": [{"id": "ma_indicator", "output_name": "ma"}, {"id": "rsi_indicator", "output_name": "rsi"}, {"id": "buy_conditions", "output_name": "conditions_met"}]}}, "capital_strategy": {"name": "PercentCapitalStrategy", "params": {"percents": 80}}}}, "customization_points": ["ma_window: 均线周期调整", "rsi_window: RSI周期调整", "rsi_threshold: RSI阈值调整", "confirmation_days: Streak确认天数", "additional_factors: 可添加其他技术指标"]}}, "usage_guide": {"template_selection": {"stock_bond_rotation": "适用于资产配置和轮动策略", "dual_moving_average": "适用于单一资产的趋势跟踪", "rsi_mean_reversion": "适用于震荡市场的均值回归", "multi_factor": "适用于需要多重确认的复合策略"}, "customization_workflow": ["1. 选择最接近用户需求的基础模板", "2. 根据customization_points调整关键参数", "3. 修改symbols以匹配目标资产", "4. 调整start_date和其他基础配置", "5. 验证配置完整性和逻辑一致性"], "parameter_ranges": {"ma_windows": {"short": "5-30天", "medium": "30-100天", "long": "100-250天"}, "rsi_parameters": {"window": "10-20天", "oversold": "15-30", "overbought": "70-85"}, "position_sizes": {"conservative": "30-50%", "balanced": "50-80%", "aggressive": "80-99%"}, "confirmation_periods": {"sensitive": "1-2天", "balanced": "3-5天", "conservative": "5-7天"}}}}