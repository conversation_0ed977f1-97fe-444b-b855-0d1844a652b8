# 投资组合配置验证规则

## 📋 必需字段验证

### ✅ 顶层配置
```json
{
  "name": "string, 必需, 长度1-100字符",
  "code": "string, 必需, 符合命名规范",
  "description": "string, 可选, 长度0-500字符",
  "symbols": "array, 必需, 至少1个元素",
  "start_date": "string, 必需, YYYY-MM-DD格式",
  "currency": "string, 必需, CNY/USD/EUR等",
  "market": "string, 必需, China/US/Europe等",
  "commission": "number, 可选, 0-0.01范围",
  "update_time": "string, 可选, HH:MM格式"
}
```

### ✅ 策略定义
```json
{
  "strategy_definition": {
    "market_indicators": "object, 使用市场指标时必需",
    "trade_strategy": "object, 必需",
    "capital_strategy": "object, 必需"
  }
}
```

## 🔍 字段格式验证

### ✅ 基础字段规则
```python
validation_rules = {
    "name": {
        "type": "string",
        "required": True,
        "min_length": 1,
        "max_length": 100,
        "pattern": r"^[^<>\"'&]*$"  # 不包含特殊字符
    },
    "code": {
        "type": "string", 
        "required": True,
        "pattern": r"^[a-zA-Z][a-zA-Z0-9_]*$",  # 字母开头，字母数字下划线
        "max_length": 50
    },
    "start_date": {
        "type": "string",
        "required": True,
        "pattern": r"^\d{4}-\d{2}-\d{2}$",  # YYYY-MM-DD
        "valid_range": "2010-01-01 to 今天"
    },
    "commission": {
        "type": "number",
        "required": False,
        "min": 0,
        "max": 0.01,  # 最大1%
        "default": 0.0003
    }
}
```

### ✅ Symbols数组验证
```python
symbols_validation = {
    "type": "array",
    "required": True,
    "min_items": 1,
    "max_items": 10,  # 避免过度复杂
    "items": {
        "type": "object",
        "required_fields": ["symbol", "name"],
        "symbol": {
            "type": "string",
            "pattern": r"^[A-Z0-9]{4,10}$",  # 4-10位大写字母数字
            "examples": ["510300", "SPY", "QQQ"]
        },
        "name": {
            "type": "string",
            "min_length": 1,
            "max_length": 50
        }
    }
}
```

## 🧮 数值参数验证

### ✅ 技术指标参数范围
```python
indicator_params = {
    "MovingAverage": {
        "window": {
            "type": "integer",
            "min": 5,
            "max": 250,
            "recommended": [10, 20, 50, 100, 200]
        },
        "method": {
            "type": "string",
            "allowed": ["simple", "exponential", "weighted"]
        }
    },
    "RSI": {
        "window": {
            "type": "integer", 
            "min": 5,
            "max": 30,
            "recommended": [14, 21]
        }
    },
    "ATR": {
        "window": {
            "type": "integer",
            "min": 5,
            "max": 50,
            "recommended": [14, 20]
        }
    },
    "ChandelierExit": {
        "window": {
            "type": "integer",
            "min": 10,
            "max": 50,
            "recommended": [22, 44]
        },
        "multiplier": {
            "type": "number",
            "min": 1.0,
            "max": 5.0,
            "recommended": [2.0, 3.0]
        }
    }
}
```

### ✅ 信号原语参数范围
```python
signal_params = {
    "Constant": {
        "value": {
            "type": "number",
            "min": -1000,
            "max": 1000
        }
    },
    "Streak": {
        "min_length": {
            "type": "integer",
            "min": 1,
            "max": 10,
            "recommended": [2, 3, 5]
        },
        "condition": {
            "type": "string",
            "allowed": ["true", "false"]
        }
    },
    "StockBondSwitch": {
        "default_to_stock": {
            "type": "boolean",
            "required": True
        }
    }
}
```

### ✅ 资金策略参数
```python
capital_strategy_params = {
    "PercentCapitalStrategy": {
        "percents": {
            "type": "number",
            "min": 1,
            "max": 99,
            "recommended": [30, 50, 80, 95]
        },
        "initial_capital": {
            "type": "number",
            "min": 1000,
            "max": 10000000,
            "default": 100000
        }
    }
}
```

## 🔗 引用一致性验证

### ✅ 市场指标引用检查
```python
def validate_market_indicators(config):
    indicators = config.get("market_indicators", {}).get("indicators", [])
    transformers = config.get("market_indicators", {}).get("transformers", [])
    
    # 检查1：每个transformer必须引用存在的indicator
    indicator_codes = {ind.get("code") for ind in indicators}
    for transformer in transformers:
        indicator_ref = transformer.get("params", {}).get("indicator")
        if indicator_ref and indicator_ref not in indicator_codes:
            raise ValidationError(f"Transformer引用了不存在的indicator: {indicator_ref}")
    
    # 检查2：transformer名称唯一性
    transformer_names = [t.get("name") for t in transformers]
    if len(transformer_names) != len(set(transformer_names)):
        raise ValidationError("Transformer名称必须唯一")
```

### ✅ 信号引用检查
```python
def validate_signal_references(trade_strategy):
    indicators = {ind.get("id") for ind in trade_strategy.get("indicators", [])}
    signals = {sig.get("id") for sig in trade_strategy.get("signals", [])}
    
    for signal in trade_strategy.get("signals", []):
        for input_ref in signal.get("inputs", []):
            if "ref" in input_ref:
                ref_id = input_ref["ref"]
                if ref_id not in indicators and ref_id not in signals:
                    raise ValidationError(f"Signal引用了不存在的id: {ref_id}")
            elif "market" in input_ref:
                # 验证market引用的合法性
                market_code = input_ref["market"]
                transformer_name = input_ref["transformer"]
                # 需要检查对应的market_indicators配置
```

### ✅ 输出映射检查
```python
def validate_outputs(trade_strategy):
    outputs = trade_strategy.get("outputs", {})
    available_signals = {sig.get("id") for sig in trade_strategy.get("signals", [])}
    available_indicators = {ind.get("id") for ind in trade_strategy.get("indicators", [])}
    
    # 检查买入卖出信号
    if "buy_signal" in outputs:
        if outputs["buy_signal"] not in available_signals:
            raise ValidationError(f"buy_signal引用了不存在的信号: {outputs['buy_signal']}")
    
    if "sell_signal" in outputs:
        if outputs["sell_signal"] not in available_signals:
            raise ValidationError(f"sell_signal引用了不存在的信号: {outputs['sell_signal']}")
```

## 🧠 逻辑一致性验证

### ✅ 买卖信号互补性
```python
def validate_signal_complementarity(config):
    """验证买入和卖出信号的逻辑互补性"""
    trade_strategy = config.get("strategy_definition", {}).get("trade_strategy", {})
    signals = {sig.get("id"): sig for sig in trade_strategy.get("signals", [])}
    outputs = trade_strategy.get("outputs", {})
    
    buy_signal_id = outputs.get("buy_signal")
    sell_signal_id = outputs.get("sell_signal")
    
    # 检查卖出信号是否为买入信号的否定
    if buy_signal_id and sell_signal_id:
        sell_signal = signals.get(sell_signal_id)
        if sell_signal.get("type") == "Not":
            not_inputs = sell_signal.get("inputs", [])
            if len(not_inputs) == 1 and not_inputs[0].get("ref") == buy_signal_id:
                return True  # 正确的互补关系
    
    # 警告：买卖信号可能不互补
    return False
```

### ✅ 循环引用检查
```python
def check_circular_references(trade_strategy):
    """检查信号和指标之间是否存在循环引用"""
    dependencies = {}
    
    # 构建依赖图
    for signal in trade_strategy.get("signals", []):
        signal_id = signal.get("id")
        dependencies[signal_id] = []
        
        for input_ref in signal.get("inputs", []):
            if "ref" in input_ref:
                dependencies[signal_id].append(input_ref["ref"])
    
    # 检查循环
    def has_cycle(node, visited, rec_stack):
        visited[node] = True
        rec_stack[node] = True
        
        for neighbor in dependencies.get(node, []):
            if neighbor in dependencies:
                if not visited.get(neighbor, False):
                    if has_cycle(neighbor, visited, rec_stack):
                        return True
                elif rec_stack.get(neighbor, False):
                    return True
        
        rec_stack[node] = False
        return False
    
    visited = {}
    rec_stack = {}
    
    for node in dependencies:
        if not visited.get(node, False):
            if has_cycle(node, visited, rec_stack):
                raise ValidationError(f"检测到循环引用，涉及节点: {node}")
```

## 💰 经济逻辑验证

### ✅ 策略合理性检查
```python
def validate_economic_logic(config):
    """验证策略的经济学合理性"""
    warnings = []
    
    # 检查1：交易频率合理性
    trade_frequency = estimate_trade_frequency(config)
    if trade_frequency > 200:  # 年交易次数
        warnings.append("交易频率过高，可能导致过度交易")
    elif trade_frequency < 2:
        warnings.append("交易频率过低，可能错失机会")
    
    # 检查2：参数设置合理性
    ma_windows = extract_ma_windows(config)
    if ma_windows:
        max_window = max(ma_windows)
        if max_window > 200:
            warnings.append("移动平均窗口过长，可能导致过度滞后")
        elif max_window < 10:
            warnings.append("移动平均窗口过短，可能产生过多噪音")
    
    # 检查3：仓位设置合理性
    position_size = extract_position_size(config)
    if position_size > 95:
        warnings.append("仓位过高，缺乏风险缓冲")
    elif position_size < 20:
        warnings.append("仓位过低，可能影响收益潜力")
    
    return warnings
```

### ✅ 风险评估
```python
def assess_risk_level(config):
    """评估策略的风险等级"""
    risk_score = 0
    
    # 因子1：仓位大小
    position_size = extract_position_size(config)
    if position_size > 90:
        risk_score += 3
    elif position_size > 70:
        risk_score += 2
    else:
        risk_score += 1
    
    # 因子2：资产集中度
    asset_count = len(config.get("symbols", []))
    if asset_count == 1:
        risk_score += 2
    elif asset_count <= 3:
        risk_score += 1
    
    # 因子3：策略复杂度
    signal_count = len(config.get("strategy_definition", {}).get("trade_strategy", {}).get("signals", []))
    if signal_count > 10:
        risk_score += 1
    
    # 因子4：确认机制
    has_confirmation = check_confirmation_mechanism(config)
    if not has_confirmation:
        risk_score += 1
    
    # 风险等级评定
    if risk_score <= 3:
        return "低风险"
    elif risk_score <= 6:
        return "中等风险"
    else:
        return "高风险"
```

## 🚀 性能预期验证

### ✅ 预期表现估算
```python
def estimate_performance_characteristics(config):
    """基于配置特征估算性能表现"""
    
    # 基础特征分析
    strategy_type = identify_strategy_type(config)
    ma_windows = extract_ma_windows(config)
    confirmation_days = extract_confirmation_days(config)
    position_size = extract_position_size(config)
    
    # 收益率估算
    if strategy_type == "trend_following":
        if ma_windows and min(ma_windows) < 50:
            expected_return = "5-12%"
        else:
            expected_return = "3-8%"
    elif strategy_type == "mean_reversion":
        expected_return = "4-10%"
    elif strategy_type == "asset_rotation":
        expected_return = "3-8%"
    else:
        expected_return = "2-6%"
    
    # 回撤估算
    if confirmation_days >= 5:
        expected_drawdown = "8-15%"
    elif confirmation_days >= 3:
        expected_drawdown = "10-18%"
    else:
        expected_drawdown = "12-25%"
    
    # 交易频率估算
    if strategy_type == "mean_reversion":
        trade_frequency = "高频(50-100次/年)"
    elif confirmation_days <= 2:
        trade_frequency = "中高频(30-60次/年)"
    else:
        trade_frequency = "低频(10-30次/年)"
    
    return {
        "expected_annual_return": expected_return,
        "expected_max_drawdown": expected_drawdown,
        "expected_trade_frequency": trade_frequency,
        "risk_level": assess_risk_level(config)
    }
```

## 📊 验证报告生成

### ✅ 验证结果结构
```json
{
  "validation_result": {
    "status": "success|warning|error",
    "errors": [
      {
        "type": "配置错误|逻辑错误|参数错误",
        "field": "字段路径",
        "message": "错误描述",
        "suggestion": "修复建议"
      }
    ],
    "warnings": [
      {
        "type": "性能警告|逻辑警告|参数警告", 
        "field": "字段路径",
        "message": "警告描述",
        "recommendation": "优化建议"
      }
    ],
    "summary": {
      "total_checks": 25,
      "passed_checks": 23,
      "failed_checks": 0,
      "warning_checks": 2
    }
  },
  "performance_estimate": {
    "expected_annual_return": "3-8%",
    "expected_max_drawdown": "10-18%",
    "expected_trade_frequency": "低频(10-30次/年)",
    "risk_level": "中等风险"
  },
  "optimization_suggestions": [
    "建议缩短移动平均窗口以提高响应速度",
    "考虑增加止损机制以控制下行风险"
  ]
}
```

## 🛠️ 自动修复建议

### ✅ 常见错误自动修复
```python
auto_fixes = {
    "missing_sell_signal": {
        "description": "缺少卖出信号",
        "fix": "自动生成buy_signal的Not信号作为sell_signal"
    },
    "invalid_parameter_range": {
        "description": "参数超出合理范围",
        "fix": "自动调整到推荐范围内的最近值"
    },
    "missing_confirmation": {
        "description": "缺少信号确认机制",
        "fix": "自动添加3日Streak确认"
    },
    "excessive_position_size": {
        "description": "仓位过高",
        "fix": "自动调整到95%以降低风险"
    }
}
```

使用这些验证规则，确保生成的策略配置符合系统要求，逻辑正确，参数合理。 