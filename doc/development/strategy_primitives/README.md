# 交易策略原语化设计方案

## 1. 背景与目标

### 1.1 背景

当前 `investStrategyService` 主要依赖于直接编写 Python 类来定义交易策略。虽然这种方式直接，但在以下方面存在局限：

* **灵活性不足:** 创建新策略或调整现有策略通常需要修改 Python 代码，对 AI Agent 生成策略不友好。
* **复用性有限:** 不同策略中可能包含大量重复的指标计算和信号判断逻辑。
* **扩展性受限:** 难以方便地集成外部市场信息（如宏观指标、情绪指数）进行决策或风险管理。
* **策略表达不直观:** 复杂的策略逻辑可能散落在代码各处，不易理解和维护。

### 1.2 目标

为了克服以上局限，本次设计旨在构建一个更灵活、可扩展、易于使用的策略框架，核心目标包括：

* **策略原语化:** 将通用的指标计算和信号判断逻辑抽象为可复用的“原语”。
* **配置驱动:** 支持通过 JSON 配置文件组合原语来创建复杂策略，降低策略开发门槛。
* **市场指标集成:** 方便策略引用外部市场数据（如 VIX, 市场 PE 等）进行决策。
* **动态风险管理:** 实现基于市场状态的动态仓位调整功能，包括调整现有持仓。
* **向后兼容:** 确保现有基于 Python 类实现的策略仍能正常运行。
* **资金策略兼容:** 保持现有资金策略类的使用方式，无需原语化，支持配置选择。

## 2. 核心设计理念

* **解耦 (Decoupling):** 将策略逻辑分解为独立的、功能单一的原语组件（指标、信号、转换器），降低各部分之间的耦合度。
* **组合 (Composition):** 通过声明式的 JSON 配置，将这些原语像积木一样组合起来，构建出完整的策略逻辑。配置即策略。
* **扩展性 (Extensibility):** 通过注册表机制和标准化的原语接口，方便地添加新的指标、信号、市场数据源或风险规则。

## 3. 系统架构

### 3.1 总体架构图

```mermaid
graph TD
    subgraph "配置与加载层"
        Config["JSON 配置 (portfolio_config.json)"]
        Factory["PortfolioFactory (portfolio_factory_v2.py)"]
        Registry["ComponentRegistry (registry.py)"]
    end

    subgraph "数据层"
        DataLoader["PortfolioDataLoader (portfolio_data_collector.py)"]
        MarketDataSources["外部市场数据源 (VIX, PE等)"]
        MarketIndicatorMgr["MarketIndicatorManager (market_indicators.py)"]
    end

    subgraph "核心组件层"
        Primitives["策略原语 (components/)"]
        Indicators["指标原语 (indicators/)"]
        Signals["信号原语 (signals/)"]
        Transformers["市场指标转换器 (market_transformers)"]
        SignalEvaluator["SignalEvaluator (signal_evaluator.py)"]
        RiskController["RiskController (risk_controller.py)"]
    end

    subgraph "策略与执行层"
        Strategy["交易策略实例 (基类/接口)"]
        CompositeStrategy["组合策略实例 (CompositeStrategy)"]
        LegacyStrategy["原有策略类 (e.g., DualMA)"]
        CapitalStrategy["资金策略实例 (capital_strategies/)"]
        BTAdapter["BacktraderStrategyAdapter (backtrader_adapters.py)"]
        BTEngine["Backtrader 引擎"]
    end

    Config -- 读取 --> Factory
    Factory -- 使用 --> Registry
    Factory -- 创建 --> MarketIndicatorMgr
    Factory -- 创建 --> RiskController
    Factory -- 创建 --> Strategy
    Factory -- 创建 --> CapitalStrategy
    Registry -- 注册/提供 --> Primitives
    Registry -- 注册/提供 --> Transformers
    Registry -- 注册/提供 --> LegacyStrategy
    Registry -- 注册/提供 --> CapitalStrategy

    MarketIndicatorMgr -- 使用 --> DataLoader
    DataLoader -- 获取 --> MarketDataSources
    MarketIndicatorMgr -- 管理 --> Transformers

    CompositeStrategy -- 使用 --> SignalEvaluator
    SignalEvaluator -- 使用 --> Indicators
    SignalEvaluator -- 使用 --> Signals
    SignalEvaluator -- 查询 --> MarketIndicatorMgr

    RiskController -- 查询 --> MarketIndicatorMgr
    RiskController -- 可能使用信号原语 --> Registry

    Factory -- 配置 --> BTAdapter
    BTAdapter -- 包含 --> Strategy
    BTAdapter -- 包含 --> CapitalStrategy
    BTAdapter -- 使用 --> RiskController
    BTEngine -- 驱动 --> BTAdapter
    BTAdapter -- 下单/调整仓位 --> BTEngine

    Primitives --> Indicators & Signals
    Strategy --> CompositeStrategy & LegacyStrategy
```

### 3.2 数据流

1.  **配置加载与初始化**:
    * `PortfolioFactory` 读取 JSON 配置。
    * `PortfolioFactory` 使用 `ComponentRegistry` 获取所需组件类。
    * `PortfolioFactory` 创建 `MarketIndicatorManager` 并加载所需市场数据及转换器。
    * `PortfolioFactory` 创建 `RiskController`。
    * `PortfolioFactory` 根据配置创建交易策略实例（旧式类或组合策略实例）和资金策略实例。
    * 对于组合策略，`PortfolioFactory` 创建 `SignalEvaluator` 并注入所需组件。
    * 所有组件被组装进 `BacktraderStrategyAdapter`。
2.  **信号生成 (组合策略)**:
    * `BacktraderStrategyAdapter` 调用 `CompositeStrategy.generate_signals`。
    * `CompositeStrategy` 调用其包含的指标原语实例计算指标值。
    * `CompositeStrategy` 调用 `SignalEvaluator.evaluate` 来评估 `outputs` 中定义的买卖信号 ID。
    * `SignalEvaluator` 解析信号配置，调用相应的信号原语 `evaluate` 方法，可能需要查询 `MarketIndicatorManager` 获取市场数据。
    * `SignalEvaluator` 返回最终信号给 `CompositeStrategy`。
    * `CompositeStrategy` 返回信号给 `BacktraderStrategyAdapter`。
3.  **回测执行 (`next` 步)**:
    * `BacktraderStrategyAdapter.next()` 被调用。
    * 调用 `RiskController.evaluate` 获取当前仓位比例 (`position_scale`)。
    * 调用 `_adjust_existing_positions` 根据 `position_scale` 检查并执行必要的减仓。
    * 获取当前 K 线的交易信号（来自 `CompositeStrategy.generate_signals` 或旧策略逻辑）。
    * 根据卖出信号执行卖出操作。
    * 根据买入信号，调用资金策略 `allocate_capital` 获取理论分配资金。
    * 将理论资金乘以 `position_scale` 得到实际可买入资金。
    * 计算买入数量并执行买入操作。

## 4. 核心组件设计

### 4.1 组件注册表 (ComponentRegistry)

* **职责:** 集中管理所有可用的组件类（策略、原语、转换器），提供按名称查找的功能。采用单例模式。
* **实现概要:**
    ```python
    # app/components/registry.py
    class ComponentRegistry:
        _instance = None
        # ... (单例模式实现) ...

        def __init__(self):
            # ... (防止重复初始化) ...
            self.trade_strategies = {}
            self.capital_strategies = {}
            self.indicators = {}
            self.signals = {}
            self.market_transformers = {}
            self._initialize_registry()

        def _scan_and_register(self, package, target_dict, base_class=None):
            """动态扫描指定 Python 包，注册符合条件的类到 target_dict"""
            # 实现细节：使用 pkgutil 和 inspect 动态导入并注册类
            # 考虑错误处理和日志记录
            pass # 简化实现细节

        def _initialize_registry(self):
            """初始化注册表，扫描所有组件包"""
            print("Initializing Component Registry...")
            # 定义基类 (或使用 object)
            # ...
            # 调用 _scan_and_register 扫描各个组件目录
            # self._scan_and_register(app.trade_strategies, self.trade_strategies, BaseTradeStrategy)
            # self._scan_and_register(app.capital_strategies, self.capital_strategies, BaseCapitalStrategy)
            # self._scan_and_register(app.components.indicators, self.indicators, BaseIndicatorPrimitive)
            # self._scan_and_register(app.components.signals, self.signals, BaseSignalPrimitive)
            # self._scan_and_register(app.components.market_transformers, self.market_transformers, BaseMarketTransformer)
            # 手动补充或覆盖注册（可选）
            # ...
            print("Component registry initialized.")
            # 打印已注册组件列表 (用于调试)
            # ...

        def get_trade_strategy(self, name):
            """获取交易策略类"""
            return self.trade_strategies.get(name)

        def get_capital_strategy(self, name):
            """获取资金策略类"""
            return self.capital_strategies.get(name)

        def get_indicator(self, name):
            """获取指标原语类"""
            return self.indicators.get(name)

        def get_signal(self, name):
            """获取信号原语类"""
            return self.signals.get(name)

        def get_market_transformer(self, name):
            """获取市场指标转换器类"""
            return self.market_transformers.get(name)

        @classmethod
        def get_instance(cls):
             """获取注册表单例"""
             # ... (确保初始化) ...
             return cls._instance
    ```

### 4.2 市场指标管理器 (MarketIndicatorManager)

* **职责:** 根据配置加载、缓存、转换（可选）外部市场指标数据，并提供按日期查询数据的功能。
* **实现概要:**
    ```python
    # app/components/market_indicators.py
    import pandas as pd
    from app.components.registry import ComponentRegistry

    class MarketIndicatorManager:
        INDICATORS_MAP = { # 定义指标来源信息
            "VIX": {"symbol": "^VIX", "market": "US", "source": "yahoo"},
            # ... 其他映射 ...
        }

        def __init__(self, data_loader):
            self.data_loader = data_loader # PortfolioDataLoader 实例
            self.indicators_data = {} # 缓存原始数据 {id: DataFrame}
            self.transformers_instances = {} # 缓存转换器实例 {id: instance}
            self.transformed_data = {} # 缓存转换后数据 {id: Series/DataFrame}
            self.registry = ComponentRegistry.get_instance()

        def load_indicator(self, indicator_id, start_date, end_date):
            """加载原始市场指标数据 (含缓存和日期范围检查)"""
            # 实现细节：
            # 1. 检查缓存及日期范围。
            # 2. 从 INDICATORS_MAP 获取信息 (symbol, source)。
            # 3. 调用 self.data_loader 中对应 source 的方法获取数据 (DataLoader需扩展)。
            # 4. 处理自定义计算指标 (_calculate_custom_indicator)。
            # 5. 缓存加载的数据 (DataFrame, DatetimeIndex)。
            # 6. 处理加载失败情况。
            pass # 简化实现细节

        def _calculate_custom_indicator(self, symbol, start_date, end_date):
            """计算自定义指标 (如 PE)"""
            pass # 简化实现细节

        def register_transformer(self, transformed_id, source_indicator_id, transformer_type, params=None):
            """注册并应用指标转换器"""
            # 实现细节：
            # 1. 检查是否已注册。
            # 2. 检查源指标数据是否存在。
            # 3. 从 Registry 获取转换器类 (优先 market_transformer，然后 indicator)。
            # 4. 实例化转换器。
            # 5. 调用转换器的 transform 或 calculate 方法。
            # 6. 缓存转换器实例和转换结果。
            # 7. 处理错误。
            pass # 简化实现细节

        def get_indicator_value(self, indicator_id, date, field='Close'):
            """获取指定日期指标值 (优先转换后, 使用 asof 处理日期)"""
            # 实现细节：
            # 1. 检查转换后数据 (self.transformed_data)。
            # 2. 如果没有，检查原始数据 (self.indicators_data)。
            # 3. 使用 .asof(date) 获取该日期或之前最近的值。
            # 4. 处理 DataFrame 和 Series 的情况。
            # 5. 处理字段不存在或日期范围外的情况。
            pass # 简化实现细节

        def get_indicator_series(self, indicator_id, field='Close'):
             """获取指标的完整时间序列 (优先转换后)"""
             # 实现细节：
             # 1. 检查转换后数据。
             # 2. 如果没有，检查原始数据。
             # 3. 返回对应的 Series 或 DataFrame 列。
             # 4. 处理数据不存在的情况。
             pass # 简化实现细节
    ```

### 4.3 信号评估器 (SignalEvaluator)

* **职责:** 解析信号配置，获取输入数据（技术指标、市场指标、常量），调用信号原语实例进行评估。
* **实现概要:**
    ```python
    # app/components/signal_evaluator.py
    import pandas as pd
    import numpy as np

    class SignalEvaluator:
        def __init__(self, signals_config, indicator_instances, market_indicator_manager=None):
            self.signals_config = signals_config # {id: {"class": instance, "inputs": [...]}}
            self.indicator_instances = indicator_instances # {id: instance}
            self.market_indicator_manager = market_indicator_manager
            self.indicator_outputs_cache = {} # {symbol: {indicator_id: Series}}

        def _get_indicator_output(self, indicator_id, data, symbol):
            """获取或计算并缓存指标输出 Series"""
            # 实现细节：检查缓存，调用 indicator_instance.calculate(data)
            pass # 简化实现细节

        def _resolve_input(self, inp, data, date, symbol):
            """解析单个输入配置，返回适用于评估的值或 Series"""
            # 实现细节：
            # 1. 处理 {"ref": ...}：从 data 或调用 _get_indicator_output 获取 Series。
            # 2. 处理 {"market": ...}：调用 market_indicator_manager.get_indicator_value 获取标量值。
            # 3. 处理常量。
            # 4. 处理引用或数据不存在的错误。
            pass # 简化实现细节

        def evaluate(self, data, signal_id, date, symbol):
            """评估指定信号在特定日期的条件"""
            # 实现细节：
            # 1. 检查日期是否在数据索引中。
            # 2. 查找 signal_config。
            # 3. 循环解析 inputs (调用 _resolve_input)。
            # 4. 调用信号原语实例 signal_config["class"].evaluate(*resolved_inputs, date=date)。
            # 5. 处理 NaN 或 None 结果，返回布尔值。
            # 6. 处理解析或评估中的异常。
            pass # 简化实现细节

        def clear_cache(self, symbol=None):
             """清除指标计算缓存"""
             pass # 简化实现细节
    ```

### 4.4 风险控制器 (RiskController)

* **职责:** 根据配置的规则（基于市场指标或信号原语）评估当前风险，输出仓位调整系数。
* **实现概要:**
    ```python
    # app/components/risk_controller.py
    import pandas as pd
    from app.components.registry import ComponentRegistry

    class RiskController:
        def __init__(self, config, market_indicator_manager):
            self.config = config if config else {}
            self.market_indicator_manager = market_indicator_manager
            self.rules = self.config.get("rules", [])
            self.default_scale = self.config.get("default_scale", 1.0)
            self.registry = ComponentRegistry.get_instance()

        def evaluate(self, date):
            """评估风险，返回 {"position_scale": float, "triggered_rules": list}"""
            # 实现细节：
            # 1. 检查是否启用 (enabled)。
            # 2. 检查 market_indicator_manager 是否存在。
            # 3. 初始化 scale = self.default_scale。
            # 4. 遍历 self.rules，调用 _evaluate_rule。
            # 5. 如果规则触发，更新 scale = min(scale, rule_scale)。
            # 6. 记录触发的规则信息。
            # 7. 确保最终 scale 在 [0.0, 1.0] 范围内。
            # 8. 处理评估错误。
            pass # 简化实现细节

        def _evaluate_rule(self, rule, date):
            """评估单条规则，返回布尔值"""
            # 实现细节：
            # 1. 判断规则类型（简单指标条件 vs 复杂信号条件）。
            # 2. 对于简单条件：
            #    - 获取 indicator, condition, threshold。
            #    - 调用 market_indicator_manager.get_indicator_value 获取值。
            #    - 处理数据不可用情况。
            #    - 执行比较 (>, <, ==, >=, <=)。
            # 3. 对于复杂条件：
            #    - 获取 condition.type 和 condition.inputs。
            #    - 从 Registry 获取信号类。
            #    - 解析 inputs (通常只支持 market 和常量)。
            #    - 处理输入数据不可用情况。
            #    - 实例化信号原语并调用 evaluate。
            # 4. 处理无效规则格式或评估错误。
            pass # 简化实现细节
    ```

### 4.5 策略工厂 (PortfolioFactory)

* **职责:** 解析投资组合配置，根据配置类型（旧式 vs `strategy_definition`）创建并组装所有必要的组件（策略、管理器、控制器），最终返回可执行的投资组合对象（如 `PortfolioManager` 或配置好的 `BacktraderStrategyAdapter`）。
* **实现概要:**
    ```python
    # app/portfolio_factory_v2.py
    from app.components.registry import ComponentRegistry
    from app.components.market_indicators import MarketIndicatorManager
    # ... 其他导入 ...

    class PortfolioFactory:
        def __init__(self, config_dir="configs"):
            self.config_dir = config_dir
            self.component_registry = ComponentRegistry.get_instance()

        def _load_config(self, portfolio_code):
            """从文件加载 JSON 配置并预处理"""
            # 实现细节：加载 JSON，处理 _original_strategy_definition
            pass # 简化实现细节

        def get_portfolio(self, portfolio_code, data_loader):
            """获取配置好的 PortfolioManager 实例"""
            config = self._load_config(portfolio_code)
            market_indicator_manager = None
            risk_controller = None
            trade_strategy = None
            capital_strategy = None

            # --- 1. 创建 MarketIndicatorManager ---
            if config.get('market_indicators'):
                # ... (实例化并加载指标/转换器) ...
                pass # 简化实现细节

            # --- 2. 创建 RiskController ---
            if config.get('risk_controller', {}).get('enabled', False):
                # ... (实例化 RiskController) ...
                pass # 简化实现细节

            # --- 3. 创建交易策略和资金策略 ---
            if "_original_strategy_definition" in config:
                # --- 创建动态组合策略 ---
                strategy_def = config["_original_strategy_definition"]
                trade_strategy = self._create_composite_strategy(
                    strategy_def.get("trade_strategy", {}),
                    market_indicator_manager
                )
                capital_strategy = self._create_capital_strategy(
                    strategy_def.get("capital_strategy", {})
                )
            else:
                # --- 加载旧式策略 ---
                # ... (使用 registry 获取类并实例化) ...
                pass # 简化实现细节

            # --- 4. 组装 PortfolioManager ---
            if not trade_strategy or not capital_strategy:
                 raise ValueError("Failed to create strategies")
            portfolio_manager = PortfolioManager(
                 config=config,
                 data_loader=data_loader,
                 trade_strategy=trade_strategy,
                 capital_strategy=capital_strategy,
                 risk_controller=risk_controller
            )
            return portfolio_manager

        def _create_capital_strategy(self, config):
            """从配置创建资金策略实例"""
            # 实现细节：使用 registry 获取类并实例化
            pass # 简化实现细节

        def _create_risk_controller(self, config, market_indicator_manager):
            """创建风险控制器实例"""
            return RiskController(config, market_indicator_manager)

        def _create_composite_strategy(self, config, market_indicator_manager):
            """创建组合策略实例"""
            # 实现细节：
            # 1. 实例化指标原语。
            # 2. 构建信号配置字典 (含实例化的信号原语)。
            # 3. 创建 SignalEvaluator。
            # 4. 获取 outputs 配置。
            # 5. 实例化通用的 CompositeStrategy 类并注入依赖。
            pass # 简化实现细节
    ```

### 4.6 BacktraderStrategyAdapter 扩展

* **职责:** 作为 Backtrader 策略运行，集成交易策略、资金策略和风险控制器，执行交易逻辑和风险调整。
* **实现概要:**
    ```python
    # app/portfolios/backtrader_adapters.py
    import backtrader as bt
    import pandas as pd

    class TradeSignalState: # 示例
        EMPTY = 0; BUY = 1; SELL = -1; HOLD = 2

    class BacktraderStrategyAdapter(bt.Strategy):
        params = ( # 定义参数以接收组件
            ('trade_strategy', None),
            ('capital_strategy', None),
            ('portfolio_manager', None),
            ('risk_controller', None), # 直接接收风险控制器
        )

        def __init__(self):
            # ... (获取参数中的组件实例) ...
            self.trade_strategy = self.p.trade_strategy
            self.capital_strategy = self.p.capital_strategy
            self.portfolio_manager = self.p.portfolio_manager
            self.risk_controller = self.p.risk_controller # 获取风险控制器
            self.current_position_scale = 1.0
            # ... (其他初始化) ...

        # set_risk_controller 方法不再需要，在 __init__ 中通过参数传入

        def next(self):
            current_date = self.datas[0].datetime.date(0)

            # --- 1. 风险评估 ---
            if self.risk_controller:
                # ... (调用 evaluate 获取 scale) ...
                pass # 简化实现细节

            # --- 2. 调整现有持仓 ---
            if self.current_position_scale < 1.0:
                self._adjust_existing_positions()

            # --- 3. 获取交易信号 ---
            signals = {} # {data: signal_state}
            if hasattr(self.trade_strategy, 'generate_signals'):
                 # ... (调用组合策略的 generate_signals) ...
                 pass # 简化实现细节
            else:
                 # 旧策略逻辑 (或适配层)
                 pass

            # --- 4. 处理卖出信号 ---
            self._process_sell_signals(signals, current_date)

            # --- 5. 处理买入信号 ---
            self._process_buy_signals(signals, current_date)

        def _adjust_existing_positions(self):
            """根据风险评估执行减仓"""
            # 实现细节：
            # 1. 遍历持仓。
            # 2. 计算目标持仓量 (基于当前价值 * scale / 当前价格)。
            # 3. 如果目标数量 < 当前数量，计算差额并执行 self.sell()。
            pass # 简化实现细节

        def _process_sell_signals(self, signals, current_date):
             """处理卖出信号"""
             # 实现细节：遍历 signals，对 SELL 信号且有持仓的执行 self.close()
             pass # 简化实现细节

        def _process_buy_signals(self, signals, current_date):
            """处理买入信号，应用资金策略和风险系数"""
            # 实现细节：
            # 1. 收集 BUY 信号且无持仓的目标。
            # 2. 调用资金策略获取各目标的分配金额。
            # 3. 将分配金额乘以 self.current_position_scale。
            # 4. 计算买入数量。
            # 5. 执行 self.buy()。
            pass # 简化实现细节

        def log(self, txt, dt=None, level='info'):
             """日志记录"""
             # ... (实现日志逻辑，可选调用 portfolio_manager.log) ...
             pass # 简化实现细节

        def notify_order(self, order):
             """订单状态通知"""
             pass # 简化实现细节

        def notify_trade(self, trade):
             """交易成交通知"""
             pass # 简化实现细节

        def stop(self):
             """策略结束回调"""
             # ... (记录最终结果，可选调用 portfolio_manager.finalize) ...
             pass # 简化实现细节
    ```

## 5. 核心流程

### 5.1 核心流程 - 动态策略初始化 (时序图)

```mermaid
sequenceDiagram
    participant Runner as main_v2/task_runner
    participant Factory as PortfolioFactory
    participant Registry as ComponentRegistry
    participant MktIndMgr as MarketIndicatorManager
    participant DataLoader as PortfolioDataLoader
    participant RiskCtrl as RiskController
    participant CompStrategy as CompositeStrategy
    participant Evaluator as SignalEvaluator

    Runner ->> Factory: get_portfolio(config_with_strategy_definition)
    Factory ->> Factory: _load_config()
    opt 如果需要市场指标或风险控制
        Factory ->> MktIndMgr: __init__(data_loader)
        Factory ->> MktIndMgr: load_indicator(id) & register_transformer()
        MktIndMgr ->> DataLoader: get_data()
        DataLoader -->> MktIndMgr: market_data
        MktIndMgr -->> Factory: manager_instance
    end
    opt 如果配置了风险控制
        Factory ->> RiskCtrl: __init__(config, MktIndMgr_instance_or_None)
        RiskCtrl -->> Factory: risk_controller_instance
    end
    Factory ->> Factory: _create_composite_strategy(trade_config, MktIndMgr_instance)
        Note right of Factory: 内部: 实例化指标, 构建信号配置, 创建Evaluator, 实例化CompositeStrategy
        Factory -->> CompStrategy: 组合策略实例
    Factory ->> Factory: _create_capital_strategy(capital_config)
        Factory -->> CapitalStrategy: 资金策略实例
    Factory ->> PortfolioManager: __init__(config, loader, trade_strategy, capital_strategy, risk_controller)
    PortfolioManager -->> Factory: portfolio_manager_instance
    Factory -->> Runner: portfolio_manager_instance
```

### 5.2 核心流程 - 回测步骤 (`next`) (时序图)

```mermaid
sequenceDiagram
    participant BTEngine as Backtrader Engine
    participant BTAdapter as BacktraderStrategyAdapter
    participant RiskCtrl as RiskController
    participant MktIndMgr as MarketIndicatorManager
    participant CompStrategy as CompositeStrategy
    participant Evaluator as SignalEvaluator
    participant CapitalStrat as CapitalStrategy

    BTEngine ->> BTAdapter: next()
    BTAdapter ->> BTAdapter: current_date = self.datas[0].datetime.date(0)
    
    opt 如果配置了风险控制
        BTAdapter ->> RiskCtrl: evaluate(current_date)
        RiskCtrl ->> MktIndMgr: get_indicator_value(date)
        MktIndMgr -->> RiskCtrl: indicator_value
        RiskCtrl -->> BTAdapter: assessment = {"position_scale": ..., "triggered_rules": ...}
        BTAdapter ->> BTAdapter: self.current_position_scale = assessment["position_scale"]
        
        opt 如果需要减仓 (scale < 1.0)
            BTAdapter ->> BTAdapter: _adjust_existing_positions()
            BTAdapter ->> BTEngine: sell(size=...) 
            Note right of BTAdapter: 执行减仓
        end
    end
    
    BTAdapter ->> CompStrategy: generate_signals(date=current_date, symbol=data._name)
    Note right of BTAdapter: 对每个 data 调用
    
    CompStrategy ->> Evaluator: evaluate(data, signal_id, date, symbol)
    Note right of CompStrategy: 评估买卖信号
    
    Evaluator ->> CompStrategy: _get_indicator_output()
    Note right of Evaluator: 获取技术指标
    
    Evaluator ->> MktIndMgr: get_indicator_value()
    Note right of Evaluator: 获取市场指标
    
    Note over Evaluator: 调用信号原语 evaluate()
    Evaluator -->> CompStrategy: signal_result (bool)
    CompStrategy -->> BTAdapter: signals = {data: state, ...}

    BTAdapter ->> BTAdapter: _process_sell_signals(signals, current_date)
    opt 如果有卖出信号且持仓
        BTAdapter ->> BTEngine: close(data=...)
        Note right of BTAdapter: 执行平仓
    end
    
    BTAdapter ->> BTAdapter: _process_buy_signals(signals, current_date)
    opt 如果有买入信号且无持仓
        BTAdapter ->> CapitalStrat: allocate_capital_for_targets(...)
        Note right of BTAdapter: 获取资金分配
        
        CapitalStrat -->> BTAdapter: allocations = {symbol: amount}
        BTAdapter ->> BTAdapter: adjusted_capital = amount * self.current_position_scale
        Note right of BTAdapter: 应用风险系数
        
        BTAdapter ->> BTAdapter: size = adjusted_capital / price
        Note right of BTAdapter: 计算数量
        
        BTAdapter ->> BTEngine: buy(size=...)
        Note right of BTAdapter: 执行买入
    end
```

## 6. 配置示例

### 6.1 动态策略配置示例 (均线交叉 + VIX 过滤 + 风险控制)

```json
{
  "portfolio_id": "ma_crossover_vix_filter_rk",
  "description": "基于双均线交叉、VIX过滤和风险控制的交易策略",
  "symbols": ["SPY", "QQQ"],
  "start_date": "2020-01-01",
  "end_date": "2024-12-31",
  "cash": 100000.0,
  "currency": "USD",
  "market": "US",
  "commission": 0.0001,

  "market_indicators": ["VIX"],

  "strategy_definition": {
    "trade_strategy": {
      "strategy_type": "CompositeStrategy",
      "indicators": [
        { "id": "fastMA", "type": "SMA", "params": { "period": 10 } },
        { "id": "slowMA", "type": "SMA", "params": { "period": 30 } }
      ],
      "signals": [
        {
          "id": "ma_cross",
          "type": "Crossover",
          "inputs": [{ "ref": "fastMA" }, { "ref": "slowMA" }]
        },
        {
          "id": "ma_cross_under",
          "type": "Crossunder",
          "inputs": [{ "ref": "fastMA" }, { "ref": "slowMA" }]
        },
        {
          "id": "vix_filter",
          "type": "LessThan",
          "inputs": [{ "market": "VIX", "field": "Close" }, 25]
        }
      ],
      "composite_signals": [
        {
          "id": "final_buy",
          "type": "And",
          "inputs": [{ "ref": "ma_cross" }, { "ref": "vix_filter" }]
        }
      ],
      "outputs": {
        "buy_signal": "final_buy",
        "sell_signal": "ma_cross_under"
      }
      // "indicator_transformers": [ // 示例：定义一个转换器
      //   {"id": "VIX_SMA20", "source": "VIX", "type": "SMA", "params": {"period": 20}}
      // ]
    },
    "capital_strategy": {
      "name": "SimplePercentStrategy",
      "params": {
        "percent": 0.95
      }
    }
  },

  "risk_controller": {
    "enabled": true,
    "default_scale": 1.0,
    "rules": [
      {
        "description": "VIX高于30降低至50%仓位",
        "indicator": "VIX",
        "field": "Close",
        "condition": "greater_than",
        "threshold": 30,
        "scale": 0.5
      },
      {
        "description": "VIX高于40降低至25%仓位",
        "indicator": "VIX",
        "field": "Close",
        "condition": "greater_than",
        "threshold": 40,
        "scale": 0.25
      }
      // 复杂规则示例 (需要实现 SPY_SMA200 转换器并在 market_indicators 中加载 SPY)
      // {
      //   "description": "SPY 跌破 200 日均线时降低至 75% 仓位",
      //   "condition": {
      //     "type": "LessThan",
      //     "inputs": [
      //       {"market": "SPY", "field": "Close"},
      //       {"market": "SPY_SMA200"} // 假设转换器 ID 是这个
      //     ]
      //   },
      //   "scale": 0.75
      // }
    ]
  }
}
```

### 6.2 布朗永久组合配置示例

```json
{
  "portfolio_id": "permanent_portfolio_rebal",
  "description": "布朗永久组合，按季度或阈值再平衡",
  "symbols": ["SPY", "TLT", "GLD", "SHV"],
  "start_date": "2010-01-01",
  "end_date": "2024-12-31",
  "cash": 100000.0,
  "currency": "USD",
  "market": "US",
  "commission": 0.0001,

  "strategy_definition": {
    "trade_strategy": {
      "strategy_type": "CompositeStrategy",
      "indicators": [],
      "signals": [
          {"id": "always_true", "type": "ConstantSignal", "params": {"value": true}}
      ],
      "outputs": {
        "buy_signal": "always_true",
        "sell_signal": "always_true"
      }
    },
    "capital_strategy": {
      "name": "AssetAllocationStrategy", // 需要实现这个策略
      "params": {
        "initial_capital": 100000,
        "target_allocations": {
          "stocks": 25, "bonds": 25, "gold": 25, "cash": 25
        },
        "asset_mapping": {
            "SPY": "stocks", "TLT": "bonds", "GLD": "gold", "SHV": "cash"
        },
        "rebalance_frequency": "quarterly", // 'monthly', 'quarterly', 'yearly', 'none'
        "rebalance_threshold": 5
      }
    }
  },
  "risk_controller": {
    "enabled": false
  }
}
```
*(需要实现 `AssetAllocationStrategy` 资金策略和 `ConstantSignal` 信号原语)*

## 7. 支持的策略类型与边界

### 7.1 支持的策略类型

* **基础技术指标策略**: 单/双/多均线交叉, RSI, MACD, ROC, ATR/布林带突破, 趋势跟踪 (ADX, 吊灯), 震荡指标 (Stochastic) 等。
* **信号组合策略**: 多指标确认 (AND), 条件或 (OR), 阈值筛选 (>, <)。
* **市场状态集成**: 可使用 VIX, 指数等市场指标进行信号过滤或风险控制。
* **风险管理**: 基于市场指标动态调整整体仓位比例，对现有持仓进行减仓。
* **(需扩展)** **资产配置与再平衡**: 如布朗永久组合，通过特定的资金策略实现。

### 7.2 支持边界 (当前架构不太适合或无法直接支持)

* **复杂机器学习/深度学习策略**: 需要大量特征工程、在线训练模型。
* **高频交易策略**: 架构未针对低延迟和 tick 级数据优化。
* **复杂统计套利**: 如配对交易、协整分析、多资产统计套利。
* **自适应参数策略**: 需要根据市场动态调整原语参数。
* **复杂组合优化**: 如风险平价、均值方差优化等。

## 8. 总结

本设计方案在现有架构基础上，通过引入原语化、配置驱动、市场指标集成和风险控制等核心组件，显著提升了策略的灵活性和可扩展性。关键在于 `ComponentRegistry` 提供了统一的组件管理，`PortfolioFactory` 负责根据配置动态组装策略，`SignalEvaluator` 和 `RiskController` 则封装了核心的评估逻辑。通过 `strategy_definition` JSON 配置，用户可以在不编写 Python 代码的情况下定义和组合出多种交易策略。同时，通过保留对旧策略类的兼容加载，确保了系统的平稳过渡。

该设计能够支持常见的大多数技术分析和规则型交易策略，并加入了基于市场状态的风险管理能力，满足了个人量化交易的主要需求。对于更高级的需求，如机器学习或复杂组合优化，则需要在当前基础上进一步扩展。
