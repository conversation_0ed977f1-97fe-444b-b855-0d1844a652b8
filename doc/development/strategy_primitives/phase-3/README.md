# Phase 3 Implementation Strategy: Expanding Strategy Primitives

This document provides strategic guidance for implementing Phase 3 of the strategy primitives framework, explaining the approach and priorities to help developers accomplish this task efficiently.

## Introduction

In Phase 3, we aim to significantly expand the primitive library by adding:
1. New technical indicators (EMA, RSI, MACD, etc.)
2. New signal primitives (logical operators, comparison signals, etc.)
3. Creating example composite strategies to validate the system

This expansion will enable the creation of sophisticated trading strategies entirely through configuration, without requiring custom Python code.

## Implementation Approach

### Directory Structure

New primitives should be organized by category in appropriate subdirectories:

```
app/components/
├── indicators/
│   ├── __init__.py
│   ├── moving_averages.py (add EMA here)
│   ├── momentum.py (for RSI, MACD)
│   ├── volatility.py (for Bollinger Bands, ATR)
│   └── oscillators.py (for Stochastic)
└── signals/
    ├── __init__.py
    ├── comparison.py (add <PERSON><PERSON>han, InRange here)
    ├── logical.py (for And, Or, Not)
    └── pattern.py (for Streak, PercentChange)
```

### Implementation Order

For efficient development, we recommend this implementation sequence:

1. **Start with the simplest extensions**:
   - Add `GreaterThan` to complement existing `<PERSON><PERSON>han`
   - Add `EMA` as a natural extension to existing `SMA`

2. **Add logical operators**:
   - Implement `And`, `Or`, `Not` to enable complex signal composition
   - These are essential for combining multiple signals

3. **Add core technical indicators**:
   - Implement `RSI` for momentum analysis
   - Implement `BollingerBands` for volatility analysis

4. **Add advanced signals**:
   - Implement `InRange`, `Streak`, `PercentChange`

5. **Add complex indicators**:
   - Implement `MACD`, `Stochastic`, `ATR`

### Component Registry Integration

All new primitives must be registered with the `ComponentRegistry`. This can be done:

1. By direct registration in the registry's initialization:
   ```python
   # In registry.py
   self.register_component('indicators', 'RSI', 'components.indicators.momentum', 'RSI')
   ```

2. By scanning the appropriate directories (requires well-organized files)

## Testing Strategy

### Test-Driven Development

We strongly recommend a test-driven approach:

1. Write test cases first, using known values from other libraries (pandas-ta, TA-Lib)
2. Implement the primitive to pass the tests
3. Verify against existing strategies where possible

### Test Structure

For each new component, create test files that:
1. Test basic functionality
2. Test edge cases (NaN values, first values, insufficient data)
3. Test parameter validation
4. Compare with known outputs

## Technical Considerations

### Multi-Output Indicators

Some indicators (Bollinger Bands, MACD) produce multiple outputs. There are two viable approaches:

1. **Dictionary approach**: Return a dictionary of Series, with specific keys for each component:
   ```python
   # Bollinger Bands example
   return {
       'upper': upper_band, 
       'middle': middle_band, 
       'lower': lower_band
   }
   ```

2. **Multiple indicator approach**: Split into separate indicators for each component

We recommend the dictionary approach for cohesion, but this will require enhancing `SignalEvaluator` to handle this pattern:

```json
// Reference a specific output from a multi-output indicator
{
  "ref": "bollinger",
  "output": "upper"
}
```

### Performance Optimization

As we add more complex indicators, performance may become a concern:

1. **Add caching**: Consider implementing caching for indicator results
2. **Optimize calculations**: Use vectorized operations where possible
3. **Reduce redundant computations**: For indicators that use other indicators (e.g., MACD uses EMAs)

### Error Handling

Establish consistent error handling patterns:

1. Validate all parameters in the `validate_params` method
2. Handle edge cases gracefully (NaNs, insufficient data)
3. Provide clear error messages when validation fails

## Example Strategy Configurations

To validate the new primitives, create reference strategies:

### RSI Mean Reversion

```json
{
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        { "id": "rsi", "type": "RSI", "params": { "period": 14 } }
      ],
      "signals": [
        {
          "id": "oversold", 
          "type": "LessThan", 
          "inputs": [{ "ref": "rsi" }, 30]
        },
        {
          "id": "overbought", 
          "type": "GreaterThan", 
          "inputs": [{ "ref": "rsi" }, 70]
        }
      ],
      "outputs": {
        "buy_signal": "oversold",
        "sell_signal": "overbought"
      }
    }
  }
}
```

### Moving Average with Volume Confirmation

```json
{
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        { "id": "fastMA", "type": "EMA", "params": { "period": 10 } },
        { "id": "slowMA", "type": "EMA", "params": { "period": 20 } },
        { "id": "volumeMA", "type": "SMA", "params": { "period": 20, "column": "Volume" } }
      ],
      "signals": [
        {
          "id": "price_cross_up", 
          "type": "Crossover", 
          "inputs": [{ "ref": "fastMA" }, { "ref": "slowMA" }]
        },
        {
          "id": "price_cross_down", 
          "type": "Crossunder", 
          "inputs": [{ "ref": "fastMA" }, { "ref": "slowMA" }]
        },
        {
          "id": "high_volume", 
          "type": "GreaterThan", 
          "inputs": [{ "column": "Volume" }, { "ref": "volumeMA" }]
        },
        {
          "id": "buy_condition", 
          "type": "And", 
          "inputs": [{ "ref": "price_cross_up" }, { "ref": "high_volume" }]
        }
      ],
      "outputs": {
        "buy_signal": "buy_condition",
        "sell_signal": "price_cross_down"
      }
    }
  }
}
```

## Best Practices and Common Pitfalls

### Numeric Errors and Edge Cases

Technical indicators can be prone to numeric errors:
- Division by zero: Always handle division with care
- NaN propagation: Be mindful of how NaN values propagate through calculations
- First values: Pay special attention to initialization for first values
- Zero values: Some indicators (like RSI) can't handle all-zero inputs properly

### Series Alignment

Ensure that all returned Series:
- Have the same index as the input data
- Don't inadvertently shift data by offset indexing
- Use `fillna` appropriately to handle missing values

### Signal Composability

When designing signal primitives, ensure they can be easily composed:
- All signals should take Series as inputs and return Series/scalars
- Handle type conversion consistently
- Validate input assumptions (types, sizes)

## Future Enhancements (Post-Phase 3)

After completing Phase 3, consider these enhancements:

1. **Custom signal serialization**: Support saving/loading signals to disk
2. **Strategy visualization**: Add tools to visualize configured signals and indicators
3. **Signal statistics**: Add analytics for signal quality (false positives, etc.)
4. **Parameter optimization**: Framework for optimizing indicator parameters

## Conclusion

Expanding the primitive library in Phase 3 will transform the strategy system from a basic framework to a powerful configuration-driven platform. By following this implementation plan and adhering to the design principles, you'll build a robust system that enables sophisticated trading strategies without custom code.

Remember to maintain consistent error handling, thorough documentation, and comprehensive testing throughout the implementation process.
