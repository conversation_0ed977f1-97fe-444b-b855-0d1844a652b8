# Strategy Primitives Phase 3: Implementation Guide

This document provides guidance for implementing the expanded set of indicator and signal primitives for the strategy system. It explains the architectural approach, integration considerations, and provides examples for validating the implementation.

## 1. Architecture and Design Principles

### 1.1 Indicator Primitives Design

All indicator primitives should:

- Inherit from `BaseIndicatorPrimitive` in `components/base/primitives.py`
- Implement the `calculate` method, which takes a pandas DataFrame and returns a Series or Dictionary of Series
- Provide proper parameter validation in `validate_params` method
- Handle edge cases gracefully (e.g., insufficient data, NaN values)
- Return data with the same index as the input DataFrame

Multi-output indicators (like Bollinger Bands, MACD) should return a dictionary of Series objects, with keys representing each output component.

### 1.2 Signal Primitives Design

All signal primitives should:

- Inherit from `BaseSignalPrimitive` in `components/base/primitives.py`
- Implement the `evaluate` method, which takes one or more Series and returns a boolean Series or scalar
- Handle date-specific evaluation when the `date` parameter is provided
- Validate input parameters and handle edge cases gracefully

### 1.3 Registry Integration

- All new primitives should be registered in `ComponentRegistry`
- Use manual registration as shown in the implementation example:

```python
# In ComponentRegistry.__init__
self.register_component('indicators', 'RSI', 'components.indicators.momentum', 'RSI')
```

## 2. Implementation Guidelines

### 2.1 Technical Indicators

#### EMA (Exponential Moving Average)
- Use pandas' `ewm()` function with `span=period` and `adjust=False` parameters
- First value should match the first value of the input series (standard convention)
- Ensure the Series is properly indexed

#### RSI (Relative Strength Index)
- Calculate price changes using `diff()`
- Separate gains and losses
- Use exponential moving average to smooth gains and losses
- Formula: `100 - (100 / (1 + RS))` where `RS = avg_gain / avg_loss`

#### ATR (Average True Range)
- Calculate true range as the maximum of:
  1. High - Low
  2. |High - Previous Close|
  3. |Low - Previous Close|
- Use simple moving average to smooth the true range values

#### Bollinger Bands
- Calculate the middle band as a simple moving average (SMA)
- Calculate the standard deviation of prices over the period
- Upper Band = Middle Band + (Standard Deviation × Factor)
- Lower Band = Middle Band - (Standard Deviation × Factor)
- Return a dictionary with 'upper', 'middle', and 'lower' keys

#### MACD (Moving Average Convergence Divergence)
- Calculate the fast EMA (typically 12 periods)
- Calculate the slow EMA (typically 26 periods)
- MACD Line = Fast EMA - Slow EMA
- Signal Line = EMA of MACD Line (typically 9 periods)
- Histogram = MACD Line - Signal Line
- Return a dictionary with 'macd', 'signal', and 'histogram' keys

#### Stochastic Oscillator
- Calculate %K = (Current Close - Lowest Low) / (Highest High - Lowest Low) × 100
- Calculate %D = SMA of %K (typically 3 periods)
- Return a dictionary with 'k' and 'd' keys

### 2.2 Signal Primitives

#### And/Or Logic Operators
- Take two boolean Series as input
- Apply the logical operation element-wise
- Handle NaN values appropriately (typically as False)

#### GreaterThan/InRange
- Support both Series-to-Series and Series-to-scalar comparisons
- Return a boolean Series with the same index as inputs

#### Streak
- Count consecutive occurrences of a condition
- Reset counter when condition is broken
- Return boolean Series based on streak threshold

#### PercentChange
- Calculate percentage change over specified period
- Compare against threshold value
- Support both positive and negative thresholds

## 3. Testing Strategy

### 3.1 Unit Testing

Create dedicated test files in `tests/core/test_primitives/` for each primitive type:

- `test_momentum_indicators.py` - For RSI, MACD, etc.
- `test_volatility_indicators.py` - For Bollinger Bands, ATR, etc.
- `test_trend_indicators.py` - For moving averages
- `test_logical_signals.py` - For And, Or, Not primitives
- `test_comparison_signals.py` - For GreaterThan, InRange primitives

Each test should:
1. Create input data with known characteristics
2. Apply the primitive with specific parameters
3. Verify output against pre-calculated expected values
4. Check behavior with edge cases (NaN, insufficient data)

### 3.2 Integration Testing

Validate that primitives work correctly within the strategy framework:

1. Create strategy configurations using the new primitives
2. Run backtests to ensure signals are generated correctly
3. Compare against equivalent implementations in traditional strategies

## 4. Example Primitive Implementations

### 4.1 Example RSI Indicator

```python
import pandas as pd
import numpy as np
from components.base.primitives import BaseIndicatorPrimitive

class RSI(BaseIndicatorPrimitive):
    """Relative Strength Index indicator primitive.
    
    Calculates the RSI technical indicator which measures the magnitude of recent
    price changes to evaluate overbought or oversold conditions.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the RSI calculation
            - column (str): The column to calculate RSI on (default: 'Close')
    """
    
    def get_default_params(self):
        return {
            'period': 14,
            'column': 'Close'
        }
    
    def validate_params(self):
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        
    def calculate(self, data):
        price = data[self.params['column']]
        delta = price.diff()
        
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window=self.params['period'], min_periods=1).mean()
        avg_loss = loss.rolling(window=self.params['period'], min_periods=1).mean()
        
        # For actual RSI calculation after initial period
        for i in range(self.params['period']+1, len(delta)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (self.params['period']-1) + gain.iloc[i]) / self.params['period']
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (self.params['period']-1) + loss.iloc[i]) / self.params['period']
        
        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
```

### 4.2 Example Or Signal

```python
import pandas as pd
import numpy as np
from components.base.primitives import BaseSignalPrimitive

class Or(BaseSignalPrimitive):
    """Logical OR signal primitive.
    
    Returns True when either of two input signals is True.
    
    Attributes:
        params (Dict[str, Any]): No specific parameters required
    """
    
    def evaluate(self, *inputs, date=None):
        """Evaluate the OR condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series of boolean values
            date (pd.Timestamp, optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: Result of the OR operation
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("Or requires exactly two input series")
        
        series_a, series_b = inputs
        result = (series_a | series_b)
        
        # Handle NaN values
        result = result.fillna(False)
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
```

## 5. Example Strategy Configurations

### 5.1 RSI + Moving Average Strategy

```json
{
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        { "id": "rsi", "type": "RSI", "params": { "period": 14 } },
        { "id": "ema10", "type": "EMA", "params": { "period": 10 } },
        { "id": "ema20", "type": "EMA", "params": { "period": 20 } }
      ],
      "signals": [
        {
          "id": "rsi_oversold",
          "type": "LessThan",
          "inputs": [{ "ref": "rsi" }, 30]
        },
        {
          "id": "ema_cross",
          "type": "Crossover",
          "inputs": [{ "ref": "ema10" }, { "ref": "ema20" }]
        },
        {
          "id": "ema_cross_under",
          "type": "Crossunder",
          "inputs": [{ "ref": "ema10" }, { "ref": "ema20" }]
        },
        {
          "id": "buy_condition",
          "type": "And",
          "inputs": [{ "ref": "rsi_oversold" }, { "ref": "ema_cross" }]
        }
      ],
      "outputs": {
        "buy_signal": "buy_condition",
        "sell_signal": "ema_cross_under"
      }
    }
  }
}
```

### 5.2 Bollinger Band Strategy

```json
{
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        { 
          "id": "bollinger", 
          "type": "BollingerBands", 
          "params": { 
            "period": 20,
            "std_dev": 2.0
          } 
        }
      ],
      "signals": [
        {
          "id": "price_below_lower",
          "type": "LessThan",
          "inputs": [
            { "column": "Close" },
            { "ref": "bollinger", "output": "lower" }
          ]
        },
        {
          "id": "price_above_upper",
          "type": "GreaterThan",
          "inputs": [
            { "column": "Close" },
            { "ref": "bollinger", "output": "upper" }
          ]
        },
        {
          "id": "price_crossover_middle",
          "type": "Crossover",
          "inputs": [
            { "column": "Close" },
            { "ref": "bollinger", "output": "middle" }
          ]
        }
      ],
      "outputs": {
        "buy_signal": "price_below_lower",
        "sell_signal": "price_above_upper"
      }
    }
  }
}
```

## 6. Implementation Sequence Recommendation

For efficient implementation and testing, we recommend the following sequence:

1. **First batch of indicators**:
   - EMA (simpler version of SMA)
   - RSI (common momentum indicator)
   - ATR (foundation for other indicators)

2. **First batch of signals**:
   - GreaterThan (complement to existing LessThan)
   - And/Or (basic logical operations)

3. **Second batch of indicators**:
   - BollingerBands (uses SMA, adds volatility measure)
   - MACD (uses EMA, multi-output indicator)

4. **Second batch of signals**:
   - Not (logical negation)
   - InRange (combines comparison operations)
   - PercentChange (momentum signal)

5. **Final batch**:
   - Stochastic (more complex oscillator)
   - Streak (state tracking signal)

This sequence lets you build on previously implemented components and gradually increase complexity.

## 7. Expected Challenges

- **Multi-output indicators**: Handling indicators that produce multiple series requires careful design of both the indicator and how SignalEvaluator handles its outputs.

- **Performance optimization**: With more complex indicators, performance might become an issue, especially for long backtests. Consider adding caching mechanisms.

- **Parameter validation**: Different indicators have different parameter constraints. Ensure each primitive validates its parameters thoroughly.

- **Signal combination complexity**: As more signal primitives are added, the complexity of how they can be combined increases. Test thoroughly!

## 8. Conclusion

The strategy primitives system is evolving into a powerful, flexible framework for trading strategy development. The planned expansion in Phase 3 will significantly increase the system's capabilities, enabling the creation of sophisticated strategies through configuration rather than code.

Remember to maintain consistent error handling, thorough documentation, and comprehensive testing throughout the implementation process.

---

```python
# Example implementation for RSI indicator

"""
RSI (Relative Strength Index) indicator primitive.

This module implements the Relative Strength Index technical indicator as a primitive component.
"""

import pandas as pd
import numpy as np
from components.base.primitives import BaseIndicatorPrimitive

class RSI(BaseIndicatorPrimitive):
    """Relative Strength Index indicator primitive.
    
    Calculates the RSI technical indicator which measures the magnitude of recent
    price changes to evaluate overbought or oversold conditions.
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the RSI calculation (default: 14)
            - column (str): The column to calculate RSI on (default: 'Close')
    """
    
    def get_default_params(self) -> dict:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters with period=14 and column='Close'
        """
        return {
            'period': 14,
            'column': 'Close'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If column is not a string
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
    
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """Calculate the Relative Strength Index.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            pd.Series: The calculated RSI values
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        price = data[self.params['column']]
        delta = price.diff()
        
        # Separate gains and losses
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # Use SMA for first calculation
        avg_gain = gain.rolling(window=self.params['period'], min_periods=1).mean()
        avg_loss = loss.rolling(window=self.params['period'], min_periods=1).mean()
        
        # Use Wilder's smoothing method after initial period
        for i in range(self.params['period']+1, len(delta)):
            avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (self.params['period']-1) + gain.iloc[i]) / self.params['period']
            avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (self.params['period']-1) + loss.iloc[i]) / self.params['period']
        
        # Calculate RS and RSI
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        # Handle division by zero and other anomalies
        rsi = rsi.replace([np.inf, -np.inf], np.nan).fillna(50)
        
        return rsi


# Example implementation for Bollinger Bands indicator

"""
Bollinger Bands indicator primitive.

This module implements the Bollinger Bands technical indicator as a primitive component.
"""

import pandas as pd
from typing import Dict
from components.base.primitives import BaseIndicatorPrimitive

class BollingerBands(BaseIndicatorPrimitive):
    """Bollinger Bands indicator primitive.
    
    Calculates the Bollinger Bands technical indicator which consists of:
    - Middle band: SMA of the price
    - Upper band: Middle band + (standard deviation * factor)
    - Lower band: Middle band - (standard deviation * factor)
    
    Attributes:
        params (Dict[str, Any]): Dictionary containing:
            - period (int): The lookback period for the SMA calculation (default: 20)
            - std_dev (float): The standard deviation factor (default: 2.0)
            - column (str): The column to calculate bands on (default: 'Close')
    """
    
    def get_default_params(self) -> dict:
        """Get default parameters.
        
        Returns:
            Dict[str, Any]: Default parameters
        """
        return {
            'period': 20,
            'std_dev': 2.0,
            'column': 'Close'
        }
    
    def validate_params(self) -> None:
        """Validate parameters.
        
        Raises:
            ValueError: If period is not a positive integer
            ValueError: If std_dev is not a positive number
            ValueError: If column is not a string
        """
        if not isinstance(self.params['period'], int) or self.params['period'] <= 0:
            raise ValueError("Period must be a positive integer")
        if not isinstance(self.params['std_dev'], (int, float)) or self.params['std_dev'] <= 0:
            raise ValueError("std_dev must be a positive number")
        if not isinstance(self.params['column'], str):
            raise ValueError("Column must be a string")
    
    def calculate(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate the Bollinger Bands.
        
        Args:
            data (pd.DataFrame): OHLCV price data
        
        Returns:
            Dict[str, pd.Series]: Dictionary containing 'upper', 'middle', and 'lower' band Series
            
        Raises:
            KeyError: If the specified column is not found in the data
        """
        if self.params['column'] not in data.columns:
            raise KeyError(f"Column {self.params['column']} not found in data")
        
        price = data[self.params['column']]
        
        # Calculate middle band (SMA)
        middle_band = price.rolling(window=self.params['period'], min_periods=1).mean()
        
        # Calculate standard deviation
        std_dev = price.rolling(window=self.params['period'], min_periods=1).std()
        
        # Calculate upper and lower bands
        upper_band = middle_band + (std_dev * self.params['std_dev'])
        lower_band = middle_band - (std_dev * self.params['std_dev'])
        
        # Return dictionary of bands
        return {
            'middle': middle_band,
            'upper': upper_band,
            'lower': lower_band
        }


# Example implementation for logical signal operators

"""
Logical operator signal primitives.

This module implements logical operator signal primitives (And, Or, Not) for combining signals.
"""

import pandas as pd
from typing import Union, Optional
from components.base.primitives import BaseSignalPrimitive

class And(BaseSignalPrimitive):
    """Logical AND signal primitive.
    
    Returns True when both input signals are True.
    """
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate the AND condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series of boolean values
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: Result of the AND operation
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("And requires exactly two input series")
        
        series_a, series_b = inputs
        
        # Convert to boolean Series and handle NaN values
        bool_a = series_a.fillna(False).astype(bool)
        bool_b = series_b.fillna(False).astype(bool)
        
        result = bool_a & bool_b
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result

class Or(BaseSignalPrimitive):
    """Logical OR signal primitive.
    
    Returns True when either of two input signals is True.
    """
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate the OR condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series of boolean values
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: Result of the OR operation
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("Or requires exactly two input series")
        
        series_a, series_b = inputs
        
        # Convert to boolean Series and handle NaN values
        bool_a = series_a.fillna(False).astype(bool)
        bool_b = series_b.fillna(False).astype(bool)
        
        result = bool_a | bool_b
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result

class Not(BaseSignalPrimitive):
    """Logical NOT signal primitive.
    
    Returns True when the input signal is False.
    """
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate the NOT condition.
        
        Args:
            *inputs (pd.Series): Exactly one Series of boolean values
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: Result of the NOT operation
            
        Raises:
            ValueError: If not exactly one input provided
        """
        if len(inputs) != 1:
            raise ValueError("Not requires exactly one input series")
        
        series = inputs[0]
        
        # Convert to boolean Series and handle NaN values
        bool_series = series.fillna(False).astype(bool)
        
        result = ~bool_series
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result


# Example implementation for GreaterThan signal

"""
GreaterThan signal primitive.

This module implements the GreaterThan comparison signal primitive.
"""

import pandas as pd
import numpy as np
from typing import Union, Optional
from components.base.primitives import BaseSignalPrimitive

class GreaterThan(BaseSignalPrimitive):
    """Compares if one series is greater than another.
    
    Signal is True when series_a is greater than series_b.
    """
    
    def evaluate(self, *inputs: pd.Series, date: Optional[pd.Timestamp] = None) -> Union[bool, pd.Series]:
        """Evaluate greater than condition.
        
        Args:
            *inputs (pd.Series): Exactly two Series to compare
            date (Optional[pd.Timestamp], optional): Specific date to evaluate
        
        Returns:
            Union[bool, pd.Series]: True when first series is greater than second
            
        Raises:
            ValueError: If not exactly two inputs provided
        """
        if len(inputs) != 2:
            raise ValueError("GreaterThan requires exactly two input series")
        
        series_a, series_b = inputs
        
        # Convert scalar second parameter to Series if needed
        if not isinstance(series_b, pd.Series):
            series_b = pd.Series(series_b, index=series_a.index)
        
        result = series_a > series_b
        
        # Handle NaN values
        result = result.mask(series_a.isna() | series_b.isna())
        
        if date is not None:
            return bool(result.loc[date]) if date in result.index else False
        return result
```
