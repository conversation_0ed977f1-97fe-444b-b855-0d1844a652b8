# Strategy Primitives Phase 4: Implementation Guide

This document provides guidance for implementing market indicators and transformers integration for the strategy primitives system. It explains the architectural approach, integration considerations, and provides implementation examples.

## 1. Architecture and Design Principles

### 1.1 Market Indicator Manager Design

The `MarketIndicatorManager` should:

- Interface with existing data loader mechanisms to fetch market indicator data
- Provide consistent date-based indicator value retrieval
- Support transformation of raw indicator data through transformers
- Handle edge cases gracefully (missing dates, unavailable indicators, etc.)

The design should avoid duplicating existing caching mechanisms in data loaders and focus on providing a clean interface for strategy components to access market data.

### 1.2 Market Transformer Design

Market transformers should:

- Inherit from `BaseMarketTransformer`
- Implement the `transform` method that takes market data and applies a transformation
- Support parameterization for flexible configuration
- Handle edge cases appropriately (insufficient data, NaN values)

### 1.3 Signal Integration Design

The extension to `SignalEvaluator` should:

- Maintain backward compatibility with existing signal evaluation logic
- Add support for market indicator references through a simple syntax
- Handle potential data misalignment (different date ranges between market data and security data)

## 2. Implementation Guidelines

### 2.1 Market Indicator Manager

```python
# app/components/market_indicators.py
import pandas as pd
import logging
from components.registry import ComponentRegistry

class MarketIndicatorManager:
    """
    Manages market indicators data loading and access.
    
    This class provides a unified interface to access various market indicators
    like VIX, market PE ratios, etc., and supports transformations of these indicators.
    """
    
    # Mapping of indicator IDs to their data sources
    INDICATORS_MAP = {
        "VIX": {"symbol": "^VIX", "market": "US", "source": "yahoo"},
        "SPX": {"symbol": "^GSPC", "market": "US", "source": "yahoo"},
        # Add more indicators as needed
    }
    
    def __init__(self, data_loader):
        """
        Initialize the market indicator manager.
        
        Args:
            data_loader: PortfolioDataLoader instance for fetching data
        """
        self.data_loader = data_loader
        self.indicators_data = {}  # Original indicator data 
        self.transformers = {}     # Transformer instances
        self.transformed_data = {} # Transformed data
        self.registry = ComponentRegistry.get_instance()
        self.logger = logging.getLogger(__name__)
        
    def load_indicator(self, indicator_id, start_date, end_date=None):
        """
        Load market indicator data.
        
        Args:
            indicator_id: ID of the indicator to load
            start_date: Start date for data
            end_date: End date for data (defaults to today)
            
        Returns:
            bool: True if successful, False otherwise
        """
        if indicator_id not in self.INDICATORS_MAP:
            self.logger.error(f"Unknown indicator ID: {indicator_id}")
            return False
            
        indicator_info = self.INDICATORS_MAP[indicator_id]
        symbol = indicator_info["symbol"]
        market = indicator_info["market"]
        source = indicator_info["source"]
        
        # Load data using existing data loader
        try:
            if source == "yahoo":
                df = self.data_loader.load_yahoo_finance_data(symbol, start_date, end_date)
            elif source == "custom":
                # Handle custom data sources if needed
                pass
            else:
                self.logger.error(f"Unsupported data source: {source}")
                return False
                
            if df is None or df.empty:
                self.logger.error(f"Failed to load data for {indicator_id}")
                return False
                
            self.indicators_data[indicator_id] = df
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading indicator {indicator_id}: {str(e)}")
            return False
            
    def register_transformer(self, transformed_id, source_indicator_id, transformer_type, params=None):
        """
        Register and apply a transformer to a market indicator.
        
        Args:
            transformed_id: ID for the transformed data
            source_indicator_id: Source indicator ID
            transformer_type: Type of transformer to apply
            params: Parameters for the transformer
            
        Returns:
            bool: True if successful, False otherwise
        """
        if source_indicator_id not in self.indicators_data:
            self.logger.error(f"Source indicator {source_indicator_id} not loaded")
            return False
            
        # Get transformer class
        transformer_class = self.registry.get_market_transformer(transformer_type)
        if transformer_class is None:
            # Try to use indicator primitive as fallback
            transformer_class = self.registry.get_indicator(transformer_type)
            
        if transformer_class is None:
            self.logger.error(f"Unknown transformer type: {transformer_type}")
            return False
            
        # Create transformer instance
        transformer = transformer_class(params=params or {})
        self.transformers[transformed_id] = transformer
        
        # Apply transformation
        source_data = self.indicators_data[source_indicator_id]['Close'] 
        if hasattr(transformer, 'transform'):
            transformed = transformer.transform(source_data)
        else:
            # Fall back to calculate for indicator primitives
            transformed = transformer.calculate(
                pd.DataFrame({'Close': source_data})
            )
            
        self.transformed_data[transformed_id] = transformed
        return True
        
    def get_indicator_value(self, indicator_id, date, field='Close'):
        """
        Get indicator value for a specific date.
        
        Args:
            indicator_id: Indicator ID
            date: Date to get value for
            field: Data field to retrieve
            
        Returns:
            float: Indicator value or None if not available
        """
        # Check transformed data first
        if indicator_id in self.transformed_data:
            data = self.transformed_data[indicator_id]
            if isinstance(data, pd.Series):
                # For Series, simply look up the date
                if date in data.index:
                    return data.loc[date]
                # Use asof to get the most recent value before date
                try:
                    return data.asof(date)
                except:
                    return None
            return None
                
        # If not transformed, check original data
        if indicator_id in self.indicators_data:
            data = self.indicators_data[indicator_id]
            if date in data.index and field in data.columns:
                return data.loc[date, field]
            # Use asof to get the most recent value before date
            try:
                return data.asof(date)[field]
            except:
                return None
                
        self.logger.warning(f"Indicator {indicator_id} not found")
        return None
        
    def get_indicator_series(self, indicator_id, field='Close'):
        """
        Get full time series for an indicator.
        
        Args:
            indicator_id: Indicator ID
            field: Data field to retrieve
            
        Returns:
            pd.Series: Time series of indicator values
        """
        if indicator_id in self.transformed_data:
            return self.transformed_data[indicator_id]
            
        if indicator_id in self.indicators_data:
            data = self.indicators_data[indicator_id]
            if field in data.columns:
                return data[field]
                
        return None
```

### 2.2 Market Transformers Implementation

```python
# app/components/market_transformers.py
import pandas as pd
import numpy as np
from components.base.primitives import BaseComponentPrimitive

class BaseMarketTransformer(BaseComponentPrimitive):
    """
    Base class for market indicator transformers.
    
    Market transformers take indicator data and apply various transformations
    like moving averages, normalization, etc.
    """
    
    def __init__(self, params=None):
        """
        Initialize the transformer with parameters.
        
        Args:
            params: Parameter dictionary
        """
        super().__init__(params or {})
        
    def transform(self, data):
        """
        Transform the input data.
        
        Args:
            data: pd.Series or DataFrame with input data
            
        Returns:
            pd.Series or DataFrame: Transformed data
        """
        raise NotImplementedError("Subclasses must implement transform")
        
    def validate_params(self):
        """Validate parameters for this transformer"""
        return True

class MovingAverageTransformer(BaseMarketTransformer):
    """Calculate moving average of input data."""
    
    def __init__(self, params=None):
        super().__init__(params)
        self.window = self.params.get('period', 20)
        
    def transform(self, data):
        """
        Calculate moving average.
        
        Args:
            data: Input data series
            
        Returns:
            pd.Series: Moving average series
        """
        return data.rolling(window=self.window).mean()
        
    def validate_params(self):
        """Validate window parameter"""
        if not isinstance(self.window, int) or self.window <= 0:
            return False
        return True

class PercentileRankTransformer(BaseMarketTransformer):
    """Calculate percentile rank of values within a rolling window."""
    
    def __init__(self, params=None):
        super().__init__(params)
        self.window = self.params.get('window', 252)  # Default to 1 trading year
        
    def transform(self, data):
        """
        Calculate percentile rank.
        
        Args:
            data: Input data series
            
        Returns:
            pd.Series: Percentile rank series (0-100)
        """
        def percentile_rank(x):
            # Calculate percentage of values in window that are <= current value
            return 100 * (x <= x.iloc[-1]).mean()
            
        result = data.rolling(window=self.window).apply(
            percentile_rank, raw=False
        )
        return result

class RelativeStrengthTransformer(BaseMarketTransformer):
    """Calculate relative strength compared to a baseline period."""
    
    def __init__(self, params=None):
        super().__init__(params)
        self.base_period = self.params.get('base_period', 60)  # Default to 60 days
        
    def transform(self, data):
        """
        Calculate relative strength.
        
        Args:
            data: Input data series
            
        Returns:
            pd.Series: Relative strength index
        """
        baseline = data.rolling(window=self.base_period).mean()
        relative_strength = (data / baseline) * 100
        return relative_strength
```

### 2.3 Registry Extension

```python
# app/components/registry.py (update)

def __init__(self):
    # Existing initialization code...
    self.market_transformers = {}
    # ...

def _initialize_registry(self):
    # Existing initialization code...
    
    # Scan and register market transformers
    self._scan_and_register('app.components.market_transformers', 
                          self.market_transformers,
                          BaseMarketTransformer)
    
    # Manual registrations as needed
    self.register_component('market_transformers', 'MovingAverage', 
                          'components.market_transformers', 'MovingAverageTransformer')
    self.register_component('market_transformers', 'PercentileRank', 
                          'components.market_transformers', 'PercentileRankTransformer')
    self.register_component('market_transformers', 'RelativeStrength', 
                          'components.market_transformers', 'RelativeStrengthTransformer')
    
def get_market_transformer(self, name):
    """
    Get a market transformer class by name.
    
    Args:
        name: Name of the transformer
        
    Returns:
        class: The transformer class or None if not found
    """
    return self.market_transformers.get(name)
```

### 2.4 Signal Evaluator Extension

```python
# app/components/signal_evaluator.py (update)

def __init__(self, registry, market_indicator_manager=None):
    """
    Initialize signal evaluator.
    
    Args:
        registry: ComponentRegistry instance for accessing primitives
        market_indicator_manager: MarketIndicatorManager instance for market data
    """
    self._registry = registry
    self._market_indicator_manager = market_indicator_manager

def _evaluate_node(self, node, data, component_source=None):
    """
    Recursively evaluate a node in the signal tree.
    
    Args:
        node: Node configuration
        data: Input data frame
        component_source: Source of the component ('indicators' or 'signals')
        
    Returns:
        Union[pd.Series, Dict[str, pd.Series]]: Evaluation result
    """
    # Handle direct data references (existing code)
    if 'column' in node:
        return data[node['column']]
    
    # Handle references to other components (existing code)
    if 'ref' in node:
        # Existing reference handling code...
        pass
        
    # Handle market indicator references (new code)
    if 'market' in node:
        if not self._market_indicator_manager:
            raise ValueError("Market indicator manager is required for market indicator references")
            
        indicator_id = node['market']
        field = node.get('field', 'Close')
        
        # Build a Series aligned with data's index
        result = pd.Series(index=data.index)
        for date in data.index:
            value = self._market_indicator_manager.get_indicator_value(
                indicator_id, date, field
            )
            result.loc[date] = value
            
        return result
        
    # Continue with existing node evaluation logic...
```

### 2.5 Factory Class Extension

```python
# app/portfolio_factory_v2.py (update)

def get_portfolio(self, portfolio_code, data_loader):
    """Get configured PortfolioManager instance"""
    config = self._load_config(portfolio_code)
    market_indicator_manager = None
    risk_controller = None
    
    # --- 1. Create MarketIndicatorManager if needed ---
    if config.get('market_indicators'):
        market_indicator_manager = MarketIndicatorManager(data_loader)
        
        # Load specified market indicators
        for indicator_id in config['market_indicators']:
            market_indicator_manager.load_indicator(
                indicator_id, 
                config['start_date'],
                config.get('end_date')
            )
            
        # Register transformers if specified
        if config.get('indicator_transformers'):
            for transformer_config in config['indicator_transformers']:
                market_indicator_manager.register_transformer(
                    transformer_config['id'],
                    transformer_config['source'],
                    transformer_config['type'],
                    transformer_config.get('params')
                )
    
    # Continue with existing portfolio creation logic...
    
def _create_composite_strategy(self, config, market_indicator_manager):
    """
    Create composite strategy instance.
    
    Args:
        config: Strategy configuration
        market_indicator_manager: MarketIndicatorManager instance
        
    Returns:
        CompositeStrategy: Configured strategy instance
    """
    # Existing indicator instantiation code...
    
    # Create SignalEvaluator with market indicator manager
    signal_evaluator = SignalEvaluator(
        self.component_registry, 
        market_indicator_manager
    )
    
    # Continue with existing strategy creation logic...
```

## 3. Testing Guidelines

### 3.1 Unit Testing Market Indicator Manager

```python
# tests/core/components/test_market_indicators.py
import unittest
import pandas as pd
from unittest.mock import MagicMock, patch
from components.market_indicators import MarketIndicatorManager

class TestMarketIndicatorManager(unittest.TestCase):
    def setUp(self):
        self.mock_data_loader = MagicMock()
        self.manager = MarketIndicatorManager(self.mock_data_loader)
        
        # Create test data frame
        dates = pd.date_range('2020-01-01', periods=10)
        self.test_df = pd.DataFrame({
            'Open': range(10, 20),
            'High': range(15, 25),
            'Low': range(5, 15),
            'Close': range(12, 22),
            'Volume': range(100, 1000, 100)
        }, index=dates)
        
    def test_load_indicator(self):
        # Mock the data loader response
        self.mock_data_loader.load_yahoo_finance_data.return_value = self.test_df
        
        # Test loading a valid indicator
        result = self.manager.load_indicator('VIX', '2020-01-01', '2020-01-10')
        self.assertTrue(result)
        self.assertIn('VIX', self.manager.indicators_data)
        
        # Test loading an unknown indicator
        result = self.manager.load_indicator('UNKNOWN', '2020-01-01', '2020-01-10')
        self.assertFalse(result)
        
    def test_get_indicator_value(self):
        # Setup test data
        self.mock_data_loader.load_yahoo_finance_data.return_value = self.test_df
        self.manager.load_indicator('VIX', '2020-01-01', '2020-01-10')
        
        # Test getting a value for a date in range
        value = self.manager.get_indicator_value('VIX', '2020-01-05', 'Close')
        self.assertEqual(value, 16)
        
        # Test getting a value for a date not in range
        value = self.manager.get_indicator_value('VIX', '2020-01-15', 'Close')
        self.assertIsNone(value)
        
    def test_register_transformer(self):
        # Setup test data
        self.mock_data_loader.load_yahoo_finance_data.return_value = self.test_df
        self.manager.load_indicator('VIX', '2020-01-01', '2020-01-10')
        
        # Mock the registry
        mock_registry = MagicMock()
        mock_transformer = MagicMock()
        mock_transformer.return_value.transform.return_value = pd.Series([100, 200, 300])
        mock_registry.get_market_transformer.return_value = mock_transformer
        self.manager.registry = mock_registry
        
        # Test registering a transformer
        result = self.manager.register_transformer(
            'VIX_MA', 'VIX', 'MovingAverage', {'period': 5}
        )
        self.assertTrue(result)
        self.assertIn('VIX_MA', self.manager.transformers)
        self.assertIn('VIX_MA', self.manager.transformed_data)
```

### 3.2 Integration Testing

```python
# tests/integration/test_market_indicators_integration.py
import unittest
import os
import pandas as pd
from unittest.mock import patch
from components.market_indicators import MarketIndicatorManager
from components.signal_evaluator import SignalEvaluator
from components.registry import ComponentRegistry
from portfolios.portfolio_data_collector import PortfolioDataLoader

class TestMarketIndicatorsIntegration(unittest.TestCase):
    def setUp(self):
        self.data_loader = PortfolioDataLoader()
        self.registry = ComponentRegistry.get_instance()
        
        # Create a market indicator manager with test data
        self.manager = MarketIndicatorManager(self.data_loader)
        
        # Generate mock data for VIX
        dates = pd.date_range('2022-01-01', periods=30)
        vix_data = pd.DataFrame({
            'Open': [20 + i % 10 for i in range(30)],
            'High': [25 + i % 10 for i in range(30)],
            'Low': [18 + i % 10 for i in range(30)],
            'Close': [22 + i % 10 for i in range(30)],
            'Volume': [1000000 for _ in range(30)]
        }, index=dates)
        
        # Mock the data loader
        with patch.object(self.data_loader, 'load_yahoo_finance_data', return_value=vix_data):
            self.manager.load_indicator('VIX', '2022-01-01', '2022-01-30')
            
        # Register transformers
        self.manager.register_transformer(
            'VIX_MA5', 'VIX', 'MovingAverage', {'period': 5}
        )
        
        # Create a signal evaluator with the market indicator manager
        self.evaluator = SignalEvaluator(self.registry, self.manager)
        
        # Create test price data
        self.price_data = pd.DataFrame({
            'Open': [100 + i for i in range(30)],
            'High': [105 + i for i in range(30)],
            'Low': [95 + i for i in range(30)],
            'Close': [101 + i for i in range(30)],
            'Volume': [10000 for _ in range(30)]
        }, index=dates)
        
    def test_strategy_with_market_filter(self):
        # Define a simple strategy configuration with a market indicator filter
        strategy_config = {
            'indicators': [
                {'id': 'sma10', 'type': 'SMA', 'params': {'period': 10}}
            ],
            'signals': [
                {
                    'id': 'price_high', 
                    'type': 'GreaterThan',
                    'inputs': [{'ref': 'sma10'}, 110]
                },
                {
                    'id': 'vix_low',
                    'type': 'LessThan',
                    'inputs': [{'market': 'VIX'}, 25]
                },
                {
                    'id': 'buy_signal',
                    'type': 'And',
                    'inputs': [{'ref': 'price_high'}, {'ref': 'vix_low'}]
                }
            ],
            'outputs': {
                'buy_signal': 'buy_signal'
            }
        }
        
        # Evaluate the strategy
        signals = self.evaluator.evaluate_for_symbol('SPY', strategy_config, self.price_data)
        
        # Verify that signals are generated correctly based on both price and VIX
        self.assertIsNotNone(signals)
        self.assertEqual(len(signals), len(self.price_data))
```

## 4. Best Practices

1. **Error Handling**:
   - Always check for null/None values when retrieving market data
   - Handle date misalignment gracefully with appropriate defaults
   - Provide clear error messages when market data is unavailable

2. **Performance Considerations**:
   - Leverage the existing caching mechanisms in data loaders
   - Minimize redundant calculations by caching transformed results
   - Batch load market data at initialization when possible

3. **Testing**:
   - Create mock market data for testing that mimics real-world patterns
   - Test edge cases (missing data, extreme values, etc.)
   - Include integration tests that verify the full flow from configuration to trading signals

4. **Documentation**:
   - Document available market indicators in user guides
   - Provide clear examples of common usage patterns
   - Include sample strategies demonstrating market-aware trading logic

## 5. Sample Configurations

### Simple VIX Filter Example

```json
{
  "portfolio_id": "vix_filtered_strategy",
  "description": "Dual moving average with VIX filter",
  "symbols": ["SPY"],
  "start_date": "2020-01-01",
  "market_indicators": ["VIX"],
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        {"id": "sma10", "type": "SMA", "params": {"period": 10}},
        {"id": "sma30", "type": "SMA", "params": {"period": 30}}
      ],
      "signals": [
        {
          "id": "crossover",
          "type": "Crossover",
          "inputs": [{"ref": "sma10"}, {"ref": "sma30"}]
        },
        {
          "id": "crossunder",
          "type": "Crossunder",
          "inputs": [{"ref": "sma10"}, {"ref": "sma30"}]
        },
        {
          "id": "vix_low",
          "type": "LessThan",
          "inputs": [{"market": "VIX"}, 25]
        }
      ],
      "composite_signals": [
        {
          "id": "buy_signal",
          "type": "And",
          "inputs": [{"ref": "crossover"}, {"ref": "vix_low"}]
        }
      ],
      "outputs": {
        "buy_signal": "buy_signal",
        "sell_signal": "crossunder"
      }
    }
  }
}
```

### Advanced Market Regime Example

```json
{
  "portfolio_id": "market_regime_strategy",
  "description": "Strategy that adapts to market regime",
  "symbols": ["SPY"],
  "start_date": "2020-01-01",
  "market_indicators": ["VIX"],
  "indicator_transformers": [
    {"id": "VIX_MA20", "source": "VIX", "type": "MovingAverage", "params": {"period": 20}},
    {"id": "VIX_RANK", "source": "VIX", "type": "PercentileRank", "params": {"window": 252}}
  ],
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        {"id": "sma10", "type": "SMA", "params": {"period": 10}},
        {"id": "sma30", "type": "SMA", "params": {"period": 30}},
        {"id": "atr", "type": "ATR", "params": {"period": 14}}
      ],
      "signals": [
        {
          "id": "crossover",
          "type": "Crossover",
          "inputs": [{"ref": "sma10"}, {"ref": "sma30"}]
        },
        {
          "id": "crossunder",
          "type": "Crossunder",
          "inputs": [{"ref": "sma10"}, {"ref": "sma30"}]
        },
        {
          "id": "low_volatility",
          "type": "LessThan",
          "inputs": [{"market": "VIX_RANK"}, 50]
        },
        {
          "id": "volatility_falling",
          "type": "LessThan",
          "inputs": [{"market": "VIX"}, {"market": "VIX_MA20"}]
        }
      ],
      "composite_signals": [
        {
          "id": "aggressive_buy",
          "type": "And",
          "inputs": [{"ref": "crossover"}, {"ref": "low_volatility"}]
        },
        {
          "id": "defensive_buy",
          "type": "And",
          "inputs": [{"ref": "crossover"}, {"ref": "volatility_falling"}]
        },
        {
          "id": "buy_signal",
          "type": "Or",
          "inputs": [{"ref": "aggressive_buy"}, {"ref": "defensive_buy"}]
        }
      ],
      "outputs": {
        "buy_signal": "buy_signal",
        "sell_signal": "crossunder" 
      }
    }
  }
}
```
