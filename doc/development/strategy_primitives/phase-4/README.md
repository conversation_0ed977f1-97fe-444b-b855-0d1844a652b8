# Strategy Primitives Phase 4: Market Indicators and Transformers Integration

## Background and Objectives

In the previous three phases, we implemented the foundational architecture for strategy primitives, including indicator primitives and signal primitives, supporting their composition through declarative configuration. However, the current implementation primarily focuses on price data for individual securities, lacking awareness of the broader market environment.

Phase 4 aims to extend the existing strategy primitives system to reference and utilize external market indicators (such as VIX, market PE ratios, etc.) to enhance trading decisions. This will enable strategies to make trading decisions based on the broader market environment, thereby improving adaptability and risk management capabilities.

## Design Overview

This phase will implement the following core components:

1. **Market Indicator Manager (MarketIndicatorManager)**: Responsible for loading and providing market indicator data
2. **Market Transformers (MarketTransformer)**: Process market indicators with transformations (e.g., moving averages, percentile rankings)
3. **Extended Signal Evaluator (SignalEvaluator)**: Support referencing market indicators in signal calculations
4. **Modified Factory Class (PortfolioFactory)**: Support initialization and configuration of market indicator components

Implementation will be divided into two stages: Stage 1 implements the basic framework, while Stage 2 completes the signal evaluator integration and end-to-end testing.

## Stage 1: Basic Framework Implementation

### Task 1.1: Design and Implement Market Indicator Manager

**File**: `app/components/market_indicators.py`

**Requirements**:
- Manage market indicator data loading
- Provide interface for querying indicator values by date
- Support registration and application of indicator transformers

**Core API**:
```python
class MarketIndicatorManager:
    """
    Market indicator manager responsible for loading and providing market indicator data.
    """
    
    INDICATORS_MAP = {
        "VIX": {"symbol": "^VIX", "market": "US", "source": "yahoo"},
        # Other indicator mappings...
    }
    
    def __init__(self, data_loader):
        """Initialize the market indicator manager"""
        
    def load_indicator(self, indicator_id, start_date, end_date=None):
        """Load the specified market indicator data"""
        
    def register_transformer(self, transformed_id, source_indicator_id, transformer_type, params=None):
        """Register and apply indicator transformer"""
        
    def get_indicator_value(self, indicator_id, date, field='Close'):
        """Get indicator value for specified date"""
        
    def get_indicator_series(self, indicator_id, field='Close'):
        """Get complete time series for an indicator"""
```

**Implementation Details**:
- Use existing data loading mechanisms to retrieve indicator data
- Support date range operations and data merging
- Handle cases of unavailable data and date mismatches

### Task 1.2: Design and Implement Market Transformer Base Class and Common Transformers

**File**: `app/components/market_transformers.py`

**Requirements**:
- Define market transformer base class interface
- Implement common market transformers

**Core API**:
```python
class BaseMarketTransformer(BaseComponentPrimitive):
    """Market indicator transformer base class"""
    
    def transform(self, data):
        """Transform input data"""
        raise NotImplementedError("Transformers must implement transform method")
        
class MovingAverageTransformer(BaseMarketTransformer):
    """Moving average transformer"""
    
class PercentileRankTransformer(BaseMarketTransformer):
    """Percentile rank transformer"""
    
class RelativeStrengthTransformer(BaseMarketTransformer):
    """Relative strength transformer"""
```

**Implementation Details**:
- All transformers should inherit from BaseMarketTransformer base class
- Support parameterized configuration
- Ensure handling of edge cases (such as insufficient data)
- Provide detailed documentation and type hints

### Task 1.3: Extend Component Registry to Support Market Transformers

**File**: `app/components/registry.py`

**Requirements**:
- Add market transformer registration and retrieval methods
- Ensure compatibility with existing primitive types

**Modifications**:
- Add `market_transformers` dictionary to store transformer classes
- Implement the `get_market_transformer` method
- Add transformer scanning code in `_initialize_registry`
- Maintain backward compatibility

### Task 1.4: Write Unit Tests

**Files**: 
- `tests/core/components/test_market_indicators.py`
- `tests/core/components/test_market_transformers.py`

**Test Focus**:
- Test indicator loading mechanisms
- Test various transformer functionalities
- Test error handling and edge cases
- Test integration with the component registry

## Stage 2: Evaluator Integration and End-to-End Testing

### Task 2.1: Extend Signal Evaluator to Support Market Indicator References

**File**: `app/components/signal_evaluator.py`

**Requirements**:
- Modify initialization method to support market indicator manager
- Extend node evaluation logic to support market indicator references

**Modifications**:
```python
def __init__(self, registry, market_indicator_manager=None):
    """Initialize the signal evaluator"""
    self._registry = registry
    self._market_indicator_manager = market_indicator_manager

def _evaluate_node(self, node, data, component_source=None):
    """Evaluate configuration node"""
    
    # ... existing processing logic ...
    
    # Handle market indicator references
    if 'market' in node:
        if not self._market_indicator_manager:
            raise ValueError("Market indicator manager required for market indicator references")
            
        indicator_id = node['market']
        field = node.get('field', 'Close')
        
        # Build Series matching data index
        result = pd.Series(index=data.index)
        for date in data.index:
            value = self._market_indicator_manager.get_indicator_value(
                indicator_id, date, field
            )
            result.loc[date] = value
            
        return result
```

### Task 2.2: Extend Factory Class to Support Market Indicator Components

**File**: `app/portfolio_factory_v2.py`

**Requirements**:
- Create and configure market indicator manager
- Handle market indicator and transformer configuration
- Inject market indicator manager into signal evaluator

**Modifications**:
- Add market indicator manager creation logic in the `get_portfolio` method
- Process `market_indicators` and `indicator_transformers` sections in the configuration
- Modify the `_create_composite_strategy` method to pass the market indicator manager

### Task 2.3: Write Integration Tests

**Files**:
- `tests/integration/test_market_indicators_integration.py`
- `tests/canary/test_market_filtered_strategy.py`

**Test Focus**:
- Test complete market indicator-driven strategies
- Validate buy/sell filtering based on market conditions
- Ensure existing tests and functionality are not affected

### Task 2.4: Create Example Configurations and Documentation

**Files**:
- `configs/examples/market_filtered_strategy.json`
- `doc/guides/primitives_guide/07_market_indicators.md`

**Key Points**:
- Provide VIX filtering strategy examples
- Explain how to reference and transform market indicators
- Provide common patterns and best practices

## Example Configuration

```json
{
  "portfolio_id": "ma_vix_filter",
  "description": "Dual moving average strategy, only allowing buys when VIX is below 30",
  "symbols": ["SPY"],
  "start_date": "2020-01-01",
  "market_indicators": ["VIX"],
  "indicator_transformers": [
    {"id": "VIX_SMA20", "source": "VIX", "type": "MovingAverage", "params": {"period": 20}}
  ],
  "strategy_definition": {
    "trade_strategy": {
      "indicators": [
        {"id": "shortMA", "type": "SMA", "params": {"period": 10}},
        {"id": "longMA", "type": "SMA", "params": {"period": 30}}
      ],
      "signals": [
        {
          "id": "ma_cross",
          "type": "Crossover",
          "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]
        },
        {
          "id": "ma_cross_under",
          "type": "Crossunder", 
          "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]
        },
        {
          "id": "vix_filter",
          "type": "LessThan",
          "inputs": [{"market": "VIX"}, 30]
        }
      ],
      "composite_signals": [
        {
          "id": "final_buy",
          "type": "And",
          "inputs": [{"ref": "ma_cross"}, {"ref": "vix_filter"}]
        }
      ],
      "outputs": {
        "buy_signal": "final_buy",
        "sell_signal": "ma_cross_under"
      }
    }
  }
}
```

## Completion Criteria

1. All unit tests and integration tests pass
2. Example strategies can be successfully backtested and produce expected trading signals
3. Documentation is complete, including API and usage examples
4. Code adheres to the project's Python coding style guidelines and type annotation requirements
5. Functionality is fully compatible with existing components without breaking existing features

## Estimated Workload

- Stage 1: 1.5 person-days
- Stage 2: 1.5 person-days
- Total: 3 person-days
