# Strategy Primitives Phase 4: Market Indicators and Transformers Integration Tasks

## Overview
Implement market indicator integration functionality to enable strategies to make trading decisions based on external market environments (such as VIX, market PE, etc.), enhancing strategy flexibility and risk management capabilities.

## Tasks Checklist

### Stage 0: Preparation
- [x] Create task tracking document
- [x] Create `doc/development/strategy_primitives/phase-4/README.md` high-level design document
- [x] Create `doc/development/strategy_primitives/phase-4/Tasks.md` task list
- [x] Create `doc/development/strategy_primitives/phase-4/Implementation-Guide.md` implementation guide
- [x] Confirm implementation plan with team

### Stage 1: Market Indicator Manager Implementation
- [x] Implement market indicator manager
  - [x] Create `app/components/market_indicators.py`
  - [x] Implement indicator mapping and configuration structure
  - [x] Implement data loading methods
  - [x] Implement indicator query methods
  - [x] Implement transformer registration and application
- [x] Implement market transformer base class and common transformers
  - [x] Create `app/components/market_transformers.py`
  - [x] Implement `BaseMarketTransformer` base class
  - [x] Implement `MovingAverageTransformer` transformer
  - [x] Implement `PercentileRankTransformer` transformer
  - [x] Implement `RelativeStrengthTransformer` transformer
  - [x] Implement `ZScoreTransformer` transformer (bonus)
- [x] Extend component registry
  - [x] Modify `app/components/registry.py`
  - [x] Add market transformer registration and retrieval methods
  - [x] Update `_initialize_registry` method
- [x] Write unit tests
  - [x] Create `tests/core/components/test_market_indicators.py`
  - [x] Create `tests/core/components/test_market_transformers.py`
  - [x] Test registry extension functionality
- [x] Run unit tests and validate
- [x] Commit: "feat(market-indicators): Implement market indicator manager and transformer base classes"

### Stage 2: Signal Evaluator Extension
- [x] Extend signal evaluator
  - [x] Modify `app/components/signal_evaluator.py` initialization method
  - [x] Extend `_evaluate_node` method to support market indicator references
  - [x] Implement market indicator reference parsing and processing
- [x] Extend factory class
  - [x] Modify `app/portfolio_factory_v2.py`
  - [x] Add market indicator manager creation process
  - [x] Add logic to read market indicator list from configuration
  - [x] Implement transformer configuration parsing
  - [x] Modify `_create_composite_strategy` method to inject dependencies
- [x] Write unit tests
  - [x] Create `tests/core/components/test_signal_evaluator_market.py`
  - [x] Test market indicator references in signal evaluation
- [x] Run unit tests and validate
- [x] Commit: "feat(signal-evaluator): Extend signal evaluator to support market indicator references"

### Stage 3: Integration Testing and Example Strategies
- [x] Implement example strategies
  - [x] Create VIX filtering strategy example (market volatility filter)
  - [x] Create market trend following strategy example (market trend overlay)
- [x] Add example configurations to existing config file
  - [x] Add `myinvestpilot_market_filtered` portfolio to `app/config/portfolio_config_primitive.json`
  - [x] Add `myinvestpilot_market_trend` portfolio to `app/config/portfolio_config_primitive.json`
- [x] Implement integration tests
  - [x] Add market indicator test cases to `app/tests/integration/test_portfolio_backtests.py`
  - [x] Add new portfolios to test parametrization
  - [x] Create baseline data in `app/tests/baseline/[portfolio_id]/portfolio_status.csv`
- [x] Create canary test for market indicator strategies
  - [x] Add simple test case to `app/tests/canary/test_canary.py`
- [x] Write usage documentation
  - [x] Create `doc/guides/primitives_guide/07_market_indicators.md`
  - [x] Update `doc/guides/primitives_guide/README.md`
- [x] Run integration tests with `./run_integration_tests.sh` from app directory
- [x] Commit: "feat(examples): Add market indicator strategy examples and integration tests"

### Quality Assurance
- [ ] Verify backward compatibility
- [ ] Check code coverage (target >80%)
- [ ] Check error handling and edge cases
- [ ] Update documentation and comments
- [ ] Performance testing (compare with strategies not using market indicators)

## Notes
- [ ] Each stage requires successful execution of `run_unit_tests.sh`
- [ ] Maintain backward compatibility
- [ ] Document any necessary changes to existing components
- [ ] Keep commit messages clear and descriptive

## Status
- Start Date: 2025-04-26
- Current Stage: Stage 3
- Completed: 3/3 stages
