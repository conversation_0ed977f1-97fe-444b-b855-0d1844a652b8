# Architecture V4

## Overview

Architecture V4 represents a significant evolution of the investStrategyService system, incorporating both the primitive engine system and unified cash flow management as core components. This document outlines the updated architecture, highlighting the integration of the primitive engine, cash flow management system, and their relationships with existing components.

## System Architecture

The architecture of the system is based on the following components:

```mermaid
graph TD
    subgraph Core["Core System"]
        PM["PortfolioManager"] --> PF["PortfolioFactory"]
        PF --> TS["Traditional Strategies"]
        PF --> PS["Primitive Strategies"]
        PM --> DL["DataLoader"]
        PM --> BT["Backtrader"]
        PM --> CFO["CashFlowAdjustedNAVObserver"]
        PM --> CRA["CashFlowReturnAnalyzer"]
    end

    subgraph PrimitiveEngine["Primitive Engine"]
        PS --> CR["ComponentRegistry"]
        PS --> SE["SignalEvaluator"]
        CR --> IP["Indicator Primitives"]
        CR --> SP["Signal Primitives"]
        SE --> MIM["MarketIndicatorManager"]
    end

    subgraph CashFlowManagement["Cash Flow Management"]
        CFI["CashFlowInterface"] --> CFM["CashFlowManager"]
        CFI --> ACFS["AdvancedCashFlowStrategy"]
        CFT["CashFlowTypes"] --> CFI
        CFM --> CFH["Cash Flow History"]
        CFM --> RC["Risk Controls"]
    end

    subgraph DataSources["Data Sources"]
        DL --> OHLC["OHLC Data"]
        MIM --> MI["Market Indicators"]
    end

    subgraph Strategies["Strategy Types"]
        TS --> TS1["ChandelierExitMAStrategy"]
        TS --> TS2["DualMovingAverageStrategy"]
        PS --> PS1["Declarative Strategy Configs"]
    end

    subgraph CapitalStrategies["Capital Strategies"]
        PM --> CS["Capital Strategy"]
        CS --> CS1["PercentCapitalStrategy"]
        CS --> CS2["SimplePercentCapitalStrategy"]
        CS --> CS3["FixedInvestmentStrategy"]
        CS3 --> CFI
    end

    subgraph PerformanceAnalysis["Performance Analysis"]
        CFO --> NAV["Time-Weighted NAV"]
        CRA --> XIRR["XIRR Calculation"]
        NAV --> PDC["PortfolioDataCollector"]
        XIRR --> PDC
    end
```

## Component Hierarchy

The system follows a hierarchical structure with the following main components:

```
investStrategyService
├── PortfolioManager (Core manager)
├── PortfolioFactory (Portfolio creation)
│   ├── Traditional strategy path
│   └── Primitive strategy path
├── Strategy implementations
│   ├── Traditional strategies
│   │   ├── ChandelierExitMAStrategy
│   │   ├── DualMovingAverageStrategy
│   │   └── ...
│   └── Primitive strategy system
│       ├── ComponentRegistry
│       ├── SignalEvaluator
│       ├── Indicator primitives
│       └── Signal primitives
├── Capital strategies
│   ├── PercentCapitalStrategy
│   ├── SimplePercentCapitalStrategy
│   └── FixedInvestmentStrategy (with cash flow support)
├── Cash flow management (new)
│   ├── CashFlowInterface (unified interface)
│   ├── CashFlowManager (processing & risk controls)
│   ├── AdvancedCashFlowStrategy (base implementation)
│   ├── CashFlowTypes (type constants)
│   └── Example implementations
│       ├── DynamicInvestmentStrategy
│       └── StopLossStrategy
├── Performance analysis (enhanced)
│   ├── CashFlowAdjustedNAVObserver (time-weighted NAV)
│   ├── CashFlowReturnAnalyzer (XIRR calculation)
│   └── PortfolioDataCollector (unified data collection)
└── Data management
    ├── DataLoader
    ├── MarketIndicatorManager
    └── Data source adapters
```

## Cash Flow Management Architecture

The cash flow management system is a core component that provides unified handling of all types of capital flows:

```mermaid
graph TD
    subgraph CashFlowSystem["Cash Flow Management System"]
        CFI["CashFlowInterface"] --> CFM["CashFlowManager"]
        CFI --> ACFS["AdvancedCashFlowStrategy"]
        CFT["CashFlowTypes"] --> CFI

        subgraph CashFlowTypes["Cash Flow Types"]
            CFT --> AI["Annual Investment"]
            CFT --> MI["Monthly Investment"]
            CFT --> DR["Dividend Reinvestment"]
            CFT --> SL["Stop Loss Withdrawal"]
            CFT --> RA["Rebalancing Adjustment"]
            CFT --> EW["Emergency Withdrawal"]
            CFT --> BI["Bonus Investment"]
            CFT --> CU["Custom Types"]
        end

        subgraph Processing["Processing Pipeline"]
            CFM --> RC["Risk Controls"]
            CFM --> CFH["Cash Flow History"]
            CFM --> VS["Validation & Sanitization"]
            RC --> LM["Limit Management"]
            RC --> FM["Frequency Management"]
        end

        subgraph Examples["Example Implementations"]
            ACFS --> DIS["DynamicInvestmentStrategy"]
            ACFS --> SLS["StopLossStrategy"]
            ACFS --> CCS["Custom Cash Flow Strategies"]
        end
    end

    subgraph Integration["System Integration"]
        CFI --> FIS["FixedInvestmentStrategy"]
        CFM --> BSA["BacktraderStrategyAdapter"]
        BSA --> CFO["CashFlowAdjustedNAVObserver"]
        CFM --> CRA["CashFlowReturnAnalyzer"]
    end
```

### Key Components

1. **CashFlowInterface**: Abstract interface defining the contract for all cash flow strategies
2. **CashFlowManager**: Central processor for cash flows with risk controls and history tracking
3. **AdvancedCashFlowStrategy**: Base implementation providing common functionality
4. **CashFlowTypes**: Standardized constants for all supported cash flow types
5. **Risk Controls**: Built-in safeguards for amount limits, frequency controls, and validation

## Primitive Engine Architecture

The primitive engine is a core component that enables declarative strategy definition through composable primitives:

```mermaid
graph TD
    subgraph PrimitiveEngine["Primitive Engine"]
        CR["ComponentRegistry"] --> IP["Indicator Primitives"]
        CR --> SP["Signal Primitives"]
        SE["SignalEvaluator"] --> CR
        SE --> MIM["MarketIndicatorManager"]
        
        subgraph IndicatorPrimitives["Indicator Primitives"]
            IP --> IP1["Moving Averages (SMA, EMA, ...)"]
            IP --> IP2["Volatility (ATR, BollingerBands, ...)"]
            IP --> IP3["Momentum (RSI, MACD, ...)"]
            IP --> IP4["Other Indicators"]
        end
        
        subgraph SignalPrimitives["Signal Primitives"]
            SP --> SP1["Comparison (GreaterThan, LessThan, ...)"]
            SP --> SP2["Crossover (Crossover, Crossunder, ...)"]
            SP --> SP3["Logic (And, Or, Not, ...)"]
            SP --> SP4["Other Signals"]
        end
    end
    
    MIM --> MI["Market Indicators"]
    SE --> SS["Strategy State Machine"]
```

### Key Components

1. **ComponentRegistry**: Central registry for all primitive components, enabling dynamic discovery and instantiation
2. **SignalEvaluator**: Evaluates the component tree to generate trading signals
3. **BaseIndicatorPrimitive**: Base class for all indicator primitives
4. **BaseSignalPrimitive**: Base class for all signal primitives
5. **MarketIndicatorManager**: Manages external market indicators for strategy use

## Data Flow

The data flow in the system has been enhanced to support traditional strategies, primitive strategies, and cash flow management:

```mermaid
sequenceDiagram
    participant PF as PortfolioFactory
    participant DL as DataLoader
    participant MIM as MarketIndicatorManager
    participant CR as ComponentRegistry
    participant SE as SignalEvaluator
    participant BT as Backtrader
    
    PF->>PF: load_portfolio_config()
    PF->>DL: get_market_data()
    DL-->>PF: OHLCV data
    
    alt Traditional Strategy
        PF->>PF: create_traditional_strategy()
        PF->>BT: run_backtest()
    else Primitive Strategy
        PF->>CR: get_components()
        PF->>MIM: load_market_indicators()
        MIM-->>SE: market indicator data
        PF->>SE: evaluate_strategy()
        SE->>SE: evaluate_component_tree()
        SE-->>PF: trading signals
        PF->>BT: run_backtest()
    end
    
    BT-->>PF: backtest results
```

## Server Mode Sequence

The server mode operation has been updated to support primitive strategies:

```mermaid
sequenceDiagram
    participant Queue as Message Queue
    participant EP as EventPoller
    participant PF as PortfolioFactory
    participant TR as TaskRunner
    participant PM as PortfolioManager
    participant DL as DataLoader
    participant SE as SignalEvaluator
    participant BT as Backtrader
    participant S3 as S3 Storage

    Note over Queue: Redis or Kafka
    Queue->>EP: Send portfolio update message
    EP->>EP: handle_message()
    EP->>PF: load_portfolio_config_from_message()
    EP->>TR: handle_message()
    TR->>TR: run()
    TR->>PF: get_portfolio()
    
    alt Traditional Strategy
        PF->>PM: create PortfolioManager with traditional strategy
    else Primitive Strategy
        PF->>SE: evaluate_strategy()
        PF->>PM: create PortfolioManager with primitive strategy
    end
    
    TR->>PM: execute_trades()
    PM->>DL: get_instance()
    PM->>BT: Set up and run backtest
    BT-->>PM: Return backtest results
    PM->>PM: post_process_results()
    PM->>PM: export_to_db()
    PM->>PM: generate_portfolio_today_notification()
    PM->>PM: export_email_html()
    PM->>PM: plot_portfolio_image()
    PM->>PM: sync_to_s3()
    PM->>S3: Upload files
    TR-->>EP: Task completed
```

## Primitive Strategy Configuration

Primitive strategies are defined using a declarative JSON configuration:

```json
{
  "trade_strategy": {
    "indicators": [
      {
        "id": "fast_ma",
        "type": "SMA",
        "params": { "window": 10 }
      },
      {
        "id": "slow_ma",
        "type": "SMA",
        "params": { "window": 30 }
      }
    ],
    "signals": [
      {
        "id": "buy_condition",
        "type": "Crossover",
        "inputs": [
          { "id": "fast_ma" },
          { "id": "slow_ma" }
        ]
      },
      {
        "id": "sell_condition",
        "type": "Crossunder",
        "inputs": [
          { "id": "fast_ma" },
          { "id": "slow_ma" }
        ]
      }
    ],
    "outputs": {
      "buy_signal": "buy_condition",
      "sell_signal": "sell_condition"
    }
  },
  "capital_strategy": {
    "name": "PercentCapitalStrategy",
    "params": {
      "initial_capital": 100000,
      "percents": 20
    }
  }
}
```

## Cash Flow Strategy Configuration

Cash flow strategies can be configured using the unified cash flow interface:

```json
{
  "trade_strategy": {
    "indicators": [
      {
        "id": "price_ma",
        "type": "SMA",
        "params": { "window": 20 }
      }
    ],
    "signals": [
      {
        "id": "buy_condition",
        "type": "GreaterThan",
        "inputs": [
          { "source": "close" },
          { "id": "price_ma" }
        ]
      }
    ],
    "outputs": {
      "buy_signal": "buy_condition"
    }
  },
  "capital_strategy": {
    "name": "FixedInvestmentStrategy",
    "params": {
      "initial_capital": 100000,
      "investment_amount": 1000,
      "investment_frequency": "m",
      "percents": 95
    }
  },
  "cash_flow_strategy": {
    "name": "DynamicInvestmentStrategy",
    "params": {
      "monthly_investment": 1000,
      "dip_buy_threshold": -0.05,
      "dip_buy_multiplier": 2.0,
      "max_monthly_investment": 5000
    }
  }
}
```

## State Machine Logic

The primitive engine implements a strict state machine to ensure consistent trading signals:

```mermaid
stateDiagram-v2
    [*] --> EMPTY
    EMPTY --> BUY: Buy Signal
    BUY --> HOLD: Next Bar
    HOLD --> SELL: Sell Signal
    SELL --> EMPTY: Next Bar
    HOLD --> HOLD: No Signal
    EMPTY --> EMPTY: No Signal
```

## Performance Analysis Enhancement

The V4 architecture includes enhanced performance analysis capabilities specifically designed for cash flow strategies:

### Time-Weighted Return Calculation

```mermaid
graph LR
    subgraph NAVCalculation["NAV Calculation Pipeline"]
        CF["Cash Flows"] --> CFO["CashFlowAdjustedNAVObserver"]
        PV["Portfolio Value"] --> CFO
        CFO --> TWR["Time-Weighted Return"]
        TWR --> NAV["Adjusted NAV"]
    end

    subgraph XIRRCalculation["XIRR Calculation Pipeline"]
        CF --> CRA["CashFlowReturnAnalyzer"]
        FV["Final Value"] --> CRA
        CRA --> XIRR["Internal Rate of Return"]
    end

    subgraph Results["Performance Metrics"]
        NAV --> SM["Strategy Performance"]
        XIRR --> IR["Investor Returns"]
        SM --> CR["Comparative Reports"]
        IR --> CR
    end
```

### Key Features

1. **Dual Return Calculation**:
   - **Time-Weighted Return**: Measures strategy performance independent of cash flows
   - **XIRR (Internal Rate of Return)**: Measures actual investor returns considering cash flow timing

2. **Automatic NAV Adjustment**:
   - Real-time adjustment for cash inflows and outflows
   - Maintains accurate performance tracking across different investment patterns

3. **Cash Flow History**:
   - Complete audit trail of all cash flow events
   - Detailed categorization by flow type and timing

## Benefits of V4 Architecture

1. **Declarative Strategy Definition**: Strategies can be defined using JSON configuration without writing code
2. **Component Reusability**: Primitives can be reused across different strategies
3. **Unified Cash Flow Management**: All cash flow types handled through a single, consistent interface
4. **Accurate Performance Measurement**: Dual return calculation provides both strategy and investor perspectives
5. **Easier Testing**: Individual primitives and cash flow strategies can be tested in isolation
6. **Rapid Prototyping**: New strategies can be created by combining existing primitives and cash flow patterns
7. **Maintainability**: Clear separation between strategy logic, cash flow management, and implementation details
8. **Extensibility**: New primitives and cash flow types can be added without affecting existing strategies
9. **Risk Management**: Built-in controls for cash flow limits, frequency, and validation

## Conclusion

Architecture V4 represents a significant advancement in the system's capabilities, introducing two major enhancements:

1. **Primitive Engine System**: A flexible and powerful engine that enables declarative strategy definition through composable primitives
2. **Unified Cash Flow Management**: A comprehensive system for handling all types of capital flows with accurate performance measurement

Key achievements of V4:

- **Backward Compatibility**: All existing traditional strategies continue to work without modification
- **Enhanced Flexibility**: New strategies can be created through JSON configuration or custom cash flow implementations
- **Accurate Analytics**: Dual return calculation provides both strategy performance and investor return perspectives
- **Risk Management**: Built-in controls and validation for all cash flow operations
- **Extensibility**: Easy addition of new primitives and cash flow types without system-wide changes

This architecture positions the system for future growth while maintaining the reliability and performance that users expect from the investStrategyService platform.
