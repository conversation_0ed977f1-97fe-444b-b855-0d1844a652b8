# Architecture V4

## Overview

Architecture V4 represents a significant evolution of the investStrategyService system, incorporating the primitive engine system as a core component. This document outlines the updated architecture, highlighting the integration of the primitive engine and its relationship with existing components.

## System Architecture

The architecture of the system is based on the following components:

```mermaid
graph TD
    subgraph Core["Core System"]
        PM["PortfolioManager"] --> PF["PortfolioFactory"]
        PF --> TS["Traditional Strategies"]
        PF --> PS["Primitive Strategies"]
        PM --> DL["DataLoader"]
        PM --> BT["Backtrader"]
    end
    
    subgraph PrimitiveEngine["Primitive Engine"]
        PS --> CR["ComponentRegistry"]
        PS --> SE["SignalEvaluator"]
        CR --> IP["Indicator Primitives"]
        CR --> SP["Signal Primitives"]
        SE --> MIM["MarketIndicatorManager"]
    end
    
    subgraph DataSources["Data Sources"]
        DL --> OHLC["OHLC Data"]
        MIM --> MI["Market Indicators"]
    end
    
    subgraph Strategies["Strategy Types"]
        TS --> TS1["ChandelierExitMAStrategy"]
        TS --> TS2["DualMovingAverageStrategy"]
        PS --> PS1["Declarative Strategy Configs"]
    end
    
    subgraph CapitalStrategies["Capital Strategies"]
        PM --> CS["Capital Strategy"]
        CS --> CS1["PercentCapitalStrategy"]
        CS --> CS2["SimplePercentCapitalStrategy"]
        CS --> CS3["FixedInvestmentStrategy"]
    end
```

## Component Hierarchy

The system follows a hierarchical structure with the following main components:

```
investStrategyService
├── PortfolioManager (Core manager)
├── PortfolioFactory (Portfolio creation)
│   ├── Traditional strategy path
│   └── Primitive strategy path (new)
├── Strategy implementations
│   ├── Traditional strategies
│   │   ├── ChandelierExitMAStrategy
│   │   ├── DualMovingAverageStrategy
│   │   └── ...
│   └── Primitive strategy system (new)
│       ├── ComponentRegistry
│       ├── SignalEvaluator
│       ├── Indicator primitives
│       └── Signal primitives
├── Capital strategies
│   ├── PercentCapitalStrategy
│   ├── SimplePercentCapitalStrategy
│   └── FixedInvestmentStrategy
└── Data management
    ├── DataLoader
    ├── MarketIndicatorManager (new)
    └── Data source adapters
```

## Primitive Engine Architecture

The primitive engine is a new core component that enables declarative strategy definition through composable primitives:

```mermaid
graph TD
    subgraph PrimitiveEngine["Primitive Engine"]
        CR["ComponentRegistry"] --> IP["Indicator Primitives"]
        CR --> SP["Signal Primitives"]
        SE["SignalEvaluator"] --> CR
        SE --> MIM["MarketIndicatorManager"]
        
        subgraph IndicatorPrimitives["Indicator Primitives"]
            IP --> IP1["Moving Averages (SMA, EMA, ...)"]
            IP --> IP2["Volatility (ATR, BollingerBands, ...)"]
            IP --> IP3["Momentum (RSI, MACD, ...)"]
            IP --> IP4["Other Indicators"]
        end
        
        subgraph SignalPrimitives["Signal Primitives"]
            SP --> SP1["Comparison (GreaterThan, LessThan, ...)"]
            SP --> SP2["Crossover (Crossover, Crossunder, ...)"]
            SP --> SP3["Logic (And, Or, Not, ...)"]
            SP --> SP4["Other Signals"]
        end
    end
    
    MIM --> MI["Market Indicators"]
    SE --> SS["Strategy State Machine"]
```

### Key Components

1. **ComponentRegistry**: Central registry for all primitive components, enabling dynamic discovery and instantiation
2. **SignalEvaluator**: Evaluates the component tree to generate trading signals
3. **BaseIndicatorPrimitive**: Base class for all indicator primitives
4. **BaseSignalPrimitive**: Base class for all signal primitives
5. **MarketIndicatorManager**: Manages external market indicators for strategy use

## Data Flow

The data flow in the system has been enhanced to support both traditional and primitive strategies:

```mermaid
sequenceDiagram
    participant PF as PortfolioFactory
    participant DL as DataLoader
    participant MIM as MarketIndicatorManager
    participant CR as ComponentRegistry
    participant SE as SignalEvaluator
    participant BT as Backtrader
    
    PF->>PF: load_portfolio_config()
    PF->>DL: get_market_data()
    DL-->>PF: OHLCV data
    
    alt Traditional Strategy
        PF->>PF: create_traditional_strategy()
        PF->>BT: run_backtest()
    else Primitive Strategy
        PF->>CR: get_components()
        PF->>MIM: load_market_indicators()
        MIM-->>SE: market indicator data
        PF->>SE: evaluate_strategy()
        SE->>SE: evaluate_component_tree()
        SE-->>PF: trading signals
        PF->>BT: run_backtest()
    end
    
    BT-->>PF: backtest results
```

## Server Mode Sequence

The server mode operation has been updated to support primitive strategies:

```mermaid
sequenceDiagram
    participant Queue as Message Queue
    participant EP as EventPoller
    participant PF as PortfolioFactory
    participant TR as TaskRunner
    participant PM as PortfolioManager
    participant DL as DataLoader
    participant SE as SignalEvaluator
    participant BT as Backtrader
    participant S3 as S3 Storage

    Note over Queue: Redis or Kafka
    Queue->>EP: Send portfolio update message
    EP->>EP: handle_message()
    EP->>PF: load_portfolio_config_from_message()
    EP->>TR: handle_message()
    TR->>TR: run()
    TR->>PF: get_portfolio()
    
    alt Traditional Strategy
        PF->>PM: create PortfolioManager with traditional strategy
    else Primitive Strategy
        PF->>SE: evaluate_strategy()
        PF->>PM: create PortfolioManager with primitive strategy
    end
    
    TR->>PM: execute_trades()
    PM->>DL: get_instance()
    PM->>BT: Set up and run backtest
    BT-->>PM: Return backtest results
    PM->>PM: post_process_results()
    PM->>PM: export_to_db()
    PM->>PM: generate_portfolio_today_notification()
    PM->>PM: export_email_html()
    PM->>PM: plot_portfolio_image()
    PM->>PM: sync_to_s3()
    PM->>S3: Upload files
    TR-->>EP: Task completed
```

## Primitive Strategy Configuration

Primitive strategies are defined using a declarative JSON configuration:

```json
{
  "trade_strategy": {
    "indicators": [
      {
        "id": "fast_ma",
        "type": "SMA",
        "params": { "window": 10 }
      },
      {
        "id": "slow_ma",
        "type": "SMA",
        "params": { "window": 30 }
      }
    ],
    "signals": [
      {
        "id": "buy_condition",
        "type": "Crossover",
        "inputs": [
          { "id": "fast_ma" },
          { "id": "slow_ma" }
        ]
      },
      {
        "id": "sell_condition",
        "type": "Crossunder",
        "inputs": [
          { "id": "fast_ma" },
          { "id": "slow_ma" }
        ]
      }
    ],
    "outputs": {
      "buy_signal": "buy_condition",
      "sell_signal": "sell_condition"
    }
  },
  "capital_strategy": {
    "name": "PercentCapitalStrategy",
    "params": {
      "initial_capital": 100000,
      "percents": 20
    }
  }
}
```

## State Machine Logic

The primitive engine implements a strict state machine to ensure consistent trading signals:

```mermaid
stateDiagram-v2
    [*] --> EMPTY
    EMPTY --> BUY: Buy Signal
    BUY --> HOLD: Next Bar
    HOLD --> SELL: Sell Signal
    SELL --> EMPTY: Next Bar
    HOLD --> HOLD: No Signal
    EMPTY --> EMPTY: No Signal
```

## Conclusion

Architecture V4 represents a significant advancement in the system's capabilities, introducing a flexible and powerful primitive engine that enables declarative strategy definition. This architecture maintains backward compatibility with traditional strategies while opening up new possibilities for strategy composition and reuse.
