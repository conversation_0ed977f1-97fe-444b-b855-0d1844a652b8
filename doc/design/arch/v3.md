# Architecture

## Overview

The architecture of the system is based on the following components:

Sequence Diagrams(Server Mode)

```mermaid
sequenceDiagram
    participant Queue as Message Queue
    participant EP as EventPoller
    participant P<PERSON> as PortfolioFactory
    participant TR as TaskRunner
    participant PM as PortfolioManager
    participant DL as PortfolioDataLoader
    participant TSC as TradeSignalCollector
    participant DC as DataCollector
    participant BT as Backtrader
    participant S3 as S3 Storage

    Note over Queue: Redis or Kafka
    Queue->>EP: Send portfolio update message
    EP->>EP: handle_message()
    EP->>PF: load_portfolio_config_from_message()
    EP->>TR: handle_message()
    TR->>TR: run()
    TR->>PF: get_portfolio()
    PF->>PM: create PortfolioManager
    TR->>PM: execute_trades()
    PM->>DL: get_instance()
    PM->>TSC: collect_data()
    TSC->>DL: get_qfq_close_price()
    DL-->>TSC: Return price data
    PM->>BT: Set up and run backtest
    BT-->>PM: Return backtest results
    PM->>DC: collect_data()
    PM->>PM: post_process_results()
    PM->>PM: export_to_db()
    PM->>PM: generate_portfolio_today_notification()
    PM->>PM: export_email_html()
    PM->>PM: plot_portfolio_image()
    PM->>PM: sync_to_s3()
    PM->>S3: Upload files
    TR-->>EP: Task completed
```

The EventPoller's polling interval is managed by `TimeUtils.get_polling_interval` and varies based on time of day:

- During working hours (06:00-23:59 UTC+8): Polls every 5 seconds
- During non-working hours (00:00-05:59 UTC+8): Polls every 10 minutes

This adaptive polling strategy helps reduce system load during off-hours while maintaining responsiveness during peak times.

## Job Message Structure

```json
{
  "job_id": "12345678-1234-5678-1234-567890abcdef",
  "timestamp": "2023-08-12T10:00:00Z",
  "action": "create_or_update",
  "portfolio_config": {
    "name": "美股1号",
    "code": "myinvestpilot_us_1",
    "description": "基于特定止损与均线策略的美股杠杆ETF组合",
    "strategy": {
      "name": "ChandelierExitMAStrategy",
      "params": {
        "n_atr": 60,
        "atr_multiplier": 4,
        "n_ma": 250
      }
    },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": {
        "initial_capital": 100000,
        "percents": 20
      }
    },
    "symbols": [
      {"symbol": "FNGU", "name": "FANG+ Index 3x Bull"},
      {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"},
      {"symbol": "TECL", "name": "S&P Technology 3x Bull"},
      {"symbol": "SOXL", "name": "Semiconductor 3x Bull"},
      {"symbol": "TNA", "name": "Russell 2000 3x Bull"},
      {"symbol": "CONL", "name": "COIN 2x Bull"},
      {"symbol": "LABU", "name": "S&P Biotech 3x Bull"},
      {"symbol": "FAS", "name": "Financial Sector 3x Bull"},
      {"symbol": "EDC", "name": "Emerging Markets 3x Bull"},
      {"symbol": "TMF", "name": "Long-term Treasury 3x Bull"}
    ],
    "start_date": "2018-01-01",
    "end_date": "2023-08-12",
    "currency": "USD",
    "market": "US",
    "commission": 0.0001
  }
}
```

## Testing with Upstash Redis Console

To send test messages via Upstash Redis web console:

1. Navigate to Upstash Console
2. Use RPUSH command with queue name:

```redis
RPUSH invest-strategy-service-ingestion-queue '{
  "job_id": "12345678-1234-5678-1234-567890abcdef",
  "timestamp": "2023-08-12T10:00:00Z",
  "action": "create_or_update",
  "portfolio_config": {
    "name": "美股1号",
    "code": "myinvestpilot_us_1",
    "description": "基于特定止损与均线策略的美股杠杆ETF组合",
    "strategy": {
      "name": "ChandelierExitMAStrategy",
      "params": {
        "n_atr": 60,
        "atr_multiplier": 4,
        "n_ma": 250
      }
    },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": {
        "initial_capital": 100000,
        "percents": 20
      }
    },
    "symbols": [
      {"symbol": "FNGU", "name": "FANG+ Index 3x Bull"},
      {"symbol": "TQQQ", "name": "Nasdaq 3x Bull"},
      {"symbol": "TECL", "name": "S&P Technology 3x Bull"},
      {"symbol": "SOXL", "name": "Semiconductor 3x Bull"},
      {"symbol": "TNA", "name": "Russell 2000 3x Bull"},
      {"symbol": "CONL", "name": "COIN 2x Bull"},
      {"symbol": "LABU", "name": "S&P Biotech 3x Bull"},
      {"symbol": "FAS", "name": "Financial Sector 3x Bull"},
      {"symbol": "EDC", "name": "Emerging Markets 3x Bull"},
      {"symbol": "TMF", "name": "Long-term Treasury 3x Bull"}
    ],
    "start_date": "2018-01-01",
    "end_date": "2024-12-07",
    "currency": "USD",
    "market": "US",
    "commission": 0.0001
  }
}'
