# 架构设计文档

本目录包含 investStrategyService 系统的架构设计文档，记录了系统架构的演进历程。

## 文档版本

- [架构设计 v2](./v2.md) - 系统架构设计文档 v2 版本
- [架构设计 v3](./v3.md) - 系统架构设计文档 v3 版本
- [架构设计 v4](./v4.md) - 系统架构设计文档 v4 版本（包含原语引擎）

## 版本说明

### V2 架构

V2 架构是系统的基础架构，主要包含：
- 传统策略系统
- 资金策略系统
- 基本的数据加载和回测功能

### V3 架构

V3 架构引入了服务器模式和消息队列：
- 增加了 EventPoller 组件
- 支持通过消息队列接收任务
- 改进了任务调度和执行流程

### V4 架构

V4 架构是当前最新的架构，主要增加了原语引擎系统：
- 引入了 ComponentRegistry 和 SignalEvaluator
- 支持声明式策略定义
- 增加了市场指标管理器
- 完善了原语组件的组合和评估机制

## 架构图使用指南

架构文档中的图表使用 Mermaid 语法编写，可以在支持 Mermaid 的 Markdown 查看器中直接渲染。如果您的查看器不支持 Mermaid，可以使用以下方法查看图表：

1. 使用 [Mermaid Live Editor](https://mermaid.live/)
2. 将文档中的 Mermaid 代码复制到编辑器中
3. 查看生成的图表

## 贡献指南

如果您想对架构设计文档进行贡献，请遵循以下原则：

1. 保持向后兼容性，不要删除已有的架构信息
2. 添加新的架构版本时，创建新的文档文件
3. 确保图表清晰易懂，并提供足够的文字说明
4. 更新 README.md 和主文档中心的引用
