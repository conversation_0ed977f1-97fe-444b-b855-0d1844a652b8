# Architecture

## Overview

The architecture of the system is based on the following components:

```mermaid
graph TD
    M[Main] --> PF[Portfolio Factory]
    M --> DL[Data Loader]
    M --> TR[Task Runner]
    M --> EP[Event Poller]
    M --> JE[Job Executor]
    
    PF --> PM
    TR --> PM
    DL --> PM
    
    subgraph PM [Portfolio Manager]
        PM1[Portfolio Manager Core]
        PM1 --> PDC[Portfolio Data Collector]
        PM1 --> PTSC[Portfolio Trade Signal Collector]
        PM1 --> CS[Capital Strategy]
        PM1 --> TS[Trade Strategy]
        PM1 --> SQ[(SQLite)]
    end
    
    DL <--> R[(Redis)]
    
    M <--> S[Slack]
    K[Kafka] --> EP
    JE --> K
    
    EP --> TR
    
    PF -.-> |Loads config| CF[Config File]
    
    subgraph External Dependencies
        R
        S
        K
        CF
    end
```

Sequence Diagrams(Server Mode)

```mermaid
sequenceDiagram
    participant M as Main
    participant PF as Portfolio Factory
    participant JE as Job Executor
    participant K as Kafka
    participant EP as Event Poller
    participant TR as Task Runner
    participant DL as Data Loader
    participant PM as Portfolio Manager
    participant PDC as Portfolio Data Collector
    participant PTSC as Portfolio Trade Signal Collector
    participant S as Slack

    M->>PF: Initialize (load config)
    M->>DL: Initialize
    M->>TR: Initialize (with PF, Slack)
    M->>EP: Initialize (with TR)
    M->>JE: Initialize (with PF configs)

    JE->>K: Send update task message
    K->>EP: Receive message
    EP->>TR: Handle message
    TR->>PF: Get portfolio
    PF->>DL: Get instance
    PF->>PM: Create
    TR->>PM: Execute trades
    PM->>DL: Request data
    DL->>PM: Return data
    PM->>PTSC: Generate trade signals
    PM->>PM: Execute backtest
    PM->>PDC: Collect result data

    alt Error occurs
        TR->>S: Send error notification
    else Successful execution
        TR->>S: Send success notification
    end

    M->>S: Receive Slack command
    M->>JE: Trigger update task
```
