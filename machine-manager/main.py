#!/usr/bin/env python3
import os
import time
import signal
from utils.logging_config import get_logger
from redis_monitor import RedisQueueMonitor
from worker_tracker import WorkerTracker
from machine_manager import FlyMachineManager

# Initialize module logger
logger = get_logger("machine_manager.service")

class MachineManagerService:
    def __init__(self):
        # Load configuration from environment
        self.worker_app_name = os.environ.get('WORKER_APP_NAME', 'invest-strategy-service')
        self.queue_name = os.environ.get('QUEUE_NAME', 'invest-strategy-service-ingestion-queue')
        self.max_machines = int(os.environ.get('MAX_MACHINES', '4'))
        self.min_idle = int(os.environ.get('MIN_IDLE_MACHINES', '1'))
        self.check_interval = int(os.environ.get('CHECK_INTERVAL', '60'))
        self.heartbeat_timeout = int(os.environ.get('HEARTBEAT_TIMEOUT', '120'))
        
        # Initialize components
        self.worker_tracker = WorkerTracker(heartbeat_timeout=self.heartbeat_timeout)
        self.queue_monitor = RedisQueueMonitor(self.queue_name)
        self.machine_manager = FlyMachineManager(
            app_name=self.worker_app_name,
            max_machines=self.max_machines,
            min_idle=self.min_idle,
            worker_tracker=self.worker_tracker
        )
        
        # Control flags
        self.running = False
        self.last_check_time = 0
    
    def start(self):
        """Start the machine manager service"""
        logger.info("Starting Machine Manager Service", 
                   app_name=self.worker_app_name,
                   queue_name=self.queue_name,
                   max_machines=self.max_machines,
                   min_idle=self.min_idle)
        
        self.running = True
        self._setup_signal_handlers()
        
        try:
            self._run_service_loop()
        except Exception as e:
            logger.error("Service error", error=str(e))
            self.shutdown()
    
    def _setup_signal_handlers(self):
        """Setup handlers for system signals"""
        def handle_signal(signum, frame):
            logger.info(f"Received signal {signum}")
            self.shutdown()
        
        signal.signal(signal.SIGINT, handle_signal)
        signal.signal(signal.SIGTERM, handle_signal)
    
    def shutdown(self):
        """Gracefully shutdown the service"""
        logger.info("Shutting down Machine Manager Service")
        self.running = False
    
    def _check_and_scale(self) -> bool:
        """
        Check current state and scale machines accordingly
        
        Returns:
            bool: True if check completed successfully
        """
        try:
            # Clean up stale workers first
            cleaned = self.worker_tracker.cleanup_stale_workers()
            if cleaned:
                logger.info("Cleaned up stale workers", count=len(cleaned))
            
            # Get current queue size
            queue_size = self.queue_monitor.get_queue_size()
            
            # Get current queue size and scale machines
            scaling_results = self.machine_manager.scale_to_workload(queue_size)
            
            # Log status
            logger.info(
                "System status",
                queue_size=queue_size,
                worker_counts=self.queue_monitor.get_worker_counts(),
                scaling_actions=scaling_results
            )
            
            return True
            
        except Exception as e:
            logger.error("Error in check and scale", error=str(e))
            return False
    
    def _run_service_loop(self):
        """Main service loop"""
        while self.running:
            current_time = time.time()
            
            # Only check if enough time has passed
            if current_time - self.last_check_time >= self.check_interval:
                if self._check_and_scale():
                    self.last_check_time = current_time
            
            # Sleep for a short time to prevent busy waiting
            time.sleep(min(5, self.check_interval / 4))

def main():
    # Create and start service
    service = MachineManagerService()
    service.start()

if __name__ == "__main__":
    main()
