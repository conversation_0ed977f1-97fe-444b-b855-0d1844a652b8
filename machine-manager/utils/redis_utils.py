import os
from typing import Optional
import redis
from .logging_config import get_logger

# Initialize module logger
logger = get_logger("machine_manager.redis_utils")

class RedisClient:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'client'):
            redis_url = os.environ.get('REDIS_URL')
            if not redis_url:
                raise ValueError("REDIS_URL environment variable is required")
            
            try:
                self.client = redis.from_url(redis_url) # must be rediss:// for TLS
                logger.info("Redis connection established")
            except redis.RedisError as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
    
    def get_connection(self) -> redis.Redis:
        """Get the Redis connection instance"""
        return self.client
    
    def key_exists(self, key: str) -> bool:
        """Check if a key exists in Redis"""
        try:
            return bool(self.client.exists(key))
        except redis.RedisError as e:
            logger.error(f"Redis error checking key existence: {e}")
            return False
    
    def set_value(self, key: str, value: str, expiry: Optional[int] = None) -> bool:
        """Set a key-value pair in Redis with optional expiry in seconds"""
        try:
            self.client.set(key, value)
            if expiry:
                self.client.expire(key, expiry)
            return True
        except redis.RedisError as e:
            logger.error(f"Redis error setting value: {e}")
            return False
    
    def get_value(self, key: str) -> Optional[str]:
        """Get value for a key from Redis"""
        try:
            value = self.client.get(key)
            return value.decode('utf-8') if value else None
        except redis.RedisError as e:
            logger.error(f"Redis error getting value: {e}")
            return None
    
    def delete_key(self, key: str) -> bool:
        """Delete a key from Redis"""
        try:
            return bool(self.client.delete(key))
        except redis.RedisError as e:
            logger.error(f"Redis error deleting key: {e}")
            return False
    
    def get_list_length(self, key: str) -> int:
        """Get the length of a Redis list"""
        try:
            return self.client.llen(key)
        except redis.RedisError as e:
            logger.error(f"Redis error getting list length: {e}")
            return 0
    
    def get_keys_by_pattern(self, pattern: str) -> list:
        """Get all keys matching a pattern"""
        try:
            keys = self.client.keys(pattern)
            return [key.decode('utf-8') for key in keys]
        except redis.RedisError as e:
            logger.error(f"Redis error getting keys by pattern: {e}")
            return []
    
    def set_multiple_values(self, mapping: dict, expiry: Optional[int] = None) -> bool:
        """Set multiple key-value pairs in Redis with optional expiry"""
        try:
            pipeline = self.client.pipeline()
            pipeline.mset(mapping)
            if expiry:
                for key in mapping.keys():
                    pipeline.expire(key, expiry)
            pipeline.execute()
            return True
        except redis.RedisError as e:
            logger.error(f"Redis error setting multiple values: {e}")
            return False
