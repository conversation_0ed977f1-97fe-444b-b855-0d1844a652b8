import logging
import structlog
from pythonjsonlogger import jsonlogger

def setup_logging(module_name=None):
    """
    Configure structured logging with JSON format
    
    Args:
        module_name (str): Optional module name for the logger
    """
    # Set up standard logging
    logger = logging.getLogger(module_name) if module_name else logging.getLogger()
    handler = logging.StreamHandler()
    
    # Create JSON formatter
    formatter = jsonlogger.JsonFormatter(
        fmt='%(asctime)s %(levelname)s %(name)s %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)
    
    # Set log level and add handler
    logger.setLevel(logging.INFO)
    logger.addHandler(handler)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.stdlib.render_to_log_kwargs,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger()

def get_logger(module_name):
    """
    Get a logger instance with the specified module name
    
    Args:
        module_name (str): Name of the module requesting the logger
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(module_name)
