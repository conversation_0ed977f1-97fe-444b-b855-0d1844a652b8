from typing import List, Optional, Dict
import json
from utils.redis_utils import RedisClient
from utils.logging_config import get_logger

# Initialize module logger
logger = get_logger("machine_manager.redis_monitor")

class RedisQueueMonitor:
    def __init__(self, queue_name: str):
        """
        Initialize the Redis Queue Monitor
        
        Args:
            queue_name (str): Name of the main queue to monitor
        """
        self.redis = RedisClient()
        self.queue_name = queue_name
    
    def get_queue_size(self) -> int:
        """Get the current size of the monitored queue"""
        return self.redis.get_list_length(self.queue_name)
    
    def peek_queue(self, count: int = 5) -> List[dict]:
        """
        Peek at the first N items in the queue without removing them
        
        Args:
            count (int): Number of items to peek at
            
        Returns:
            List[dict]: List of queue items as dictionaries
        """
        try:
            client = self.redis.get_connection()
            items = client.lrange(self.queue_name, 0, count - 1)
            return [json.loads(item) for item in items]
        except Exception as e:
            logger.error(f"Error peeking queue: {e}")
            return []
    
    def get_worker_counts(self) -> Dict[str, int]:
        """
        Get counts of active and idle workers
        
        Returns:
            Dict with worker counts:
            - total: Total number of registered workers
            - active: Number of workers processing tasks
            - idle: Number of workers waiting for tasks
        """
        try:
            worker_keys = self.redis.get_keys_by_pattern("worker:*")
            heartbeat_keys = [k for k in worker_keys if k.startswith("worker:heartbeat:")]
            task_keys = [k for k in worker_keys if k.startswith("worker:task:")]
            
            return {
                'total': len(set(k.split(':')[-1] for k in heartbeat_keys)),
                'active': len(set(k.split(':')[-1] for k in task_keys)),
                'idle': len(set(k.split(':')[-1] for k in heartbeat_keys)) - 
                       len(set(k.split(':')[-1] for k in task_keys))
            }
        except Exception as e:
            logger.error(f"Error getting worker counts: {e}")
            return {'total': 0, 'active': 0, 'idle': 0}
