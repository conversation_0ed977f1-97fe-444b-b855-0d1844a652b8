import os
import time
from typing import Dict, List, Optional
import requests
from utils.logging_config import get_logger
from worker_tracker import WorkerTracker

# Initialize module logger
logger = get_logger("machine_manager.manager")

class FlyMachineManager:
    def __init__(
        self,
        app_name: str,
        max_machines: int = 4,
        min_idle: int = 1,
        worker_tracker: Optional[WorkerTracker] = None
    ):
        """
        Initialize the Fly Machine Manager
        
        Args:
            app_name (str): Name of the Fly.io application
            max_machines (int): Maximum number of machines to run
            min_idle (int): Minimum number of idle machines to maintain
            worker_tracker (Optional[WorkerTracker]): Worker tracking instance
        """
        self.api_token = os.environ.get('FLY_API_TOKEN')
        if not self.api_token:
            raise ValueError("FLY_API_TOKEN environment variable is required")
        
        # Normalize app name to match Fly.io conventions (lowercase, no spaces)
        self.app_name = app_name.lower().replace(" ", "-")
        self.max_machines = max_machines
        self.min_idle = min_idle
        self.worker_tracker = worker_tracker
        self.base_url = "https://api.machines.dev/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
    
    def list_machines(self) -> List[Dict]:
        """
        Get list of all machines for the app
        
        Returns:
            List[Dict]: List of machine information dictionaries
        """
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if not isinstance(data, list):
                logger.error("Unexpected API response: expected array of machines")
                return []
                
            # Validate each machine object has required fields
            validated_machines = []
            for machine in data:
                if not isinstance(machine, dict):
                    logger.error("Invalid machine data: not a dictionary")
                    continue
                    
                if "id" not in machine or "state" not in machine:
                    logger.error(f"Invalid machine data: missing required fields")
                    continue
                    
                validated_machines.append(machine)
                
            return validated_machines
        except Exception as e:
            logger.error(f"Error listing machines: {e}")
            return []
    
    def get_machine(self, machine_id: str) -> Optional[Dict]:
        """Get information for a specific machine"""
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines/{machine_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting machine {machine_id}: {e}")
            return None
    
    def start_machine(self, machine_id: str) -> bool:
        """Start a stopped machine"""
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines/{machine_id}/start"
            response = requests.post(url, headers=self.headers)
            response.raise_for_status()
            
            # Wait for machine to be ready
            for _ in range(30):  # 30 second timeout
                status = self.get_machine(machine_id)
                if status and status.get("state") == "started":
                    logger.info(f"Machine {machine_id} started successfully")
                    return True
                time.sleep(1)
            
            logger.error(f"Timeout waiting for machine {machine_id} to start")
            return False
            
        except Exception as e:
            logger.error(f"Error starting machine {machine_id}: {e}")
            return False
    
    def stop_machine(self, machine_id: str) -> bool:
        """Stop a running machine"""
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines/{machine_id}/stop"
            response = requests.post(url, headers=self.headers)
            response.raise_for_status()
            
            logger.info(f"Machine {machine_id} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping machine {machine_id}: {e}")
            return False
    
    def create_machine(self, config: Optional[Dict] = None) -> Optional[str]:
        """
        Create a new machine with optional configuration
        
        Args:
            config (Optional[Dict]): Machine configuration overrides
            
        Returns:
            Optional[str]: ID of created machine, or None if creation failed
        """
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines"
            
            # Get image tag from existing machines
            machines = self.list_machines()
            image_tag = None
            for machine in machines:
                if machine.get("image_ref", {}).get("tag"):
                    image_tag = machine["image_ref"]["tag"]
                    break
            
            if not image_tag:
                logger.error("Could not find image tag from existing machines")
                return None

            default_config = {
                "config": {
                    "name": self.app_name,
                    "image": f"registry.fly.io/{self.app_name}:{image_tag}",
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 2,
                        "memory_mb": 512
                    },
                    "env": {
                        "ENABLE_KAFKA_POLLER": "false"
                    }
                }
            }
            logger.info(f"Creating machine with config: {default_config}")
            logger.info(f"URL: {url}")
            
            # Merge with provided config if any
            if config:
                default_config["config"].update(config)
            
            response = requests.post(url, headers=self.headers, json=default_config)
            response.raise_for_status()
            
            data = response.json()
            if "id" not in data:
                logger.error("Unexpected API response: 'id' key not found")
                return None
            
            machine_id = data["id"]
            logger.info(f"Created machine {machine_id}")
            
            if self.worker_tracker:
                self.worker_tracker.register_machine(machine_id)
            
            return machine_id
            
        except Exception as e:
            logger.error(f"Error creating machine: {e}")
            return None
    
    def delete_machine(self, machine_id: str) -> bool:
        """Delete a machine (use with caution)"""
        try:
            url = f"{self.base_url}/apps/{self.app_name}/machines/{machine_id}"
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()
            
            logger.info(f"Deleted machine {machine_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting machine {machine_id}: {e}")
            return False
    
    def scale_to_workload(self, queue_size: int) -> Dict[str, int]:
        """
        Adjust number of running machines based on queue size
        
        Args:
            queue_size (int): Current size of the task queue
            
        Returns:
            Dict[str, int]: Summary of scaling actions taken
        """
        results = {'started': 0, 'stopped': 0, 'created': 0}
        
        try:
            machines = self.list_machines()
            running = [m for m in machines if isinstance(m, dict) and m.get("state") == "started"]
            stopped = [m for m in machines if isinstance(m, dict) and m.get("state") == "stopped"]
            
            # Calculate needed machines based on queue size:
            # - If queue size = 0, use min_idle machines
            # - If queue size > max_machines * 10, use max_machines
            # - Otherwise (queue_size > 0), use 1 machine
            needed = self.min_idle if queue_size == 0 else (self.max_machines if queue_size > self.max_machines * 10 else 1)
            current = len(running)
            
            logger.info(
                f"Scaling - Queue: {queue_size}, "
                f"Current: {current}, "
                f"Needed: {needed}, "
                f"Stopped: {len(stopped)}"
            )
            
            # Scale up if needed
            if current < needed:
                # Start stopped machines first
                for machine in stopped[:needed - current]:
                    if not isinstance(machine, dict) or "id" not in machine:
                        logger.error("Invalid machine data: missing 'id' field")
                        continue
                    if self.start_machine(machine["id"]):
                        results['started'] += 1
                        current += 1
                
                # Create new machines if necessary
                while current < needed:
                    if self.create_machine():
                        results['created'] += 1
                        current += 1
            
            # Scale down if needed
            elif current > needed:
                # Stop excess machines
                for machine in running[needed:]:
                    if not isinstance(machine, dict) or "id" not in machine:
                        logger.error("Invalid machine data: missing 'id' field")
                        continue
                    if self.stop_machine(machine["id"]):
                        results['stopped'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error scaling machines: {e}")
            return results
