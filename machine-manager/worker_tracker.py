import time
from typing import Dict, List, Optional
import json
from utils.redis_utils import RedisClient
from utils.logging_config import get_logger

# Initialize module logger
logger = get_logger("machine_manager.worker_tracker")

class WorkerTracker:
    def __init__(self, heartbeat_timeout: int = 60):
        """
        Initialize the Worker Tracker
        
        Args:
            heartbeat_timeout (int): Time in seconds after which a worker is considered dead
        """
        self.redis = RedisClient()
        self.heartbeat_timeout = heartbeat_timeout
        self.heartbeat_key_prefix = "worker:heartbeat:"
        self.task_key_prefix = "worker:task:"
    
    def register_machine(self, machine_id: str) -> bool:
        """
        Register a new worker machine
        
        Args:
            machine_id (str): Unique identifier for the worker machine
            
        Returns:
            bool: True if registration was successful
        """
        try:
            heartbeat_key = f"{self.heartbeat_key_prefix}{machine_id}"
            self.redis.set_value(heartbeat_key, "ONLINE", self.heartbeat_timeout * 2)
            logger.info(f"Registered machine {machine_id}")
            return True
        except Exception as e:
            logger.error(f"Error registering machine {machine_id}: {e}")
            return False
    
    def update_heartbeat(self, machine_id: str) -> bool:
        """Update worker machine heartbeat"""
        try:
            heartbeat_key = f"{self.heartbeat_key_prefix}{machine_id}"
            return self.redis.set_value(heartbeat_key, "ONLINE", self.heartbeat_timeout * 2)
        except Exception as e:
            logger.error(f"Error updating heartbeat for machine {machine_id}: {e}")
            return False
    
    def update_machine_status(self, machine_id: str, task_id: Optional[str] = None) -> bool:
        """
        Update worker machine status with current task
        
        Args:
            machine_id (str): Machine identifier
            task_id (Optional[str]): Current task ID, or None if machine is idle
        """
        try:
            task_key = f"{self.task_key_prefix}{machine_id}"
            
            if task_id:
                return self.redis.set_value(task_key, task_id)
            else:
                return self.redis.delete_key(task_key)
                
        except Exception as e:
            logger.error(f"Error updating status for machine {machine_id}: {e}")
            return False
    
    def get_machine_status(self, machine_id: str) -> Dict:
        """
        Get current status of a worker machine
        
        Returns:
            Dict containing:
            - status: OFFLINE, IDLE, or BUSY
            - current_task: Task ID if busy, None otherwise
        """
        try:
            heartbeat_key = f"{self.heartbeat_key_prefix}{machine_id}"
            task_key = f"{self.task_key_prefix}{machine_id}"
            
            # Get values in pipeline
            pipeline = self.redis.get_connection().pipeline()
            pipeline.get(heartbeat_key)
            pipeline.get(task_key)
            heartbeat, task = pipeline.execute()
            
            if not heartbeat:
                return {'status': 'OFFLINE', 'current_task': None}
            
            status = 'BUSY' if task else 'IDLE'
            current_task = task.decode('utf-8') if task else None
            
            return {
                'status': status,
                'current_task': current_task
            }
            
        except Exception as e:
            logger.error(f"Error getting status for machine {machine_id}: {e}")
            return {'status': 'ERROR', 'current_task': None}
    
    def is_machine_idle(self, machine_id: str) -> bool:
        """Check if a machine is idle (online but not processing tasks)"""
        status = self.get_machine_status(machine_id)
        return status['status'] == 'IDLE'
    
    def cleanup_stale_workers(self) -> List[str]:
        """
        Clean up data for workers that haven't sent heartbeats
        
        Returns:
            List[str]: List of machine IDs that were cleaned up
        """
        cleaned_workers = []
        try:
            worker_keys = self.redis.get_keys_by_pattern(f"{self.heartbeat_key_prefix}*")
            
            for key in worker_keys:
                machine_id = key.split(':')[-1]
                ttl = self.redis.get_connection().ttl(key)
                
                if ttl < 0 or ttl < self.heartbeat_timeout / 2:
                    # Clean up worker data
                    pipeline = self.redis.get_connection().pipeline()
                    pipeline.delete(f"{self.heartbeat_key_prefix}{machine_id}")
                    pipeline.delete(f"{self.task_key_prefix}{machine_id}")
                    pipeline.execute()
                    
                    cleaned_workers.append(machine_id)
                    logger.warn(f"Cleaned up stale worker: {machine_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up stale workers: {e}")
        
        return cleaned_workers
    
    def get_all_workers(self) -> List[Dict]:
        """
        Get status information for all registered workers
        
        Returns:
            List[Dict]: List of worker status dictionaries with machine_id and status info
        """
        workers = []
        try:
            worker_keys = self.redis.get_keys_by_pattern(f"{self.heartbeat_key_prefix}*")
            
            for key in worker_keys:
                machine_id = key.split(':')[-1]
                status = self.get_machine_status(machine_id)
                status['machine_id'] = machine_id
                workers.append(status)
                
        except Exception as e:
            logger.error(f"Error getting all workers: {e}")
        
        return workers
