FROM python:3.10-slim-buster

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set executable permissions for the entry point
RUN chmod +x main.py

# Run as non-root user for better security
RUN useradd -m appuser && chown -R appuser:appuser /app
USER appuser

# Command to run the service
CMD ["./main.py"]
