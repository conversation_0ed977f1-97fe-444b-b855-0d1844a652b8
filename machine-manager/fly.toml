# fly.toml for machine-manager service
app = "machine-manager"
primary_region = "sin"

[build]
  dockerfile = "Dockerfile"

[env]
  PORT = "8080"
  WORKER_APP_NAME = "invest-strategy-service"
  QUEUE_NAME = "invest-strategy-service-ingestion-queue"
  MAX_MACHINES = "4"
  MIN_IDLE_MACHINES = "0"
  CHECK_INTERVAL = "120"
  HEARTBEAT_TIMEOUT = "120"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256
