# Machine Manager Service

A dynamic task scheduler for managing Fly Machines in the investStrategyService application.

## Overview

The Machine Manager Service automatically scales Fly.io machines based on Redis queue workload. It monitors queue length and worker status to optimize resource utilization and cost efficiency.

## Features

- Dynamic scaling based on queue size
- Worker health monitoring via heartbeats
- Automatic cleanup of stale workers
- Comprehensive metrics collection
- Cost-efficient machine management
- Graceful shutdown handling

## Prerequisites

- Python 3.10+
- Redis instance
- Fly.io account with API access
- Environment variables configured

## Configuration

Set the following environment variables:

```bash
# Required
FLY_API_TOKEN=your_fly_api_token
REDIS_URL=redis://username:password@host:port

# Optional (shown with defaults)
WORKER_APP_NAME=invest-strategy-service
QUEUE_NAME=invest-strategy-service-ingestion-queue
MAX_MACHINES=4
MIN_IDLE_MACHINES=0
CHECK_INTERVAL=60
HEARTBEAT_TIMEOUT=120
```

## Deployment

1. Create the Fly.io application:
   ```bash
   fly apps create machine-manager
   ```

2. Set required secrets:
   ```bash
   fly secrets set \
     FLY_API_TOKEN=your_fly_api_token \
     REDIS_URL=redis://username:password@host:port \
     -a machine-manager
   ```

3. Deploy:
   ```bash
   fly deploy
   ```

## Development

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/MacOS
   # or
   .\venv\Scripts\activate  # Windows
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables in `.env`:
   ```env
   FLY_API_TOKEN=your_token
   REDIS_URL=your_redis_url
   ```

4. Run locally:
   ```bash
   python main.py
   ```

## Monitoring

View service logs:
```bash
fly logs -a machine-manager
```

Monitor machine status:
```bash
fly status -a machine-manager
```

## Architecture

The service consists of several key components:

- **Machine Manager**: Handles Fly.io machine lifecycle
- **Redis Monitor**: Tracks queue metrics
- **Worker Tracker**: Monitors worker health
- **Main Service**: Coordinates components and implements control loop

## License

MIT
